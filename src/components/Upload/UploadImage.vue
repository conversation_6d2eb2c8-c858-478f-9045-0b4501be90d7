<template>
  <div class="upload-image">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :data="uploadData"
      :file-list="fileList"
      :list-type="listType"
      :multiple="multiple"
      :limit="limit"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
    >
      <i v-if="listType === 'picture-card'" class="el-icon-plus"></i>
      <el-button v-else size="small" type="primary">
        <i class="el-icon-upload el-icon--right"></i>
        选择图片
      </el-button>
      <div v-if="listType !== 'picture-card'" slot="tip" class="el-upload__tip">
        只能上传jpg/png文件，且不超过{{ maxSize }}MB
      </div>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" title="图片预览" width="800px" append-to-body>
      <img :src="previewUrl" style="width: 100%" alt="预览图片" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  name: 'UploadImage',
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    limit: {
      type: Number,
      default: 1,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    listType: {
      type: String,
      default: 'text',
    },
    maxSize: {
      type: Number,
      default: 5, // MB
    },
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/api/admin/file/upload/image',
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      uploadData: {
        type: 'image',
      },
      fileList: [],
      previewVisible: false,
      previewUrl: '',
    };
  },
  watch: {
    value: {
      handler(val) {
        this.initFileList(val);
      },
      immediate: true,
    },
  },
  methods: {
    /** 初始化文件列表 */
    initFileList(value) {
      if (!value) {
        this.fileList = [];
        return;
      }

      if (this.multiple) {
        // 多图上传
        try {
          const urls = Array.isArray(value) ? value : JSON.parse(value);
          this.fileList = urls.map((url, index) => ({
            uid: index,
            name: `image-${index}`,
            status: 'done',
            url: url,
          }));
        } catch (e) {
          this.fileList = [];
        }
      } else {
        // 单图上传
        if (typeof value === 'string' && value) {
          this.fileList = [
            {
              uid: 1,
              name: 'image',
              status: 'done',
              url: value,
            },
          ];
        } else {
          this.fileList = [];
        }
      }
    },

    /** 上传前检查 */
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt5M = file.size / 1024 / 1024 < this.maxSize;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error(`上传图片大小不能超过 ${this.maxSize}MB!`);
        return false;
      }
      return true;
    },

    /** 上传成功 */
    handleSuccess(response, file, fileList) {
      if (response.status === 200) {
        const imageUrl = response.data.url;
        this.updateValue(fileList, imageUrl);
        this.$message.success('上传成功');
      } else {
        this.$message.error(response.message || '上传失败');
        this.handleError();
      }
    },

    /** 上传失败 */
    handleError() {
      this.$message.error('上传失败');
    },

    /** 移除文件 */
    handleRemove(file, fileList) {
      this.updateValue(fileList);
    },

    /** 超出限制 */
    handleExceed() {
      this.$message.warning(`最多只能上传 ${this.limit} 张图片`);
    },

    /** 预览图片 */
    handlePreview(file) {
      this.previewUrl = file.url;
      this.previewVisible = true;
    },

    /** 更新值 */
    updateValue(fileList, newUrl = null) {
      if (this.multiple) {
        // 多图上传
        const urls = fileList
          .filter(file => file.status === 'done')
          .map(file => file.url || file.response?.data?.url)
          .filter(url => url);

        this.$emit('input', JSON.stringify(urls));
      } else {
        // 单图上传
        if (fileList.length > 0 && fileList[0].status === 'done') {
          const url = newUrl || fileList[0].url || fileList[0].response?.data?.url;
          this.$emit('input', url || '');
        } else {
          this.$emit('input', '');
        }
      }
    },
  },
};
</script>

<style scoped>
.upload-image {
  width: 100%;
}

.el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
