import request from "@/nxTemp/request/ajax.js";

// 获取字典列表
export function getDictList(dictCode) {
	return request({
		url: `/api/front/foundation/sys/dict/getDictItems/${dictCode}`,
		method: 'GET',
	})
}
// 获取首页数据-公告，预警
export function getIndexInfo() {
	return request({
		url: '/api/front/business/wx/homeDetail',
		method: 'GET',
	})
}
// 微信登录小程序授权登录
export function getWechatLogin(data) {
	return request({
		url: '/api/front/login/wechat/routine',
		method: 'POST',
		data
	})
}
// 微信注册绑定手机号
export function getWechatRegisterBindingPhone(data) {
	return request({
		url: '/api/front/login/wechat/register/binding/phone',
		method: 'POST',
		data
	})
}

// 获取所有的图片数据
export function getAllImgList() {
	return request({
		url: '/api/front/foundation/sys/img/getInfo',
		method: 'GET',
	})
}

// 获取通知公告
export function getNoticeList() {
	return request({
		url: '/api/front/business/notice/list',
		method: 'GET',
		params: {
			page: 1,
			limit: 1000
		}
	})
}

// 获取攻略列表
export function getBusinessList(params) {
	return request({
		url: '/api/front/business/guide/list',
		method: 'GET',
		params
	})
}
// 获取攻略详情
export function getBusinessInfo(params) {
	return request({
		url: '/api/front/business/guide/info',
		method: 'GET',
		params
	})
}
// 获取资讯列表
export function getNewsList(params) {
	return request({
		url: '/api/front/business/information/list',
		method: 'GET',
		params
	})
}
// 获取资讯详情
export function getNewsInfo(params) {
	return request({
		url: '/api/front/business/information/info',
		method: 'GET',
		params
	})
}
// 获取小管家数据
export function housekeeper() {
	return request({
		url: '/api/front/foundation/housekeeper/mgmt/info',
		method: 'GET',
	})
}

// 新增投诉建议
export function postComplaintsSave(data) {
	return request({
		url: '/api/front/business/complaints/save',
		method: 'POST',
		data
	})
}
// 修改投诉建议回复
export function postComplaintReply(data) {
	return request({
		url: `/api/front/business/complaints/update?id=${data.id}`,
		method: 'POST',
		data
	})
}

// 获取投诉建议列表
export function getComplaintsList(params) {
	return request({
		url: '/api/front/business/complaints/list',
		method: 'GET',
		params
	})
}
// 获取应急救援管理数据
export function getEmergency() {
	return request({
		url: '/api/front/business/emergency/info',
		method: 'GET',
	})
}

// 获取预警管理列表
export function getWarningList() {
	return request({
		url: '/api/front/business/warning/list',
		method: 'GET',
		params: {
			limit: 1000,
			page: 1
		}
	})
}
// 获取用户服务协议
export function getAgreement() {
	return request({
		url: '/api/front/agreement/userinfo',
		method: 'GET',
	})
}
// 问卷调查模板查询
export function getQuestionnaire() {
	return request({
		url: '/api/front/business/mode/mgmt/info',
		method: 'GET',
	})
}
// 问卷调查模板是否填写查询
export function getQuestionnaireIsFill(modelId) {
	return request({
		url: '/api/front/business/fill/item/list',
		method: 'GET',
		params: {
			modelId,
			limit: 500,
			page: 1
		}
	})
}
// 提交问卷
export function submitQuestionnaire(data) {
	return request({
		url: '/api/front/business/fill/item/save',
		method: 'POST',
		data
	})
}
// 查询龙宫币全部记录
export function getCoinRecord(data) {
	return request({
		url: '/api/front/business/verifyRecord/allList',
		method: 'GET',
		params: data
	})
}
// 查询龙宫币累计记录
export function getCoinRecordTotal(data) {
	return request({
		url: '/api/front/business/receiveRecord/list',
		method: 'GET',
		params: data
	})
}
// 查询龙宫币核销记录
export function getCoinRecordVerify(data) {
	return request({
		url: '/api/front/business/verifyRecord/list',
		method: 'GET',
		params: data
	})
}
// 查询龙宫币数量
export function getCoinRecordNumber() {
	return request({
		url: '/api/front/business/receiveRecord/number',
		method: 'GET'
	})
}
// 查询龙宫币分配的景点名称
export function getCoinFormName() {
	return request({
		url: '/api/front/business/integral/info',
		method: 'GET'
	})
}
