import request from "@/nxTemp/request/ajax.js";

/**
 * 酒店预订相关API
 */

// 获取酒店列表
export function getHotelList(params) {
    return request({
        url: '/api/front/hotel/list',
        method: 'GET',
        params: params
    })
}

// 获取酒店详情
export function getHotelDetail(hotelId) {
    return request({
        url: `/api/front/hotel/detail/${hotelId}`,
        method: 'GET'
    })
}

// 获取房型列表
export function getRoomList(params) {
    return request({
        url: '/api/front/hotel/room/list',
        method: 'GET',
        params: params
    })
}

// 解析商品名称（调试用）
export function parseProductName(productName) {
    return request({
        url: '/api/front/hotel/parse/product',
        method: 'GET',
        params: {
            productName: productName
        }
    })
}

/**
 * 预订酒店房间
 * @param {Object} data 预订数据
 */
export function bookHotelRoom(data) {
    return request({
        url: '/hotel/book',
        method: 'POST',
        data
    })
}

/**
 * 获取特价房源
 * @param {Object} params 查询参数
 */
export function getSpecialOffers(params = {}) {
    return request({
        url: '/hotel/special-offers',
        method: 'GET',
        params
    })
}

/**
 * 获取周末不加价房源
 * @param {Object} params 查询参数
 */
export function getWeekendDeals(params = {}) {
    return request({
        url: '/hotel/weekend-deals',
        method: 'GET',
        params
    })
}

/**
 * 获取本地房源
 * @param {Object} params 查询参数
 */
export function getLocalHotels(params = {}) {
    return request({
        url: '/hotel/local',
        method: 'GET',
        params
    })
}

/**
 * 创建酒店预订订单
 * @param {Object} data 预订数据
 */
export function createHotelBooking(data) {
    return request({
        url: '/api/front/order/create',
        method: 'POST',
        data
    })
}

/**
 * 获取支付配置
 */
export function getPayConfig() {
    return request({
        url: '/api/front/pay/get/config',
        method: 'GET'
    })
}

/**
 * 订单支付
 * @param {Object} data 支付数据
 */
export function orderPayment(data) {
    return request({
        url: '/api/front/pay/payment',
        method: 'POST',
        data
    })
}

/**
 * 查询订单支付结果
 * @param {string} orderNo 订单号
 */
export function queryPayResult(orderNo) {
    return request({
        url: `/api/front/pay/query/wechat/pay/result/${orderNo}`,
        method: 'GET'
    })
}

/**
 * 获取订单详情
 * @param {string} orderNo 订单号
 */
export function getOrderDetail(orderNo) {
    return request({
        url: `/api/front/order/detail/${orderNo}`,
        method: 'GET'
    })
}

/**
 * 取消订单
 * @param {string} orderNo 订单号
 */
export function cancelOrder(orderNo) {
    return request({
        url: `/api/front/order/cancel/${orderNo}`,
        method: 'POST'
    })
}

