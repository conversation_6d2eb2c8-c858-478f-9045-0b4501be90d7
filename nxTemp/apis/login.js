import request from "@/nxTemp/request/ajax.js";
// 用户账号密码登录
export function postPasswordLogin(data) {
	return request({
		url: '/api/front/login/mobile/password',
		method: 'POST',
		data
	})
}
// 用户验证码登录
export function postCaptchaLogin(data) {
	return request({
		url: '/api/front/login/mobile/captcha',
		method: 'POST',
		data
	})
}

// 用户注册
export function postRegister(data) {
	return request({
		url: '/api/front/login/userRegister',
		method: 'POST',
		data
	})
}
// 用户退出
export function postLogout(data) {
	return request({
		url: '/api/front/login/logout',
		method: 'GET',
	})
}
// 发送验证码
export function postSendCode(data) {
	return request({
		url: '/api/front/login/send/code',
		method: 'POST',
		data
	})
}
// 获取个人信息
export function getUserInfo() {
	return request({
		url: '/api/front/user/info',
		method: 'GET'
	})
}
// 修改个人信息
export function editUserInfo(data) {
	return request({
		url: '/api/front/user/user/edit',
		method: 'POST',
		data
	})
}
// 修改密码
export function changePwd(data) {
	return request({
		url: '/api/front/user/register/reset',
		method: 'POST',
		data
	})
}