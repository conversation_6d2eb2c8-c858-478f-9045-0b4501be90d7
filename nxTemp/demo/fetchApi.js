// 服务端访问地址
const serverName = 'https://cs.jsdjq.com';
// Api server 地址
const serverUrl = `${serverName}/frontApi/api/front`;

function jumpTo(path) {
	// 手绘图里面跳转vr链接，path是自定义跳转事件里要跳转的vr链接，
	// path示例:https://szsp.jsdjq.com/html/?s=16129916，s=后面是具体要跳转的场景id
	// https://szsp.jsdjq.com/html2/?from=xcx&flag=1723704021751&token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiNzUiLCJyb2xlX2lkIjpudWxsLCJuaWNrbmFtZSI6Iua4uOWuoiIsInVzZXJfa2V5IjoiYzc3MDdhZDItZGE5MC00MjVhLWEzZWYtNWZkMjNkMGY3ODBlIiwidXNlcl9waG9uZSI6IjE5NTI4ODk2ODk3IiwidXNlcm5hbWUiOm51bGx9.4vKgqw7GZchqFDYeXDBc57BnAlrwQI33kcEa9-_jj9ja34wBWObkFaLLiACw1wT4S5afaeRk4utbZjHJuzRyqQ

	let url = window.location.search;
	const params = new URLSearchParams(url);
	const tokenValue = params.get('token') || '';
	const fromType = params.get('from') || '';
	let aa = path.split('?')
	const sValue = aa.length == 1 ? false : true
	// console.log(path, '===path', sValue)
	// debugger;
	if (sValue) {
		// 如果有s参数，则
		let href = path + '&token=' + tokenValue + '&from=' + fromType;
		window.location.href = href;
		// console.log(href, '===href')
	} else {
		let href = path + '?token=' + tokenValue + '&from=' + fromType;
		window.location.href = href;
		// console.log(href, '===href')
	}

}

function getLinkUrl(path) {
	// 跳转vr链接，path是自定义跳转事件里要跳转的vr链接，
	// path示例:https://szsp.jsdjq.com/html/?s=16129916，s=后面是具体要跳转的场景id
	// https://szsp.jsdjq.com/html2/?from=xcx&flag=1723704021751&token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoiNzUiLCJyb2xlX2lkIjpudWxsLCJuaWNrbmFtZSI6Iua4uOWuoiIsInVzZXJfa2V5IjoiYzc3MDdhZDItZGE5MC00MjVhLWEzZWYtNWZkMjNkMGY3ODBlIiwidXNlcl9waG9uZSI6IjE5NTI4ODk2ODk3IiwidXNlcm5hbWUiOm51bGx9.4vKgqw7GZchqFDYeXDBc57BnAlrwQI33kcEa9-_jj9ja34wBWObkFaLLiACw1wT4S5afaeRk4utbZjHJuzRyqQ

	let url = window.location.search;
	const params = new URLSearchParams(url);
	const tokenValue = params.get('token') || '';
	const fromType = params.get('from') || '';
	let aa = path.split('?')
	const sValue = aa.length == 1 ? false : true
	let href = ""
	if (sValue) {
		// 如果有s参数，则
		href = path + '&token=' + tokenValue + '&from=' + fromType;
		console.log(href, '===href')
	} else {
		href = path + '?token=' + tokenValue + '&from=' + fromType;
		console.log(href, '===href')
	}
	return href
}
var outLinkUrl = ""; //需要跳转的外部链接



// 简单的提示信息
function showToast(message) {
	// 创建toast的div元素
	const toast = document.createElement('div');
	toast.textContent = message;
	toast.style.position = 'fixed';
	toast.style.top = '50%';
	toast.style.left = '50%';
	toast.style.transform = "translate(-50%, -50%)"
	toast.style.backgroundColor = '#ffffff';
	toast.style.color = '#000';
	toast.style.padding = '10px';
	toast.style.borderRadius = '5px';
	toast.style.opacity = '0';
	toast.style.zIndex = '999999';
	toast.style.transition = 'opacity 0.3s ease, visibility 0.3s ease';

	// 将toast添加到body中
	document.body.appendChild(toast);
	// 通过过渡效果显示toast
	setTimeout(() => {
		toast.style.opacity = '1';
	}, 100);

	// 3秒后隐藏toast并移除
	setTimeout(() => {
		toast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 500);
	}, 3000);
}

// 文章详情弹窗

// contentCategory内容分类(攻略1/商户2)、ifArticle关联内容类型（标识是内部1/外部2）、关联文章ID/外链URL
// ifPopWindow 1 弹窗显示 2 页面跳转
function showDetail(contentCategory, articleId, ifArticle, linkUrl, ifPopWindow) {
	if (articleId && ifArticle == 1) {
		// 内部文章详情弹窗
		let iframeUrl = `${serverName}/h5/#/pages/home/<USER>
		detailDialog(contentCategory, iframeUrl)
	}
	if (ifArticle == 0 && ifPopWindow == 1) {
		// 外部链接弹窗
		detailDialog('', linkUrl)
	}
}
// 商户弹窗
function detailDialog(linkUrl) {
	let iframeUrl = linkUrl;
	const toast = document.createElement('div');
	toast.style.width = '100%';
	toast.style.height = '100%';
	toast.style.position = 'fixed';
	toast.style.bottom = '0';
	toast.style.left = '50%';
	toast.style.transform = "translate(-50%, 0)"
	toast.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
	toast.style.opacity = '0';
	toast.style.zIndex = '999990';
	let str = ``
	str += `<div id="article-box-bg"  style="width:100%;height:88%;margin: 0 auto;border-radius: 20px 20px 0 0;
  background:#fff;position: absolute;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        bottom: 0;
        left: 0;padding:40px 0 0 0;" id="box-box">`;
	str += `<img src="` + serverName + `/staticFile/close1.png" style="width:25px;height:25px;position: absolute;right:10px;top:10px;" id="closeBtnD1"/>`;
	str += `<iframe  frameborder="0" width="100%" height="100%" src=` + iframeUrl + `></iframe>`;
	str += `</div>`
	toast.innerHTML = str;
	// 将toast添加到body中
	document.body.appendChild(toast);
	// 通过过渡效果显示toast
	setTimeout(() => {
		toast.style.opacity = '1';
	}, 100);
	document.getElementById('closeBtnD1').addEventListener('click', function () {
		console.log('点击关闭按钮')
		toast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 500);
	});
}

// 点击热点事件
window.sceneClick = function (id) {
	console.log('hotspotKey1', window.envName)
	window.envName = id;
	console.log('hotspotKey2', window.envName)
};

// 点击热点事件
window.hotspotClick = function (id) {
	getId(id)
};

// 根据热点的key 查询对应的信息
function getId(hotspotKey) {
	console.log('hotspotKey', hotspotKey)
	const params = new URLSearchParams(window.location.search);
	const tokenValue = params.get('token');
	if (hotspotKey && tokenValue) {
		fetch(serverUrl + `/business/receiveRecord/checkHotspot?hotspotKey=${hotspotKey}`, {
			method: 'GET', // 或者 'GET'
			headers: {
				'Content-Type': 'application/json',
				'Authori-zation': tokenValue
			},
			// body: JSON.stringify(data) // post请求携带的参数
			// params: { hotspotKey: hotspotKey } // get请求携带的参数
		})
			.then(response => {
				// 检查响应状态是否为200
				if (response.ok) {
					// 如果响应成功，转换数据为JSON格式
					return response.json();
				} else {
					throw new Error('Network response was not ok.');
				}
			}) // 将响应转换为 JSON
			.then(data => {
				if (data.code === 200) {
					const allData = data.data
					if (allData && allData.hotspotInfo) {
						let hotspotType = allData.hotspotInfo.hotspotType || '' //热点类型
						let operatingEvent = allData.operatingEvent //龙宫币类型
						let hotspotName = allData.hotspotInfo.hotspotName || '' //景点名称
						let hotspotDesc = allData.hotspotInfo.hotspotDesc || '' //景点内容
						let hotspotMerchantId = allData.hotspotInfo.hotspotMerchantId || '' //唯一标识
						let hotspotVrUrl = allData.hotspotInfo.hotspotVrUrl || '' //热点路径

						// 3 可以领取
						// 2 已经领取
						// 1 不可领取
						if (operatingEvent === '3') {
							// 显示可以获取龙宫币
							showCollectToast({
								total: allData.currentNumber || 0,
								score: allData.thisNumber || 0
							}, hotspotVrUrl, tokenValue)
						} else if (operatingEvent === '2') {
							// 显示已经获取了龙宫币
							showCoinToast({}, 2, hotspotVrUrl, tokenValue)
						} else {
							// 景点、遗址
							if (hotspotVrUrl) {
								window.location.href = `${hotspotVrUrl}&token=${tokenValue}`;
							}
						}
						// 景点、遗址 hotspotType == 1
						// 传说故事 hotspotType == 2
						// 品质商家 hotspotType == 3
						// 浏览路线 hotspotType == 4
						// 其他 hotspotType == 5



						// 传说故事
						if (hotspotType == 2) {
							legendlDialog(hotspotName, hotspotDesc)
						}

						// 品质商家
						if (hotspotType == 3) {
							let linkUrl = `${serverName}/h5/#/pages/merchant/merchantDetail?iswx=false&id=${hotspotMerchantId}`
							detailDialog(linkUrl)
						}
					}
				} else {
					// 如果响应不成功，抛出错误
					if (data.message) {
						console.log('抛出错误', data.message);
						if (data.code === 401) {
							showToast('登录状态已过期，请前往个人中心重新登录账号哦。');
						} else {
							showToast(data.message);
						}
					}
				}
			}) // 打印响应数据
			.catch((error) => {
				showToast('服务异常');
				console.error('Error:', error);
			});
	} else if (!tokenValue) {
		showToast('检测到您尚未登录，请前往个人中心登录账号哦。');
	}
}

// 收集龙宫币
function showCollectToast(allData, hotspotVrUrl, tokenValue) {
	// 创建toast的div元素
	const collectToast = document.createElement('div');
	collectToast.style.width = '100%';
	collectToast.style.height = '100%';
	collectToast.style.position = 'fixed';
	collectToast.style.top = '50%';
	collectToast.style.left = '50%';
	collectToast.style.transform = "translate(-50%, -50%)"
	collectToast.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
	collectToast.style.padding = '10px';
	collectToast.style.borderRadius = '5px';
	collectToast.style.opacity = '0';
	collectToast.style.zIndex = '999999';
	collectToast.style.color = '#ffffff';
	collectToast.style.fontSize = '14px';
	collectToast.style.display = 'flex';
	collectToast.style.alignItems = 'center';
	let str = ``
	str += `<div  style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); display: flex; justify-content: center; align-items: center; z-index: 999; flex-direction: column;">`;
	str += `<div style="max-width: 90%;width: 90vw; height: 90vw; display: flex; align-items: center; flex-direction: column; position: relative;">`;
	str += `<img src="${serverName}/staticFile/jb.png" style="width: 43vw; height: 36vw; z-index: 2;">`;
	str += `<div style="width: 80vw; height: 75vw; background-image: url('${serverName}/staticFile/vr-bg.png'); background-size: 100% 100%; position: relative; margin-top: -21vw; z-index: 1; display: flex; flex-direction: column; align-items: center; justify-content: flex-end; padding-bottom: 5vw;">`;
	str += `<div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">`;
	str += `<p style="font-weight: 500; font-size: 4.5vw; color: #9F4900; margin: 0;">恭喜您获得龙宫币</p>`;
	str += `<p style="font-weight: 500; font-size: 4.5vw; color: #9F4900; margin: 2.5vw 0; ">累积龙宫币兑换好礼</p>`;
	str += `<p style="font-weight: 500; font-size: 4.5vw; color: #9F4900; margin: 0;">欢乐无休止</p>`;
	str += `<img src="${serverName}/staticFile/xs.png" style="width: 43vw; height: 4vw; margin: 2vw 0;">`;
	str += `<img src="${serverName}/staticFile/kxsx.png" onclick="closePopup()" style="width: 50vw; height: 15vw; cursor: pointer;" id="closeBtn1">`;
	str += `</div>`;
	str += `</div>`;
	str += `</div>`;
	str += `<img src="${serverName}/staticFile/close.png" onclick="closePopup()" style="width: 10vw; height: 10vw; margin-top: 3vw; cursor: pointer;" id="closeBtn2">`;
	str += `</div>`;


	collectToast.innerHTML = str;
	// 将toast添加到body中
	document.body.appendChild(collectToast);
	// 通过过渡效果显示toast
	setTimeout(() => {
		collectToast.style.opacity = '1';
	}, 100);
	document.getElementById('closeBtn1').addEventListener('click', function () {
		collectToast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(collectToast);
			// 显示
			showCoinToast({
				total: allData.total || 0,
				score: allData.score || 0
			}, 1, hotspotVrUrl, tokenValue)

		}, 500);
	});
	document.getElementById('closeBtn2').addEventListener('click', function () {
		collectToast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(collectToast);
			if (hotspotVrUrl) {
				window.location.href = `${hotspotVrUrl}&token=${tokenValue}`;
			}
		}, 500);
	});
}

// 打开龙宫币获取
function showCoinToast(data, type, hotspotVrUrl, tokenValue) {
	// 创建toast的div元素
	const toast = document.createElement('div');
	toast.style.width = '100%';
	toast.style.height = '100%';
	toast.style.position = 'fixed';
	toast.style.top = '50%';
	toast.style.left = '50%';
	toast.style.transform = "translate(-50%, -50%)"
	toast.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
	toast.style.padding = '10px';
	toast.style.borderRadius = '5px';
	toast.style.opacity = '0';
	toast.style.zIndex = '999999';
	toast.style.color = '#ffffff';
	toast.style.fontSize = '14px';
	toast.style.display = 'flex';
	toast.style.alignItems = 'center';
	toast.style.transition = 'opacity 0.3s ease, visibility 0.3s ease';

	let str = ``
	// 收集成功
	if (type == 1) {
		let score = data.score || ""
		let total = data.total || ""
		str += `<div class="toast-box" style="max-width:80%;width:275px;height:275px;margin: 0 auto;border-radius: 8px;padding: 30px 15px;margin-bottom: 50px;
    font-size: 14px;display: flex;align-items: center;justify-content: center;flex-direction: column;background-image: url('${serverName}/staticFile/vr-bg.png');background-repeat: no-repeat;
    background-size: 100% 100%;position: relative;">`;
		str += `<img src="${serverName}/staticFile/vr-su-icon.png" style="width:90px;height:90px;" />`;
		str += `<img src="${serverName}/staticFile/close.png" style="position: absolute;left: 50%;bottom: -44px;transform:translate(-50%, 0);z-index: 9999999;width:36px;height:36px;" id="closeBtn1"/>`;
		str += `<div style="margin-bottom:15px;color:#9F4900;font-size: 16px;font-weight: bold;display: flex;align-items: center;">成功收集` + `<span style="min-width:25px;min-height:25px;background: #9F4900;
    border-radius: 50%;text-align:center;display: inline-block;margin: 0 5px;
    line-height: 25px;font-size: 16px;
    color: #FFFFFF;">` + score + `</span>枚龙宫币</div>`;
		str += `<div style="font-weight: 500;font-size: 14px;color: #9F4900;">您现在共有` + total + `枚</div>`;
		str += `<img src="${serverName}/staticFile/vr-su-btn.png" style="width:180px;height:56px;margin-top:30px;" id="closeBtn2"/>`;
		str += `</div>`
	} else if (type == 2) {
		str += `<div class="toast-box" style="max-width:80%;width:275px;height:275px;margin: 0 auto;border-radius: 8px;padding: 30px 15px;margin-bottom: 50px;
    font-size: 14px;display: flex;align-items: center;justify-content: center;flex-direction: column;background-image: url('${serverName}/staticFile/vr-bg.png');background-repeat: no-repeat;
    background-size: 100% 100%;position: relative;">`;
		str += `<img src="${serverName}/staticFile/vr-er-icon.png" style="width:90px;height:90px;" />`;
		str += `<img src="${serverName}/staticFile/close.png" style="position: absolute;left: 50%;bottom: -44px;transform:translate(-50%, 0);z-index: 9999999;width:36px;height:36px;" id="closeBtn1"/>`;
		str += `<div style="margin-bottom:15px;color:#9F4900;font-size: 16px;font-weight: bold;">您已经收集过该龙宫币</div>`;
		str += `<div style="font-weight: 500;font-size: 14px;color: #9F4900;">请到别的地方进行收集吧</div>`;
		str += `<img src="${serverName}/staticFile/vr-er-btn.png" style="width:180px;height:56px;margin-top:30px;" id="closeBtn2"/>`;
		str += `</div>`
	} else if (type == 3) {
		str += `<div class="toast-box" style="max-width:80%;width:275px;height:275px;margin: 0 auto;border-radius: 8px;padding: 30px 15px;margin-bottom: 50px;
    font-size: 14px;display: flex;align-items: center;justify-content: center;flex-direction: column;background-image: url('${serverName}/staticFile/vr-bg.png');background-repeat: no-repeat;
    background-size: 100% 100%;position: relative;">`;
		str += `<img src="${serverName}/staticFile/vr-er-icon.png" style="width:90px;height:90px;" />`;
		str += `<img src="${serverName}/staticFile/close.png" style="position: absolute;left: 50%;bottom: -44px;transform:translate(-50%, 0);z-index: 9999999;width:36px;height:36px;" id="closeBtn1"/>`;
		str += `<div style="margin-bottom:15px;color:#9F4900;font-size: 16px;font-weight: bold;">积分点位暂未开启</div>`;
		str += `<div style="font-weight: 500;font-size: 14px;color: #9F4900;">请到别的地方进行收集吧</div>`;
		str += `<img src="${serverName}/staticFile/vr-er-btn.png" style="width:180px;height:56px;margin-top:30px;" id="closeBtn2"/>`;
		str += `</div>`
	}

	toast.innerHTML = str;
	// 将toast添加到body中
	document.body.appendChild(toast);
	// 通过过渡效果显示toast
	setTimeout(() => {
		toast.style.opacity = '1';
	}, 100);
	document.getElementById('closeBtn1').addEventListener('click', function () {
		toast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(toast);
			if (hotspotVrUrl) {
				window.location.href = `${hotspotVrUrl}&token=${tokenValue}`;
			}
		}, 500);
	});
	document.getElementById('closeBtn2').addEventListener('click', function () {
		toast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(toast);
			if (hotspotVrUrl) {
				window.location.href = `${hotspotVrUrl}&token=${tokenValue}`;
			}
		}, 500);
	});
}


// 传说故事弹窗
function legendlDialog(title, content) {
	const toast = document.createElement('div');
	toast.style.width = '100%';
	toast.style.height = '100%';
	toast.style.position = 'fixed';
	toast.style.bottom = '0';
	toast.style.left = '50%';
	toast.style.transform = "translate(-50%, 0)";
	toast.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
	toast.style.opacity = '0';
	toast.style.zIndex = '999990';
	toast.style.transition = 'opacity 0.3s ease, visibility 0.3s ease';

	// 使用aaa.html中的body内容结构，但不直接插入content
	let str = ``;
	str += `<div id="article-box-bg" style="width:100%;height:88%;margin: 0 auto;border-radius: 20px 20px 0 0;
	background-image: url('${serverName}/staticFile/csgs_back.png');
	background-size: 100% 100%;position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        bottom: 0;
        left: 0;padding:40px 0 0 0;" id="box-box">
		<img src="${serverName}/staticFile/close1.png"
			style="width:30px;height:30px;position: absolute;right:10px;top:10px;zIndex='999999'" id="closeBtnLen" />
		<div
			style="font-size: 24px;color: #007489;text-align: center;font-weight: bold;margin-top: 10%;margin-bottom: 3%;max-width: 50%;">
			${title || ''}</div>
		<img src="${serverName}/staticFile/csgs_titile.png"
			style="width:150px;height:30px;" />
		<div id="content-container" style="width: 75%;height: 72%;margin-top: 3%;margin-left: 1%;overflow-y: auto;padding: 10px;color: #333;font-size: 16px;line-height: 1.6;">
		</div>
	</div>`;

	toast.innerHTML = str;

	// 将toast添加到body中
	document.body.appendChild(toast);

	// 设置富文本内容
	const contentContainer = document.getElementById('content-container');
	if (contentContainer) {
		contentContainer.innerHTML = content || '';
	}

	// 通过过渡效果显示toast
	setTimeout(() => {
		toast.style.opacity = '1';
	}, 100);

	document.getElementById('closeBtnLen').addEventListener('click', function () {
		console.log('点击关闭按钮');
		toast.style.opacity = '0';
		setTimeout(() => {
			document.body.removeChild(toast);
		}, 500);
	});
}
