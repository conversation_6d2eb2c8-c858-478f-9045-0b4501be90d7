// 路由
import {
	RouterMount,
	createRouter
} from './uni-simple-router.js'
import store from '@/nxTemp/store'
const router = createRouter({
	platform: process.env.VUE_APP_PLATFORM,
	applet: {
		animationDuration: 0 //默认 300ms 
	},
	routerErrorEach: ({
		type,
		msg
	}) => {
		switch (type) {
			case 3: // APP退出应用
				// #ifdef APP-PLUS
				router.$lockStatus = false;
				uni.showModal({
					title: '提示',
					content: '您确定要退出应用吗？',
					success: function(res) {
						if (res.confirm) {
							plus.runtime.quit();
						}
					}
				});
				// #endif
				break;
			case 2:
			case 0:
				router.$lockStatus = false;
				break;
			default:
				break;
		}

	},
	// 通配符，非定义页面，跳转404
	routes: [...ROUTES,
		{
			path: '*',
			redirect: (to) => {
				return {
					name: '404'
				}
			}
		},
	]
});

//全局路由前置守卫
router.beforeEach((to, from, next) => {
	// 权限控制登录
	// 有两个判断条件,一个是token,还有一个路由元信息
	const hasLogin = store.getters.hasLogin;
	
	// 权限控制
	if (to.meta && to.meta.auth && !hasLogin) {
		// 没有登录信息，需要登录
		next({
			path: '/pages/login/login',
			// 将要跳转的路径作为参数传递，登录成功后可以跳回
			query: {
				redirect: to.fullPath
			}
		});
	} else {
		next();
	}
});

export {
	router,
	RouterMount
}
