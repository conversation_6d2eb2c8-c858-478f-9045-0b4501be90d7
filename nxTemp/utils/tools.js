import { BASE_IMG_URL } from "@/env.js";

export default {
	/**
	 * 图片处理-预览图片
	 * @param {Array} urls - 图片列表
	 * @param {Number} current - 首个预览下标
	 */
	previewImage(urls = [], current = 0) {
		uni.previewImage({
			urls: urls,
			current: current,
			indicator: 'default',
			loop: true,
			fail(err) {
				console.log('previewImage出错', urls, err)
			},
		})
	},
	/**
	 * 打电话
	 * @param {String<Number>} phoneNumber - 数字字符串
	 */
	callPhone(phoneNumber = '') {
		let num = phoneNumber.toString()
		uni.makePhoneCall({
			phoneNumber: num,
			fail(err) {
				console.log('makePhoneCall出错', err)
			},
		});
	},

	/**
	 * @description 弹窗提示 showModal
	 */
	showModal(content, callback) {
		uni.showModal({
			title: '提示',
			content: content,
			showCancel: false,
			success(res) {
				if (res.confirm) {
					if (typeof callback === 'function') {
						callback();
					}
				} else if (res.cancel) { }
			}
		})
	},

	/**
	 * 将日期字符串转换为中文格式 (YYYY年MM月DD日 HH:MM)
	 * @param {string} dateStr - 日期字符串，格式为 'YYYY-MM-DD HH:MM:SS'
	 * @returns {string} 格式化后的日期字符串，如 '2025年5月28日09:48'
	 */
	formatDateToChinese(dateStr) {
		// 如果日期为空，返回空字符串
		if (!dateStr) return '';

		try {
			// 尝试解析日期字符串
			const date = new Date(dateStr.replace(/-/g, '/'));

			// 检查日期是否有效
			if (isNaN(date.getTime())) {
				return dateStr;
			}

			// 获取年、月、日、时、分
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');

			// 拼接成中文格式
			return `${year}年${month}月${day}日 ${hours}:${minutes}`;
		} catch (error) {
			console.error('日期格式化错误:', error);
			return dateStr;
		}
	},

	/**
	 * 将日期字符串转换为简单格式 (YYYY-MM-DD HH:MM)
	 * @param {string} dateStr - 日期字符串，格式为 'YYYY-MM-DD HH:MM:SS'
	 * @returns {string} 格式化后的日期字符串，如 '2025-5-28 09:48'
	 */
	formatDateSimple(dateStr) {
		// 如果日期为空，返回空字符串
		if (!dateStr) return '';

		try {
			// 尝试解析日期字符串
			const date = new Date(dateStr.replace(/-/g, '/'));

			// 检查日期是否有效
			if (isNaN(date.getTime())) {
				return dateStr;
			}

			// 获取年、月、日、时、分
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');

			// 拼接成简单格式
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		} catch (error) {
			console.error('日期格式化错误:', error);
			return dateStr;
		}
	},
}
