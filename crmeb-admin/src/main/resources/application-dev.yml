# CRMEB 相关配置
crmeb:
  version: JAVA-MER-v1.8.1 # 当前代码版本
  domain: #配合swagger使用 # 待部署域名
  wechat-api-url:  #请求微信接口中专服务器
  asyncConfig: false #是否同步config表数据到redis
  asyncWeChatProgramTempList: false #是否同步小程序公共模板库
  imagePath: /JAVA_PROJECT/MER/trip/admin/ # 服务器图片路径配置 斜杠结尾
  retailStoreBrokerageRatio: 30 #佣金返佣比例和上限
  activityStyleCachedTime: 10 #活动边框缓存周期 秒为单位，生产环境适当5-10分钟即可
  selectProductLimit: 100 # 商品选择 指定商品上线
  productTagCacheMinutes: 10 # 商品标签缓存分钟数

server:
  port: 20800

spring:
  profiles:
    #  配置的环境
    active: dev
    #  数据库配置
  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *************************************************************************************************************************************************
    url: ****************************************************************************************************
#    url: ********************************************************************************************************
    username: root
    password: root
#    password: Czkj@2024
  redis:
    host: wzblcy.cn #地址
    port: 10196 #端口
    password: 123456
    timeout: 10000 # 连接超时时间（毫秒）
    database: 8 #默认数据库

#    host: localhost #地址
#    port: 6379 #端口
#    password:
#    password: 123456
#    timeout: 10000 # 连接超时时间（毫秒）
#    database: 8 #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1

minio:
  minio_url: http://**************:8084
  minio_name: minioadmin
  minio_pass: minioadmin
  bucketName: dljs
