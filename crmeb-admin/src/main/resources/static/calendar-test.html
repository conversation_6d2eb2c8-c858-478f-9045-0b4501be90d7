<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国日历接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .date-type {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 3px;
            font-size: 12px;
        }
        .workday { background-color: #d4edda; color: #155724; }
        .weekend { background-color: #fff3cd; color: #856404; }
        .holiday { background-color: #f8d7da; color: #721c24; }
        .transfer { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗓️ 中国日历接口测试</h1>
        <p>这个页面用于测试中国日历接口的功能，包括节假日查询、日期类型判断等。</p>
    </div>

    <!-- 测试用例 -->
    <div class="container">
        <div class="test-section">
            <h2>📋 获取测试用例</h2>
            <button onclick="getTestCases()">获取测试用例</button>
            <div id="testCasesResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <!-- 节假日数据测试 -->
    <div class="container">
        <div class="test-section">
            <h2>🎉 节假日数据测试</h2>
            <label>年份: </label>
            <input type="number" id="holidayYear" value="2025" min="2020" max="2030">
            <button onclick="getHolidayData()">获取节假日数据</button>
            <div id="holidayResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <!-- 单个日期测试 -->
    <div class="container">
        <div class="test-section">
            <h2>📅 单个日期类型测试</h2>
            <label>日期: </label>
            <input type="date" id="singleDate" value="2025-01-16">
            <button onclick="testSingleDate()">测试日期类型</button>
            <div id="singleDateResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <!-- 批量日期测试 -->
    <div class="container">
        <div class="test-section">
            <h2>📊 批量日期测试</h2>
            <label>开始日期: </label>
            <input type="date" id="startDate" value="2025-01-01">
            <label>结束日期: </label>
            <input type="date" id="endDate" value="2025-01-31">
            <button onclick="batchTest()">批量测试</button>
            <div id="batchResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <!-- 快速测试按钮 -->
    <div class="container">
        <div class="test-section">
            <h2>⚡ 快速测试</h2>
            <button onclick="quickTest('2025-01-01')">测试元旦</button>
            <button onclick="quickTest('2025-02-10')">测试春节</button>
            <button onclick="quickTest('2025-01-04')">测试周六</button>
            <button onclick="quickTest('2025-01-06')">测试周一</button>
            <button onclick="quickTestRange()">测试1月份</button>
        </div>
    </div>

    <script>
        const baseUrl = '/api/test/calendar';
        const authToken = 'platformef3f3cef785f49c9aec2765ede5eff03';

        // 通用请求头配置
        const getHeaders = () => {
            return {
                'Authori-zation': authToken,
                'Content-Type': 'application/json'
            };
        };

        // 获取测试用例
        function getTestCases() {
            fetch(`${baseUrl}/test-cases`, {
                headers: getHeaders()
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('testCasesResult').style.display = 'block';
                    document.getElementById('testCasesResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取测试用例失败: ' + error.message);
                });
        }

        // 获取节假日数据
        function getHolidayData() {
            const year = document.getElementById('holidayYear').value;
            fetch(`${baseUrl}/holiday-data/${year}`, {
                headers: getHeaders()
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('holidayResult').style.display = 'block';
                    document.getElementById('holidayResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取节假日数据失败: ' + error.message);
                });
        }

        // 测试单个日期
        function testSingleDate() {
            const date = document.getElementById('singleDate').value;
            fetch(`${baseUrl}/date-type/${date}`, {
                headers: getHeaders()
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('singleDateResult').style.display = 'block';
                    document.getElementById('singleDateResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('测试单个日期失败: ' + error.message);
                });
        }

        // 批量测试
        function batchTest() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            fetch(`${baseUrl}/batch-test`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify({
                    startDate: startDate,
                    endDate: endDate
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('batchResult').style.display = 'block';

                // 格式化显示批量测试结果
                if (data.success && data.data && data.data.dateTypes) {
                    let formattedResult = `批量测试结果:\n`;
                    formattedResult += `时间范围: ${data.data.startDate} 到 ${data.data.endDate}\n`;
                    formattedResult += `总天数: ${data.data.totalDays}\n\n`;

                    Object.entries(data.data.dateTypes).forEach(([date, type]) => {
                        formattedResult += `${date}: ${type}\n`;
                    });

                    document.getElementById('batchResult').textContent = formattedResult;
                } else {
                    document.getElementById('batchResult').textContent = JSON.stringify(data, null, 2);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('批量测试失败: ' + error.message);
            });
        }

        // 快速测试单个日期
        function quickTest(date) {
            document.getElementById('singleDate').value = date;
            testSingleDate();
        }

        // 快速测试日期范围
        function quickTestRange() {
            document.getElementById('startDate').value = '2025-01-01';
            document.getElementById('endDate').value = '2025-01-31';
            batchTest();
        }
    </script>
</body>
</html>
