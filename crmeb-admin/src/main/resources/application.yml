# CRMEB 相关配置
crmeb:
  version: JAVA-MER-v1.8.1 # 当前代码版本
  domain: #配合swagger使用 # 待部署域名
  wechat-api-url:  #请求微信接口中专服务器
  asyncConfig: false #是否同步config表数据到redis
  imagePath: /JAVA_PROJECT/MER/dev/admin/ # 服务器图片路径配置 斜杠结尾
  retailStoreBrokerageRatio: 30 #佣金返佣比例和上限
  activityStyleCachedTime: 10 #活动边框缓存周期 秒为单位，生产环境适当5-10分钟即可
  selectProductLimit: 100 # 商品选择 指定商品上线
  productTagCacheMinutes: 10 # 商品标签缓存分钟数
  orderCancelTime: 30 # 订单支付取消时间，单位分钟
  phoneMaskSwitch: false # 手机号掩码开关，true-开启,false-关闭
  ignored: #安全路径白名单
      - swagger-ui/
      - swagger-resources/**
      - /**/v2/api-docs
      - /**/*.html
      - /**/*.js
      - /**/*.css
      - /**/*.png
      - /**/*.map
      - /favicon.ico
      - /actuator/**
      - /druid/**
      - api/admin/platform/pagediy/info
      - api/admin/merchant/page/diy/info

# 配置端口
server:
  port: 8080
  servlet:
    context-path: /         # 访问path
  tomcat:
    uri-encoding: UTF-8     # 默认编码格式
    max-threads: 1000       # 最大线程数量 默认200
    min-spare-threads: 30   # 初始化启动线程数量

spring:
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  application:
    name: crmeb-admin #这个很重要，这在以后的服务与服务之间相互调用一般都是根据这个name
  jackson:
    locale: zh_CN
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***************************************************************************************************
    username: test_java_mer
    password: 111111
  # quartz
  quartz:
    properties:
      org:
        quartz:
          scheduler:
            instanceName: quartzScheduler
            #            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: false
            clusterCheckinInterval: 10000
            useProperties: false
          #            dataSource: crmeb_java_beta
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    #数据库方式
    job-store-type: JDBC
  # 捕获404异常
  mvc:
    throw-exception-if-no-handler-found: true
  resources:
    add-mappings: false

  redis:
    host: 127.0.0.1 #地址
    port: 6379 #端口
    password: 111111
    timeout: 30000 # 连接超时时间（毫秒）
    database: 9  #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
    second:
      database: 13 # 微信accessToken存储库

debug: true
logging:
  level:
    io.swagger.*: error
    com.zbjk.crmeb: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml
  file:
    path: ./crmeb_log

# mybatis 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*/*Mapper.xml #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  typeAliasesPackage: com.zbkj.**.model
  # 配置slq打印日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDel  #全局逻辑删除字段值 3.3.0开始支持，详情看下面。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

# 行为验证码
aj:
  captcha:
    type: default       # 验证码类型
    water-mark: CRMEB Java   # 水印
    slip-offset: 5      # 校验滑动拼图允许误差偏移量(默认5像素)
    aes-status: true    # aes加密坐标开启或者禁用(true|false)
    interference-options: 2 # 滑动干扰项(0/1/2)
    font-style: 1       # 点选字体样式 默认Font.BOLD
    font-size: 25       # 点选字体字体大小
    req-frequency-limit-enable: false   # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5     # 验证失败5次，get接口锁定
    req-get-lock-seconds: 360   # 验证失败后，锁定时间间隔,s
    req-get-minute-limit: 30     # get接口一分钟内请求数限制
    req-check-minute-limit: 60    # check接口一分钟内请求数限制
    req-verify-minute-limit: 60     # verify接口一分钟内请求数限制

    # 滑动验证，底图路径，不配置将使用默认图片
    # 支持全路径
    # 支持项目路径,以classpath:开头,取resource目录下路径,例：classpath:images/jigsaw
    jigsaw: classpath:images/jigsaw
    #滑动验证，底图路径，不配置将使用默认图片
    ##支持全路径
    # 支持项目路径,以classpath:开头,取resource目录下路径,例：classpath:images/pic-click
    pic-click: classpath:images/pic-click
    # 对于分布式部署的应用，我们建议应用自己实现CaptchaCacheService，比如用Redis或者memcache，
    # 参考CaptchaCacheServiceRedisImpl.java
    # 如果应用是单点的，也没有使用redis，那默认使用内存。
    # 内存缓存只适合单节点部署的应用，否则验证码生产与验证在节点之间信息不同步，导致失败。
    # ！！！ 注意啦，如果应用有使用spring-boot-starter-data-redis，
    # 请打开CaptchaCacheServiceRedisImpl.java注释。
    # redis ----->  SPI： 在resources目录新建META-INF.services文件夹(两层)，参考当前服务resources。
    # 缓存local/redis...
    cache-type: redis
    # local缓存的阈值,达到这个值，清除缓存
    cache-number: 1000
    # local定时清除过期缓存(单位秒),设置为0代表不执行
    timing-clear: 3600

    history-data-clear-enable: false

#swagger 配置
swagger:
  basic:
    enable: true #是否开启
    check: false #是否打开验证
    username: #访问swagger的账号
    password: #访问swagger的密码
# wx-java-tools 第三方框架仅仅用到了微信小程序直播功能 是用小程序直播时必须配置
wx:
  miniapp:
    configs:
      - appid: 111111 #微信小程序的appid
        secret: 111111 #微信小程序的Secret
        token: CrmebJava #微信小程序消息服务器配置的token
        aesKey:  #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON

    config-storage:
       type: redistemplate
