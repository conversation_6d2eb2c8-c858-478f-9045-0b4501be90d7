package com.zbkj.admin.controller.foundation;

import com.zbkj.common.model.foundation.SysDictItem;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.SysDictItemRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.foundation.ISysDictItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 数据字典
 */
@Api(tags = "数据字典")
@RestController
@RequestMapping("api/sys/dictItem")
@Slf4j
public class SysDictItemController {
    @Autowired
    private ISysDictItemService iSysDictItemService;

    @ApiOperation(value = "字典分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<SysDictItem>> getList(@ModelAttribute SysDictItemRequest request,
                                                         @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(iSysDictItemService.getList(request, pageParamRequest)));
    }

    @ApiOperation(value = "新增字典")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated SysDictItemRequest sysDictRequest) {
        SysDictItem sysDictItem = new SysDictItem();
        BeanUtils.copyProperties(sysDictRequest, sysDictItem);
        if (iSysDictItemService.save(sysDictItem)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "编辑字典")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated SysDictItemRequest request) {
        SysDictItem sysDictItem = new SysDictItem();
        BeanUtils.copyProperties(request, sysDictItem);
        if (iSysDictItemService.updateById(sysDictItem)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "字典删除")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable(value = "id") String id) {
        if (iSysDictItemService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "字典详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<SysDictItem> info(@PathVariable String id) {
        return CommonResult.success(iSysDictItemService.getById(id));
    }
}
