package com.zbkj.admin.controller.foundation;

import com.zbkj.common.model.foundation.FdnData;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.FdnDataRequest;
import com.zbkj.common.request.foundation.FdnDataSearchRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.foundation.FdnDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 基础数据维护 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/foundation/data")
@Api(tags = "基础数据维护") //配合swagger使用
public class FdnDataController {

    @Autowired
    private FdnDataService fdnDataService;

    /**
     * 分页显示基础数据维护
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * <AUTHOR>
     * @since 2025-05-21
     */
    @ApiOperation(value = "分页列表") //配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<FdnData>> getList(@Validated FdnDataSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<FdnData> fdnDataCommonPage = CommonPage.restPage(fdnDataService.getList(request, pageParamRequest));
        return CommonResult.success(fdnDataCommonPage);
    }

    /**
     * 新增基础数据维护
     * @param fdnDataRequest 新增参数
     * <AUTHOR>
     * @since 2025-05-21
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated FdnDataRequest fdnDataRequest) {
        FdnData fdnData = new FdnData();
        BeanUtils.copyProperties(fdnDataRequest, fdnData);

        if(fdnDataService.save(fdnData)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除基础数据维护
     * @param id Integer
     * <AUTHOR>
     * @since 2025-05-21
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public CommonResult<String> delete(@RequestParam(value = "id") String id) {
        if(fdnDataService.removeById(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改基础数据维护
     * @param id integer id
     * @param fdnDataRequest 修改参数
     * <AUTHOR>
     * @since 2025-05-21
     */
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam String id, @RequestBody @Validated FdnDataRequest fdnDataRequest) {
        FdnData fdnData = new FdnData();
        BeanUtils.copyProperties(fdnDataRequest, fdnData);
        fdnData.setId(id);

        if(fdnDataService.updateById(fdnData)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 查询基础数据维护信息
     * @param id Integer
     * <AUTHOR>
     * @since 2025-05-21
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<FdnData> info(@RequestParam(value = "id") Integer id) {
        FdnData fdnData = fdnDataService.getById(id);
        return CommonResult.success(fdnData);
    }
}



