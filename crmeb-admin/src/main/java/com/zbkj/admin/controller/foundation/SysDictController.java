package com.zbkj.admin.controller.foundation;


import com.zbkj.common.model.foundation.SysDict;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.SysDictRequest;
import com.zbkj.common.response.foundation.SysDictResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.foundation.ISysDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @Date 2025-05-14
 */
@Slf4j
@RestController
@RequestMapping("api/sys/dict")
@Api(tags = "平台端 字典管理")
public class SysDictController {

    @Autowired
    private ISysDictService sysDictService;

    @ApiOperation(value = "字典分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<SysDict>> getList(@ModelAttribute SysDictRequest request,
                                                     @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(sysDictService.getList(request, pageParamRequest)));
    }

    @ApiOperation(value = "新增字典")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated SysDictRequest sysDictRequest) {
        SysDict sysDict = new SysDict();
        BeanUtils.copyProperties(sysDictRequest, sysDict);
        if (sysDictService.save(sysDict)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "编辑字典")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated SysDictRequest sysDictRequest) {
        SysDict sysDict = new SysDict();
        BeanUtils.copyProperties(sysDictRequest, sysDict);
        if (sysDictService.updateById(sysDict)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "字典删除")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable(value = "id") String id) {
        if (sysDictService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "字典详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<SysDict> info(@PathVariable String id) {
        return CommonResult.success(sysDictService.getById(id));
    }

    @ApiOperation(value = "根据字典编码查询字典项")
    @RequestMapping(value = "/getDictItems/{dictCode}", method = RequestMethod.GET)
    public CommonResult<SysDictResponse> getDictItems(@PathVariable("dictCode") String dictCode) {
        return CommonResult.success(sysDictService.getDictItems(dictCode));
    }
}
