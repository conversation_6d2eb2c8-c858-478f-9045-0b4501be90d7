package com.zbkj.admin.controller.foundation;

import com.zbkj.common.model.foundation.FdnHousekeeperMgmt;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.FdnHousekeeperMgmtSearchRequest;
import com.zbkj.common.request.foundation.FdnHousekeeperMgmtRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.foundation.FdnHousekeeperMgmtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 小管家管理 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/foundation/housekeeper/mgmt")
@Api(tags = "小管家管理") //配合swagger使用
public class FdnHousekeeperMgmtController {

    @Autowired
    private FdnHousekeeperMgmtService fdnHousekeeperMgmtService;

    /**
     * 分页显示小管家管理
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "分页列表") //配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<FdnHousekeeperMgmt>> getList(@Validated FdnHousekeeperMgmtSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<FdnHousekeeperMgmt> fdnHousekeeperMgmtCommonPage = CommonPage.restPage(fdnHousekeeperMgmtService.getList(request, pageParamRequest));
        return CommonResult.success(fdnHousekeeperMgmtCommonPage);
    }

    /**
     * 新增小管家管理
     * @param fdnHousekeeperMgmtRequest 新增参数
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated FdnHousekeeperMgmtRequest fdnHousekeeperMgmtRequest) {
        FdnHousekeeperMgmt fdnHousekeeperMgmt = new FdnHousekeeperMgmt();
        BeanUtils.copyProperties(fdnHousekeeperMgmtRequest, fdnHousekeeperMgmt);

        if(fdnHousekeeperMgmtService.save(fdnHousekeeperMgmt)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除小管家管理
     * @param id Integer
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public CommonResult<String> delete(@RequestParam(value = "id") Integer id) {
        if(fdnHousekeeperMgmtService.removeById(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改小管家管理
     * @param id integer id
     * @param fdnHousekeeperMgmtRequest 修改参数
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam String id, @RequestBody @Validated FdnHousekeeperMgmtRequest fdnHousekeeperMgmtRequest) {
        FdnHousekeeperMgmt fdnHousekeeperMgmt = new FdnHousekeeperMgmt();
        BeanUtils.copyProperties(fdnHousekeeperMgmtRequest, fdnHousekeeperMgmt);
        fdnHousekeeperMgmt.setId(id);

        if(fdnHousekeeperMgmtService.updateById(fdnHousekeeperMgmt)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 查询小管家管理信息
     * @param id Integer
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<FdnHousekeeperMgmt> info() {
        return CommonResult.success(fdnHousekeeperMgmtService.getLatest());
    }
}




