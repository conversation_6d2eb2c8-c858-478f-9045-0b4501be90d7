package com.zbkj.admin.controller.business;

import com.zbkj.common.model.business.BizIntegralConfigMgmt;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizIntegralConfigMgmtRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.business.BizIntegralConfigMgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName: BizIntegralConfigMgmtController
 * @Description: 龙宫币配置控制类
 * @Author: zlj
 * @Date: 2025-05-26 10:12
 * @Version: 1.0
 **/
@Slf4j
@RestController
@RequestMapping("api/admin/business/integral")
@Api(tags = "龙宫币配置管理") //配合swagger使用
public class BizIntegralConfigMgmtController {
    @Autowired
    private BizIntegralConfigMgmtService bizIntegralConfigMgmtService;

    @ApiOperation(value = "龙宫币配置分页列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<BizIntegralConfigMgmt>> getList(@ModelAttribute BizIntegralConfigMgmtRequest request,
                                                                   @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(bizIntegralConfigMgmtService.getList(request, pageParamRequest)));
    }

    @ApiOperation(value = "新增龙宫币配置")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated BizIntegralConfigMgmtRequest request) {
        BizIntegralConfigMgmt bizIntegralConfigMgmt = new BizIntegralConfigMgmt();
        BeanUtils.copyProperties(request, bizIntegralConfigMgmt);
        if (bizIntegralConfigMgmtService.save(bizIntegralConfigMgmt)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "编辑龙宫币配置")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated BizIntegralConfigMgmtRequest request) {
        BizIntegralConfigMgmt bizIntegralConfigMgmt = new BizIntegralConfigMgmt();
        BeanUtils.copyProperties(request, bizIntegralConfigMgmt);
        if (bizIntegralConfigMgmtService.updateById(bizIntegralConfigMgmt)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "更新配置启用状态配置")
    @RequestMapping(value = "/updateIntegralEnable", method = RequestMethod.POST)
    public CommonResult<String> updateIntegralEnable(@RequestBody @Validated BizIntegralConfigMgmtRequest request) {
        BizIntegralConfigMgmt bizIntegralConfigMgmt = new BizIntegralConfigMgmt();
        if (bizIntegralConfigMgmtService.updateIntegralEnable(request)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "龙宫币配置删除")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable(value = "id") String id) {
        if (bizIntegralConfigMgmtService.removeById(id)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    @ApiOperation(value = "龙宫币配置详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<BizIntegralConfigMgmt> info(@PathVariable String id) {
        return CommonResult.success(bizIntegralConfigMgmtService.getById(id));
    }
}
