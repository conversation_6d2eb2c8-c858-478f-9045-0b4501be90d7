package com.zbkj.admin.controller.business;

import com.zbkj.common.model.business.BizQnrFillMgmt;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizQnrFillMgmtRequest;
import com.zbkj.common.request.business.BizQnrFillMgmtSearchRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.business.BizQnrFillMgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 问卷调查填报模板管理 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/business/fill/mgmt")
@Api(tags = "问卷调查填报模板管理") //配合swagger使用
public class BizQnrFillMgmtController {

    @Autowired
    private BizQnrFillMgmtService bizQnrFillMgmtService;

    /**
     * 分页显示问卷调查填报模板管理
     *
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     * <AUTHOR>
     * @since 2025-06-04
     */
    @ApiOperation(value = "分页列表") //配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<BizQnrFillMgmt>> getList(@Validated BizQnrFillMgmtSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<BizQnrFillMgmt> bizQnrFillMgmtCommonPage = CommonPage.restPage(bizQnrFillMgmtService.getList(request, pageParamRequest));
        return CommonResult.success(bizQnrFillMgmtCommonPage);
    }

    /**
     * 新增问卷调查填报模板管理
     *
     * @param bizQnrFillMgmtRequest 新增参数
     * <AUTHOR>
     * @since 2025-06-04
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated BizQnrFillMgmtRequest bizQnrFillMgmtRequest) {
        BizQnrFillMgmt bizQnrFillMgmt = new BizQnrFillMgmt();
        BeanUtils.copyProperties(bizQnrFillMgmtRequest, bizQnrFillMgmt);

        if (bizQnrFillMgmtService.save(bizQnrFillMgmt)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除问卷调查填报模板管理
     *
     * @param id Integer
     * <AUTHOR>
     * @since 2025-06-04
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public CommonResult<String> delete(@RequestParam(value = "id") String id) {
        if (bizQnrFillMgmtService.removeById(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 修改问卷调查填报模板管理
     *
     * @param id                    integer id
     * @param bizQnrFillMgmtRequest 修改参数
     * <AUTHOR>
     * @since 2025-06-04
     */
    @ApiOperation(value = "修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestParam String id, @RequestBody @Validated BizQnrFillMgmtRequest bizQnrFillMgmtRequest) {
        BizQnrFillMgmt bizQnrFillMgmt = new BizQnrFillMgmt();
        BeanUtils.copyProperties(bizQnrFillMgmtRequest, bizQnrFillMgmt);
        bizQnrFillMgmt.setId(id);

        if (bizQnrFillMgmtService.updateById(bizQnrFillMgmt)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 查询问卷调查填报模板管理信息
     *
     * @param id Integer
     * <AUTHOR>
     * @since 2025-06-04
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<BizQnrFillMgmt> info(@RequestParam(value = "id") String id) {
        BizQnrFillMgmt bizQnrFillMgmt = bizQnrFillMgmtService.getById(id);
        return CommonResult.success(bizQnrFillMgmt);
    }
}



