package com.zbkj.admin.controller.distributionmanagement;

import com.zbkj.common.model.distributionmanagement.UserApply;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.distributionmanagement.UserApplySearchRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.distributionmanagement.UserApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 推广员申请管理，用户申请推广员表
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@RestController
@RequestMapping("api/admin/platform/retail/store/apply")
@Api(tags = "推广员申请管理")
public class UserApplyController {

    @Autowired
    private UserApplyService userApplyService;

    /**
     * 分页获取推广员申请列表
     * @param request 搜索参数
     * @param pageParamRequest 分页参数
     * @return 申请列表
     */
    @PreAuthorize("hasAuthority('platform:retail:store:people:list')")
    @ApiOperation(value = "推广员申请列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<UserApply>> getList(@Validated UserApplySearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserApply> commonPage = CommonPage.restPage(userApplyService.getList(request, pageParamRequest));
        return CommonResult.success(commonPage);
    }

    /**
     * 审核推广员申请
     * @return 审核结果
     */
    @PreAuthorize("hasAuthority('platform:retail:store:people:list')")
    @ApiOperation(value = "审核推广员申请")
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public CommonResult<Boolean> audit(@RequestBody Map<String, Object> requestMap) {
        // 添加详细日志记录
        log.info("收到审核请求参数: {}", requestMap);
        
        // 获取原始参数并记录其类型
        Object idObj = requestMap.get("id");
        Object statusObj = requestMap.get("status");
        Object auditReasonObj = requestMap.get("auditReason");
        
        log.info("id参数值: {}, 类型: {}", idObj, idObj != null ? idObj.getClass().getName() : "null");
        log.info("status参数值: {}, 类型: {}", statusObj, statusObj != null ? statusObj.getClass().getName() : "null");
        log.info("auditReason参数值: {}, 类型: {}", auditReasonObj, auditReasonObj != null ? auditReasonObj.getClass().getName() : "null");
        
        // 更安全的类型转换逻辑
        Integer id = null;
        if (idObj != null) {
            if (idObj instanceof Integer) {
                id = (Integer) idObj;
            } else if (idObj instanceof Number) {
                id = ((Number) idObj).intValue();
            } else if (idObj instanceof String) {
                try {
                    id = Integer.parseInt((String) idObj);
                } catch (NumberFormatException e) {
                    log.error("id参数类型转换失败，无法将 {} 转换为Integer: {}", idObj, e.getMessage());
                }
            } else {
                log.error("id参数类型不支持转换: {}", idObj.getClass().getName());
            }
        } else {
            log.error("id参数为null，请检查前端传值");
        }
        
        Integer status = null;
        if (statusObj != null) {
            if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else if (statusObj instanceof Number) {
                status = ((Number) statusObj).intValue();
            } else if (statusObj instanceof String) {
                try {
                    status = Integer.parseInt((String) statusObj);
                } catch (NumberFormatException e) {
                    log.error("status参数类型转换失败，无法将 {} 转换为Integer: {}", statusObj, e.getMessage());
                }
            } else {
                log.error("status参数类型不支持转换: {}", statusObj.getClass().getName());
            }
        }
        
        String auditReason = null;
        if (auditReasonObj != null) {
            auditReason = auditReasonObj.toString();
        }
        
        if (id == null) {
            log.error("请求参数id为空或类型转换失败，审核失败");
            return CommonResult.failed("请求参数id不能为空");
        }
        
        if (status == null) {
            log.error("请求参数status为空或类型转换失败，审核失败");
            return CommonResult.failed("请求参数status不能为空");
        }
        
        // 记录最终处理的参数值
        log.info("最终处理的参数 - id: {}, status: {}, auditReason: {}", id, status, auditReason);
        
        return CommonResult.success(userApplyService.audit(id, status, auditReason));
    }
} 