package com.zbkj.admin.task.business;

import com.zbkj.service.service.business.BizEarlyWarningMgmtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 预警管理 定时任务
 */
@Slf4j
@Component("BizEarlyWarningMgmtTask")
public class BizEarlyWarningMgmtTask {

    @Autowired
    private BizEarlyWarningMgmtService bizEarlyWarningMgmtService;

    /**
     * 每天3点同步一次数据
     */
    public void autoCheck() {
        // cron : 0 0 3 * * ?
        log.info("BizEarlyWarningMgmtTask#autoCheck,--init--");
        try {
            bizEarlyWarningMgmtService.checkEarlyWarning();
        } catch (Exception e) {
            log.error("BizEarlyWarningMgmtTask#autoCheck,报错:{}", e.getMessage());
            throw new RuntimeException("500");
        }
    }

}
