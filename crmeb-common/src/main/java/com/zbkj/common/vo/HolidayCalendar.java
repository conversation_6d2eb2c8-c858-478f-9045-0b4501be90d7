package com.zbkj.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 节假日日历VO
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HolidayCalendar对象", description = "节假日日历")
public class HolidayCalendar implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "地区代码（CN-中国）")
    private String region;

    @ApiModelProperty(value = "节假日列表")
    private List<HolidayInfo> dates;

    /**
     * 获取法定节假日列表
     * @return 法定节假日列表
     */
    public List<HolidayInfo> getPublicHolidays() {
        if (dates == null) {
            return null;
        }
        return dates.stream()
                .filter(HolidayInfo::isPublicHoliday)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取调休工作日列表
     * @return 调休工作日列表
     */
    public List<HolidayInfo> getTransferWorkdays() {
        if (dates == null) {
            return null;
        }
        return dates.stream()
                .filter(HolidayInfo::isTransferWorkday)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据日期查找节假日信息
     * @param date 日期字符串（yyyy-MM-dd）
     * @return 节假日信息，如果不是节假日返回null
     */
    public HolidayInfo findByDate(String date) {
        if (dates == null || date == null) {
            return null;
        }
        return dates.stream()
                .filter(holiday -> date.equals(holiday.getDate()))
                .findFirst()
                .orElse(null);
    }
}
