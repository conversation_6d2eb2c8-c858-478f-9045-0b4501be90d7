package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 资讯管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_information_mgmt")
@ApiModel(value = "BizInformationMgmt", description = "资讯管理")
public class BizInformationMgmt extends SuperModel {

    /**
     * 资讯封面图
     */
    @TableField("inform_cover")
    @ApiModelProperty("资讯封面图")
    private String informCover;

    /**
     * 资讯名称
     */
    @TableField("inform_title")
    @ApiModelProperty("资讯名称")
    private String informTitle;

    /**
     * 资讯作者
     */
    @TableField("inform_author")
    @ApiModelProperty("资讯作者")
    private String informAuthor;

    /**
     * 资讯发布时间
     */
    @TableField("inform_push_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("资讯发布时间")
    private Date informPushTime;

    /**
     * 资讯置顶（0、1）数据越大显示在最顶上
     */
    @TableField("inform_top")
    @ApiModelProperty("资讯置顶，数据越大显示越靠前")
    private Integer informTop;

    /**
     * 资讯是否在首页显示
     */
    @TableField("inform_index_show")
    @ApiModelProperty("资讯是否在首页显示,参考字典：yn")
    private String informIndexShow;

    /**
     * 资讯内容（富文本）
     */
    @TableField("inform_content")
    @ApiModelProperty("资讯内容")
    private String informContent;

    /**
     * 来源
     */
    @TableField("inform_data_source")
    @ApiModelProperty("来源")
    private String informDataSource;

    /**
     * 资讯描述
     */
    @TableField("inform_desc")
    @ApiModelProperty("资讯描述")
    private String informDesc;

} 