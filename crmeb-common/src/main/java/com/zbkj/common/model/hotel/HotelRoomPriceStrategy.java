package com.zbkj.common.model.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店房型价格策略表
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_hotel_room_price_strategy")
public class HotelRoomPriceStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户ID(酒店ID)
     */
    private Integer merId;

    /**
     * 房间ID(关联eb_hotel_room)
     */
    private Integer roomId;

    /**
     * 策略名称(如:基础价格,周末价格,节假日价格)
     */
    private String strategyName;

    /**
     * 策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期
     */
    private Integer strategyType;

    /**
     * 适用星期(1,2,3,4,5,6,7 逗号分隔)
     */
    private String weekDays;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 具体日期列表(JSON格式)
     */
    private String specificDates;

    /**
     * 价格值(固定价格)
     */
    private BigDecimal priceValue;

    /**
     * 优先级(数字越大优先级越高,节假日>周末>基础价格)
     */
    private Integer priority;

    /**
     * 状态:0-禁用,1-启用
     */
    private Integer status;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer isDel;

    /**
     * 创建人ID
     */
    private Integer createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
