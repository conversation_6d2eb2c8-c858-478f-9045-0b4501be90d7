package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: biz_receive_record
 * @Author: jeecg-boot
 * @Date: 2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("biz_receive_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "biz_receive_record对象", description = "biz_receive_record")
public class BizReceiveRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 获取的热点或名称
     */
    @ApiModelProperty(value = "获取的热点或名称")
    private String receiveHotspotName;
    /**
     * 获取的热点或名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "获取的热点或名称")
    private String hotspotName;
    /**
     * 获取的数量
     */
    @ApiModelProperty(value = "获取的数量")
    private Integer receiveNumber;
    /**
     * 获取人的姓名或昵称
     */
    @ApiModelProperty(value = "获取人的姓名或昵称")
    private String receiveUserName;
    /**
     * 获取人的id
     */
    @ApiModelProperty(value = "获取人的id")
    private String receiveUserId;
    /**
     * 龙宫币的状态（有效、过期、使用中、已使用）
     */
    @ApiModelProperty(value = "龙宫币的状态（有效、过期、使用中、已使用）")
    private String recevieState = "0";

    /**
     * 记录核销码
     */
    @ApiModelProperty(value = "记录核销码")
    private String recevieVerifyCode;

    /**
     * 龙宫币剩余有效期（天）
     */
    @ApiModelProperty(value = "龙宫币剩余有效期（天）")
    private Integer recevieIndate;
    /**
     * 获取时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "获取时间")
    private Date receiveTime;
    /**
     * 是否删除，0-未删除，1-删除
     */
    @ApiModelProperty(value = "是否删除，0-未删除，1-删除")
    private Integer isDel;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    /**
     * 来源
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "来源")
    private String source = "receive";
}
