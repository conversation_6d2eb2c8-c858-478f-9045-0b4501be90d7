package com.zbkj.common.model.foundation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 字典表子项
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fdn_dict_item")
@ApiModel(value="fdnDictItem对象", description="字典表子项")
public class SysDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 字典id
     */
    @ApiModelProperty(value = "字典的id")
    private String dictId;

    /**
     * 字典项文本
     */
    @ApiModelProperty(value = "子项名称")
    private String itemName;

    /**
     * 字典项值
     */
    @ApiModelProperty(value = "子项编码")
    private String itemCode;

    /**
     * 描述
     */
    @ApiModelProperty(value = "子项描述")
    private String itemDesc;

    /**
     * 排序
     */
    @ApiModelProperty(value = "子项排序")
    private Integer itemSort;

    /**
     * 子项是否显示
     */
    @ApiModelProperty(value = "子项是否显示")
    private String itemEnable;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
