package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 应急救援管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_emergency_rescue_mgmt")
@ApiModel(value = "BizEmergencyRescueMgmt", description = "应急救援管理")
public class BizEmergencyRescueMgmt extends SuperModel {

    /**
     * 救援电话名称
     */
    @TableField("er_name")
    @ApiModelProperty("救援电话名称")
    private String erName;

    /**
     * 救援电话
     */
    @TableField("er_phone")
    @ApiModelProperty("救援电话")
    private String erPhone;

    /**
     * 应急疏散线路
     */
    @TableField("er_line_img")
    @ApiModelProperty("应急疏散线路")
    private String erLineImg;

    /**
     * 紧急救援说明
     */
    @TableField("er_line_desc")
    @ApiModelProperty("紧急救援说明")
    private String erLineDesc;

    /**
     * 安全注意事项（富文本）(按照顺序，存成一个JSON格式)
     */
    @TableField("er_security_item")
    @ApiModelProperty("安全注意事项")
    private String erSecurityItem;

} 