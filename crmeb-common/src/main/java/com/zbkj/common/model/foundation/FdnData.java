package com.zbkj.common.model.foundation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 基础数据维护
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fdn_data")
@ApiModel(value = "FdnData", description = "基础数据维护")
public class FdnData extends SuperModel {

    /**
     * 数据类型（字典）
     */
    @TableField("data_type")
    @ApiModelProperty("数据类型，参考字典:dataConfig")
    private String dataType;

    /**
     * 数据内容（富文本）
     */
    @TableField("data_content")
    @ApiModelProperty("数据内容")
    private String dataContent;

    /**
     * 数据描述
     */
    @TableField("data_desc")
    @ApiModelProperty("数据描述")
    private String dataDesc;

} 