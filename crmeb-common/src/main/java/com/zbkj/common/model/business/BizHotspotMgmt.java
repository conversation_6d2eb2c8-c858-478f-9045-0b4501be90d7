package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 热点管理
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_hotspot_mgmt")
@ApiModel(value = "BizHotspotMgmt", description = "热点管理")
public class BizHotspotMgmt extends SuperModel {

    /**
     * 热点名称
     */
    @TableField("hotspot_name")
    @ApiModelProperty("热点名称")
    private String hotspotName;

    /**
     * 热点编号
     */
    @TableField("hotspot_code")
    @ApiModelProperty("热点编号")
    private String hotspotCode;

    /**
     * 热点类型
     */
    @TableField("hotspot_type")
    @ApiModelProperty("热点类型")
    private String hotspotType;

    /**
     * 热点描述
     */
    @TableField("hotspot_desc")
    @ApiModelProperty("热点描述")
    private String hotspotDesc;

    /**
     * 关联的商家id
     */
    @TableField("hotspot_merchant_id")
    @ApiModelProperty("关联的商家id")
    private String hotspotMerchantId;

    /**
     * 热点vr路径
     */
    @TableField("hotspot_vr_url")
    @ApiModelProperty("热点vr路径")
    private String hotspotVrUrl;

    /**
     * 是否显示
     */
    @TableField("is_show")
    @ApiModelProperty("是否显示")
    private String isShow = "0";
} 