package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName: BizVerifyRecord
 * @Description: 龙宫币核销记录
 * @Author: zlj
 * @Date: 2025-06-03 17:09
 * @Version: 1.0
 **/
@Data
@TableName("biz_verify_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "biz_verify_record对象", description = "biz_verify_record")
public class BizVerifyRecord extends SuperModel {

    /**
     * 核销的热点或名称
     */
    @ApiModelProperty(value = "核销的热点或名称")
    private String verifyHotspotName;

    /**
     * 获取的热点或名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "获取的热点或名称")
    private String hotspotName;

    /**
     * 获取的数量
     */
    @ApiModelProperty(value = "获取的数量")
    private Integer verifyNumber;

    /**
     * 获取人的姓名或昵称
     */
    @ApiModelProperty(value = "获取人的姓名或昵称")
    private String verifyUserName;

    /**
     * 核销人的id
     */
    @ApiModelProperty(value = "核销人的id")
    private String verifyUserId;

    /**
     * 核销人的手机号
     */
    @ApiModelProperty(value = "核销人的手机号")
    private String verifyUserPhone;

    /**
     * 核销时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "核销时间")
    private Date verifyTime = new Date();

    /**
     * 核销操作人员
     */
    @ApiModelProperty(value = "核销操作人员")
    private String verifyActionUser;

    /**
     * 核销操作人员id
     */
    @ApiModelProperty(value = "核销操作人员id")
    private String verifyActionUserId;

    /**
     * 核销操作商家id
     */
    @ApiModelProperty(value = "核销操作商家id")
    private String verifyActionMerchantId;

    /**
     * 核销操作商家
     */
    @ApiModelProperty(value = "核销操作商家")
    private String verifyActionMerchant;

    /**
     * 核销二维码地址
     */
    @ApiModelProperty(value = "核销二维码地址")
    private String verifyQrcodeUrl;

    /**
     * 核销编码
     */
    @ApiModelProperty(value = "核销编码")
    private String verifyCode;

    /**
     * 核销状态（未核销、核销成功）
     */
    @ApiModelProperty(value = "核销状态（未核销、核销成功）")
    private String verifyState;

    /**
     * 核销的订单id
     */
    @ApiModelProperty(value = "核销的订单id")
    private String verifyOrderId;

    /**
     * 核销的商品名称
     */
    @ApiModelProperty(value = "核销的商品名称")
    private String verifyProductName;

    /**
     * 核销的商品编号
     */
    @ApiModelProperty(value = "核销的商品编号")
    private String verifyProductCode;

    /**
     * 来源
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "来源")
    private String source = "verify";

}
