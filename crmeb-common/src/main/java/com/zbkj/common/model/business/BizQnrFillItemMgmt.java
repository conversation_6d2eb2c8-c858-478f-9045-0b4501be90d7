package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 问卷调查填报子项管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_qnr_fill_item_mgmt")
@ApiModel(value = "BizQnrFillItemMgmt", description = "问卷调查填报子项管理")
public class BizQnrFillItemMgmt extends SuperModel {

    /**
     * 填报模板id
     */
    @ApiModelProperty("填报模板id")
    @TableField("model_id")
    private String modelId;

    /**
     * 填报子项id
     */
    @ApiModelProperty("填报子项id")
    @TableField("item_id")
    private String itemId;

    /**
     * 填报用户
     */
    @ApiModelProperty("填报用户")
    @TableField("fill_user")
    private String fillUser;

    /**
     * 填报用户id
     */
    @ApiModelProperty("填报用户id")
    @TableField("fill_user_id")
    private String fillUserId;

    /**
     * 填报结果
     */
    @ApiModelProperty("填报结果")
    @TableField("fill_result")
    private String fillResult;

} 