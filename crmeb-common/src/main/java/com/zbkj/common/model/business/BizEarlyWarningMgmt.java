package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 预警管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_early_warning_mgmt")
@ApiModel(value = "BizEarlyWarningMgmt", description = "预警管理")
public class BizEarlyWarningMgmt extends SuperModel {

    /**
     * 预警标题
     */
    @TableField("ew_title")
    @ApiModelProperty("预警标题")
    private String ewTitle;

    /**
     * 预警等级（一般）、Ⅲ级（较重）、Ⅱ级（严重）、Ⅰ级（特别严重）
     */
    @TableField("ew_level")
    @ApiModelProperty("预警等级,参考字典:warningType")
    private String ewLevel;

    /**
     * 预警天数
     */
    @TableField("ew_day")
    @ApiModelProperty("预警天数")
    private Integer ewDay;

    /**
     * 预警内容（富文本）
     */
    @TableField("ew_content")
    @ApiModelProperty("预警内容")
    private String ewContent;

    /**
     * 预警状态(有效、过期)
     */
    @TableField("ew_state")
    @ApiModelProperty("预警状态,参考字典:warningState")
    private String ewState;

    /**
     * 首页是否弹出展示
     */
    @TableField("ew_index_show")
    @ApiModelProperty("首页是否弹出展示")
    private String ewIndexShow;

} 