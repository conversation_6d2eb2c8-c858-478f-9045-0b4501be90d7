package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 问卷调查统计管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_qnr_statistics_mgmt")
@ApiModel(value = "BizQnrStatisticsMgmt", description = "问卷调查统计管理")
public class BizQnrStatisticsMgmt extends SuperModel {

    /**
     * 模板id
     */
    @ApiModelProperty("模板id")
    @TableField("mode_id")
    private String modeId;

    /**
     * 子项id
     */
    @ApiModelProperty("子项id")
    @TableField("item_id")
    private String itemId;

    /**
     * 统计结果
     */
    @ApiModelProperty("统计结果")
    @TableField("sc_result")
    private String scResult;

} 