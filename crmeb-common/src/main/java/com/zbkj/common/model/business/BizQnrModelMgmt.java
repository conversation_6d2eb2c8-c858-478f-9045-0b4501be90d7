package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 问卷调查模板管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_qnr_model_mgmt")
@ApiModel(value = "BizQnrModelMgmt", description = "问卷调查模板管理")
public class BizQnrModelMgmt extends SuperModel {

    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    @TableField("model_title")
    private String modelTitle;

    /**
     * 模板描述
     */
    @ApiModelProperty("模板描述")
    @TableField("model_desc")
    private String modelDesc;

    /**
     * 模板状态（草稿、已公布、已归档）
     */
    @ApiModelProperty("模板状态")
    @TableField("model_state")
    private String modelState;

    /**
     * 模板开始时间
     */
    @ApiModelProperty("模板开始时间")
    @TableField("model_start_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modelStartTime;

    /**
     * 模板结束时间
     */
    @ApiModelProperty("模板结束时间")
    @TableField("model_end_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modelEndTime;

    /**
     * 统计参与数
     */
    @ApiModelProperty("统计参与数")
    @TableField("model_qnr_cnt")
    private Integer modelQnrCnt;

    /**
     * 模板的题目
     */
    @ApiModelProperty("模板的题目")
    @TableField(exist = false)
    private List<BizQnrItemMgmt> itemList;

} 