package com.zbkj.common.model.foundation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 小管家管理
 *
 * <AUTHOR>
 * @since 2025-05-19 16:13:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("fdn_housekeeper_mgmt")
@ApiModel(value = "FdnHousekeeperMgmt", description = "小管家管理")
public class FdnHousekeeperMgmt extends SuperModel {

    /**
     * 小管家描述（富文本）
     */
    @TableField("hk_desc")
    @ApiModelProperty("小管家描述")
    private String hkDesc;

}
