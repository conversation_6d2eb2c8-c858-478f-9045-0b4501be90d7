package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: biz_integral_config_mgmt
 * @Author: zlj
 * @Date: 2025-05-26
 * @Version: V1.0
 */
@Data
@TableName("biz_integral_config_mgmt")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "biz_integral_config_mgmt对象", description = "biz_integral_config_mgmt")
public class BizIntegralConfigMgmt implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 随机分配热点（多选）
     */
    @ApiModelProperty(value = "随机分配热点（多选）")
    private String integralRandomHotspot;
    /**
     * 随机分配数量（天/个）
     */
    @ApiModelProperty(value = "随机分配数量（天/个）")
    private Integer integralRandomNumber;
    /**
     * 固定分配热点（多选）
     */
    @ApiModelProperty(value = "固定分配热点（多选）")
    private String integralFixedHotspot;
    /**
     * 固定分配数量（个）
     */
    @ApiModelProperty(value = "固定分配数量（个）")
    private Integer integralFixedNumber;
    /**
     * 问卷调查奖励（个）
     */
    @ApiModelProperty(value = "问卷调查奖励（个）")
    private Integer integralSurvey;
    /**
     * 消费奖励(%)
     */
    @ApiModelProperty(value = "消费奖励(%)")
    private Integer integralConsume;
    /**
     * 龙宫币规则
     */
    @ApiModelProperty(value = "龙宫币规则")
    private String integralRuleImg;
    /**
     * 兑换规则
     */
    @ApiModelProperty(value = "兑换规则")
    private String integralConvertImg;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用  0=否  1 =是")
    private Boolean integralEnable;
    /**
     * 有效期（天）预留
     */
    @ApiModelProperty(value = "有效期（天）预留")
    private Integer integralIndate;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String integralDesc;
    /**
     * 是否删除，0-未删除，1-删除
     */
    @ApiModelProperty(value = "是否删除，0-未删除，1-删除")
    private Integer isDel;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
