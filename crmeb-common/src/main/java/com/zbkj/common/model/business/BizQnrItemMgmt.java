package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 问卷调查子项管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_qnr_item_mgmt")
@ApiModel(value = "BizQnrItemMgmt", description = "问卷调查子项管理")
public class BizQnrItemMgmt extends SuperModel {

    /**
     * 子项模板id
     */
    @ApiModelProperty("子项模板id")
    @TableField("model_id")
    private String modelId;

    /**
     * 子项一级标题
     */
    @ApiModelProperty("子项一级标题")
    @TableField("item_title")
    private String itemTitle;

    /**
     * 子项二级标题
     */
    @ApiModelProperty("子项二级标题")
    @TableField("item_sub_title")
    private String itemSubTitle;

    /**
     * 子项二级所属的一级标题id
     */
    @ApiModelProperty("子项二级所属的一级标题id")
    @TableField("item_parent_id")
    private String itemParentId;

    /**
     * 子项排序
     */
    @ApiModelProperty("子项排序")
    @TableField("item_sort")
    private Integer itemSort;

    /**
     * 子项类型，参考问卷星
     */
    @ApiModelProperty("子项类型，参考问卷星")
    @TableField("item_result_type")
    private String itemResultType;

    /**
     * 子项结果选择，多个用逗号分隔（答案和选项值使用label:value的方式存储，例如:  男:A,女:B）
     */
    @ApiModelProperty("子项结果选择")
    @TableField("item_result_option")
    private String itemResultOption;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必填")
    @TableField("item_is_required")
    private String itemIsRequired;

} 