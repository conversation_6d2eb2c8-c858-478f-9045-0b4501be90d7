package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 投诉管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_complaints_mgmt")
@ApiModel(value = "BizComplaintsMgmt", description = "投诉管理")
public class BizComplaintsMgmt extends SuperModel {

    /**
     * 投诉人员
     */
    @TableField("cm_user")
    @ApiModelProperty("投诉人员")
    private String cmUser;

    /**
     * 投诉人员id
     */
    @TableField("cm_user_id")
    @ApiModelProperty("投诉人员id")
    private String cmUserId;

    /**
     * 投诉人员电话
     */
    @TableField("cm_user_phone")
    @ApiModelProperty("投诉人员电话")
    private String cmUserPhone;

    /**
     * 投诉内容
     */
    @TableField("cm_content")
    @ApiModelProperty("投诉内容")
    private String cmContent;

    /**
     * 投诉类型
     */
    @TableField("cm_type")
    @ApiModelProperty("投诉类型,参考字典：complaintsType")
    private String cmType;

    /**
     * 投诉照片
     */
    @TableField("cm_img")
    @ApiModelProperty("投诉照片")
    private String cmImg;

    /**
     * 投诉状态（已提交、已回复、已解决、未解决）
     */
    @TableField("cm_state")
    @ApiModelProperty("投诉状态,参考字典：complaintsState")
    private String cmState;

    /**
     * 投诉回复内容
     */
    @TableField("cm_reply_content")
    @ApiModelProperty("投诉回复内容")
    private String cmReplyContent;

    /**
     * 投诉时间
     */
    @TableField("cm_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("投诉时间")
    private Date cmTime;

    /**
     * 回复时间
     */
    @TableField("cm_reply_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("回复时间")
    private Date cmReplyTime;

    /**
     * 处理人
     */
    @TableField("cm_reply_user")
    @ApiModelProperty("处理人")
    private String cmReplyUser;

    /**
     * 处理人id
     */
    @TableField("cm_reply_user_id")
    @ApiModelProperty("处理人id")
    private String cmReplyUserId;

} 