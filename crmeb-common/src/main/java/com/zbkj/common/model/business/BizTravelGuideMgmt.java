package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zbkj.common.model.SuperModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 攻略管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_travel_guide_mgmt")
@ApiModel(value = "BizTravelGuideMgmt", description = "攻略管理")
public class BizTravelGuideMgmt extends SuperModel {

    /**
     * 攻略类型（字典）
     */
    @TableField("guide_type")
    @ApiModelProperty("攻略类型，参考字典：travelGuide")
    private String guideType;

    /**
     * 攻略封面图
     */
    @TableField("guide_cover")
    @ApiModelProperty("攻略封面图")
    private String guideCover;

    /**
     * 攻略名称
     */
    @TableField("guide_title")
    @ApiModelProperty("攻略名称")
    private String guideTitle;

    /**
     * 攻略作者
     */
    @TableField("guide_author")
    @ApiModelProperty("攻略作者")
    private String guideAuthor;

    /**
     * 攻略发布时间
     */
    @TableField("guide_push_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("攻略发布时间")
    private Date guidePushTime;

    /**
     * 置顶（0、1）数据越大显示在最顶上
     */
    @TableField("guide_top")
    @ApiModelProperty("置顶，数据越大显示在最顶上")
    private Integer guideTop;

    /**
     * 攻略是否首页显示
     */
    @TableField("guide_index_show")
    @ApiModelProperty("攻略是否首页显示,参考字典：yn")
    private String guideIndexShow;

    /**
     * 攻略内容（富文本）
     */
    @TableField("guide_content")
    @ApiModelProperty("攻略内容")
    private String guideContent;

    /**
     * 攻略来源
     */
    @TableField("guide_data_source")
    @ApiModelProperty("攻略来源")
    private String guideDataSource;

    /**
     * 攻略描述
     */
    @TableField("guide_desc")
    @ApiModelProperty("攻略描述")
    private String guideDesc;

    /**
     * 攻略关联的商家id
     */
    @TableField("guide_merchant_id")
    @ApiModelProperty("攻略关联的商家id")
    private String guideMerchantId;

    /**
     * 视频路径
     */
    @TableField("video")
    @ApiModelProperty("视频路径")
    private String video;

    /**
     * 联系电话
     */
    @TableField("telephone")
    @ApiModelProperty("联系电话")
    private String telephone;

    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 位置
     */
    @TableField("address")
    @ApiModelProperty("位置")
    private String address;

} 