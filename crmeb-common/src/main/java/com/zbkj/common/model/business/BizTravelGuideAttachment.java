package com.zbkj.common.model.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: BizTravelGuideAttachment
 * @Description: 攻略管理附件信息
 * @Author: zlj
 * @Date: 2025-06-10 19:23
 * @Version: 1.0
 **/
@Data
@TableName("biz_travel_guide_attachment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "biz_travel_guide_attachment对象", description = "biz_travel_guide_attachment")
public class BizTravelGuideAttachment implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String attId;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private String fkId;
    /**
     * 附件路径
     */
    @ApiModelProperty(value = "附件路径")
    private String attDir;
    /**
     * 标签类型
     */
    @ApiModelProperty(value = "标签类型")
    private String tagsType;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String sort;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
