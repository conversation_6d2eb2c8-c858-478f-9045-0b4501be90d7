package com.zbkj.common.request.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 问卷调查模板管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "BizQnrModelMgmtSearchRequest", description = "问卷调查模板管理查询对象")
public class BizQnrModelMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    private String modelTitle;

    /**
     * 模板描述
     */
    @ApiModelProperty("模板描述")
    private String modelDesc;

    /**
     * 模板状态（草稿、已公布、已归档）
     */
    @ApiModelProperty("模板状态")
    private String modelState;

    /**
     * 模板开始时间
     */
    @ApiModelProperty("模板开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modelStartTime;

    /**
     * 模板结束时间
     */
    @ApiModelProperty("模板结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modelEndTime;

    /**
     * 统计参与数
     */
    @ApiModelProperty("统计参与数")
    private Integer modelQnrCnt;

} 