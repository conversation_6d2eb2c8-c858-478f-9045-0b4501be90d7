package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店价格策略创建请求对象（用于新增房型时）
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelPriceStrategyCreateRequest", description = "酒店价格策略创建请求对象")
public class HotelPriceStrategyCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "策略名称", required = true)
    @NotBlank(message = "策略名称不能为空")
    @Size(max = 100, message = "策略名称长度不能超过100个字符")
    private String strategyName;

    @ApiModelProperty(value = "策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期", required = true)
    @NotNull(message = "策略类型不能为空")
    @Min(value = 1, message = "策略类型值错误")
    @Max(value = 5, message = "策略类型值错误")
    private Integer strategyType;

    @ApiModelProperty(value = "适用星期(1,2,3,4,5,6,7 逗号分隔)")
    private String weekDays;

    @ApiModelProperty(value = "价格值(固定价格)", required = true)
    @NotNull(message = "价格值不能为空")
    @DecimalMin(value = "0.01", message = "价格值不能小于0.01")
    @DecimalMax(value = "99999.99", message = "价格值不能超过99999.99")
    private BigDecimal priceValue;

    @ApiModelProperty(value = "优先级(数值越大优先级越高)", required = true)
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 999, message = "优先级不能超过999")
    private Integer priority;

    @ApiModelProperty(value = "状态:0-禁用,1-启用", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值错误")
    @Max(value = 1, message = "状态值错误")
    private Integer status;

    @ApiModelProperty(value = "策略描述")
    @Size(max = 500, message = "策略描述长度不能超过500个字符")
    private String description;
}
