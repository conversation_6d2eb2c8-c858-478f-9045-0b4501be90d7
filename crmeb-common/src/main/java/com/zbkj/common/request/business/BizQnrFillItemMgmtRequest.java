package com.zbkj.common.request.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 问卷调查填报子项管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "BizQnrFillItemMgmtRequest", description = "问卷调查填报子项管理请求对象")
public class BizQnrFillItemMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 填报模板id
     */
    @ApiModelProperty("填报模板id")
    private String modelId;

    /**
     * 填报子项id
     */
    @ApiModelProperty("填报子项id")
    private String itemId;

    /**
     * 填报用户
     */
    @ApiModelProperty("填报用户")
    private String fillUser;

    /**
     * 填报用户id
     */
    @ApiModelProperty("填报用户id")
    private String fillUserId;

    /**
     * 填报结果
     */
    @ApiModelProperty("填报结果")
    private String fillResult;

} 