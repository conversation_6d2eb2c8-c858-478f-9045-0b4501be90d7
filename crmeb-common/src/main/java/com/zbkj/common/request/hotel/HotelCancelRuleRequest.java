package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店取消规则请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelCancelRuleRequest", description = "酒店取消规则请求对象")
public class HotelCancelRuleRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则ID(修改时必填)")
    private Integer id;

    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过100个字符")
    private String ruleName;

    @ApiModelProperty(value = "提前取消小时数", required = true)
    @NotNull(message = "提前取消小时数不能为空")
    @Min(value = 0, message = "提前取消小时数不能小于0")
    @Max(value = 8760, message = "提前取消小时数不能超过8760(一年)")
    private Integer advanceHours;

    @ApiModelProperty(value = "扣费类型:1-按比例,2-固定金额", required = true)
    @NotNull(message = "扣费类型不能为空")
    @Min(value = 1, message = "扣费类型值错误")
    @Max(value = 2, message = "扣费类型值错误")
    private Integer penaltyType;

    @ApiModelProperty(value = "扣费值(比例或金额)", required = true)
    @NotNull(message = "扣费值不能为空")
    @DecimalMin(value = "0.00", message = "扣费值不能小于0")
    // 注意：固定金额扣费时可以超过100，所以移除@DecimalMax限制，在业务层进行分类验证
    private BigDecimal penaltyValue;

    @ApiModelProperty(value = "最小提前取消小时数")
    @Min(value = 0, message = "最小提前取消小时数不能小于0")
    @Max(value = 8760, message = "最小提前取消小时数不能超过8760(一年)")
    private Integer minAdvanceHours;

    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空")
    @Min(value = 0, message = "排序不能小于0")
    @Max(value = 999, message = "排序不能超过999")
    private Integer sort;

    @ApiModelProperty(value = "状态:0-禁用,1-启用", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值错误")
    @Max(value = 1, message = "状态值错误")
    private Integer status;

    @ApiModelProperty(value = "规则描述")
    @Size(max = 500, message = "规则描述长度不能超过500个字符")
    private String description;
}
