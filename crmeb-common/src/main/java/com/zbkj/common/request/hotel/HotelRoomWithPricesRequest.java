package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 酒店房型和价格策略组合请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomWithPricesRequest", description = "酒店房型和价格策略组合请求对象")
public class HotelRoomWithPricesRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房型信息", required = true)
    @NotNull(message = "房型信息不能为空")
    @Valid
    private HotelRoomRequest roomData;

    @ApiModelProperty(value = "价格策略列表", required = true)
    @NotEmpty(message = "价格策略列表不能为空")
    @Valid
    private List<HotelPriceStrategyCreateRequest> priceStrategies;
}
