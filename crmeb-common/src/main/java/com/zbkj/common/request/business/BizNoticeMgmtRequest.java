package com.zbkj.common.request.business;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 公告管理
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@ApiModel(value = "BizNoticeMgmtRequest", description = "公告管理新增、修改对象")
public class BizNoticeMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 公告封面图
     */
    @ApiModelProperty("公告封面图")
    private String noticeCover;

    /**
     * 公告名称
     */
    @ApiModelProperty("公告名称")
    private String noticeTitle;

    /**
     * 公告作者
     */
    @ApiModelProperty("公告作者")
    private String noticeAuthor;

    /**
     * 公告发布时间
     */
    @ApiModelProperty("公告发布时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noticePushTime;

    /**
     * 公告置顶（0、1）数据越大显示在最顶上
     */
    @ApiModelProperty("公告置顶（0、1）数据越大显示在最顶上")
    private Integer noticeTop;

    /**
     * 公告是否在首页显示
     */
    @ApiModelProperty("公告是否在首页显示")
    private String noticeIndexShow;

    /**
     * 公告内容（富文本）
     */
    @ApiModelProperty("公告内容（富文本）")
    private String noticeContent;

    /**
     * 公告描述
     */
    @ApiModelProperty("公告描述")
    private String noticeDesc;

} 