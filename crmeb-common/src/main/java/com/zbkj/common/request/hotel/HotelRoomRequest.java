package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店房型请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomRequest", description = "酒店房型请求对象")
public class HotelRoomRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID(修改时必填)")
    private Integer id;

    @ApiModelProperty(value = "房型名称", required = true)
    @NotBlank(message = "房型名称不能为空")
    @Size(max = 100, message = "房型名称长度不能超过100个字符")
    private String roomName;

    @ApiModelProperty(value = "房型类型(标准间,大床房,套房等)")
    @Size(max = 50, message = "房型类型长度不能超过50个字符")
    private String roomType;

    @ApiModelProperty(value = "房间设施(JSON格式)")
    private String roomFacilities;

    @ApiModelProperty(value = "房间面积(平方米)")
    @DecimalMin(value = "0.00", message = "房间面积不能小于0")
    @DecimalMax(value = "999.99", message = "房间面积不能超过999.99平方米")
    private BigDecimal roomArea;

    @ApiModelProperty(value = "楼层信息")
    @Size(max = 20, message = "楼层信息长度不能超过20个字符")
    private String roomFloor;

    @ApiModelProperty(value = "床型(单人床,双人床,大床等)")
    @Size(max = 50, message = "床型长度不能超过50个字符")
    private String bedType;

    @ApiModelProperty(value = "最大入住人数", required = true)
    @NotNull(message = "最大入住人数不能为空")
    @Min(value = 1, message = "最大入住人数不能小于1")
    @Max(value = 10, message = "最大入住人数不能超过10")
    private Integer maxGuests;

    @ApiModelProperty(value = "房型基础价格", required = true)
    @NotNull(message = "房型基础价格不能为空")
    @DecimalMin(value = "0.01", message = "房型基础价格不能小于0.01")
    @DecimalMax(value = "99999.99", message = "房型基础价格不能超过99999.99")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "总房间数", required = true)
    @NotNull(message = "总房间数不能为空")
    @Min(value = 1, message = "总房间数不能小于1")
    @Max(value = 999, message = "总房间数不能超过999")
    private Integer totalRooms;

    @ApiModelProperty(value = "房间图片(JSON格式)")
    private String roomImages;

    @ApiModelProperty(value = "房间描述")
    @Size(max = 500, message = "房间描述长度不能超过500个字符")
    private String roomDescription;

    @ApiModelProperty(value = "最大可预订天数", required = true)
    @NotNull(message = "最大可预订天数不能为空")
    @Min(value = 1, message = "最大可预订天数不能小于1")
    @Max(value = 365, message = "最大可预订天数不能超过365")
    private Integer maxBookingDays;

    @ApiModelProperty(value = "状态:0-禁用,1-启用", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值错误")
    @Max(value = 1, message = "状态值错误")
    private Integer status;

    @ApiModelProperty(value = "排序")
    @Min(value = 0, message = "排序不能小于0")
    @Max(value = 9999, message = "排序不能超过9999")
    private Integer sort;
}
