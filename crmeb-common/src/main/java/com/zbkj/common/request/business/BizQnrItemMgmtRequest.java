package com.zbkj.common.request.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 问卷调查子项管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "BizQnrItemMgmtRequest", description = "问卷调查子项管理保存对象")
public class BizQnrItemMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 子项模板id
     */
    @ApiModelProperty("子项模板id")
    private String modelId;

    /**
     * 子项一级标题
     */
    @ApiModelProperty("子项一级标题")
    private String itemTitle;

    /**
     * 子项二级标题
     */
    @ApiModelProperty("子项二级标题")
    private String itemSubTitle;

    /**
     * 子项二级所属的一级标题id
     */
    @ApiModelProperty("子项二级所属的一级标题id")
    private String itemParentId;

    /**
     * 子项排序
     */
    @ApiModelProperty("子项排序")
    private Integer itemSort;

    /**
     * 子项类型，参考问卷星
     */
    @ApiModelProperty("子项类型")
    private String itemResultType;

    /**
     * 子项结果选择，多个用逗号分隔（答案和选项值使用label:value的方式存储，例如:  男:A,女:B）
     */
    @ApiModelProperty("子项结果选择")
    private String itemResultOption;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必填")
    private String itemIsRequired;

} 