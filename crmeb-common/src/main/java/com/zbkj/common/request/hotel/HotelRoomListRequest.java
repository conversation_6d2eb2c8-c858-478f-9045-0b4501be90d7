package com.zbkj.common.request.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 酒店房型列表请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomListRequest", description = "酒店房型列表请求对象")
public class HotelRoomListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "酒店ID（商户ID）", required = true)
    @NotNull(message = "酒店ID不能为空")
    private Integer hotelId;

    @ApiModelProperty(value = "入住日期", required = true)
    @NotNull(message = "入住日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDate;

    @ApiModelProperty(value = "离店日期", required = true)
    @NotNull(message = "离店日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDate;

    @ApiModelProperty(value = "房间数量")
    private Integer roomCount = 1;
}
