package com.zbkj.common.request.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 热点管理
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@ApiModel(value = "BizHotspotMgmtRequest", description = "热点管理 保存对象")
public class BizHotspotMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 热点名称
     */
    @ApiModelProperty("热点名称")
    private String hotspotName;

    /**
     * 热点编号
     */
    @ApiModelProperty("热点编号")
    private String hotspotCode;

    /**
     * 热点类型
     */
    @ApiModelProperty("热点类型")
    private String hotspotType;

    /**
     * 热点描述
     */
    @ApiModelProperty("热点描述")
    private String hotspotDesc;

    /**
     * 关联的商家id
     */
    @ApiModelProperty("关联的商家id")
    private String hotspotMerchantId;

    /**
     * 热点vr路径
     */
    @ApiModelProperty("热点vr路径")
    private String hotspotVrUrl;

} 