package com.zbkj.common.request.business;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: BizAllotRecordRequest
 * @Description: 随机分配热点记录参数对象
 * @Author: zlj
 * @Date: 2025-05-28 17:36
 * @Version: 1.0
 **/
@Data
public class BizAllotRecordRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 随机分配热点
     */
    @ApiModelProperty(value = "随机分配热点")
    private String allotRandomHotspot;
    /**
     * 随机分配数量（天/个）可被修改
     */
    @ApiModelProperty(value = "随机分配数量（天/个）可被修改")
    private Integer allotRandomNumber;
    /**
     * 被领取的数量
     */
    @ApiModelProperty(value = "被领取的数量")
    private Integer allotReceiveNumber;
    /**
     * 分配日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "分配日期")
    private Date allotDate;
}
