package com.zbkj.common.request.hotel;

import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 酒店价格策略搜索请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelPriceStrategySearchRequest", description = "酒店价格策略搜索请求对象")
public class HotelPriceStrategySearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "策略名称(模糊搜索)")
    private String strategyName;

    @ApiModelProperty(value = "策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期")
    private Integer strategyType;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;
}
