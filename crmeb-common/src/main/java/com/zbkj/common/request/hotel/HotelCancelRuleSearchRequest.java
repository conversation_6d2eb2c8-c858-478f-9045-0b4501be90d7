package com.zbkj.common.request.hotel;

import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 酒店取消规则搜索请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelCancelRuleSearchRequest", description = "酒店取消规则搜索请求对象")
public class HotelCancelRuleSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则名称(模糊搜索)")
    private String ruleName;

    @ApiModelProperty(value = "扣费类型:1-按比例,2-固定金额")
    private Integer penaltyType;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;
}
