package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 酒店价格日历查询请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelPriceCalendarRequest", description = "酒店价格日历查询请求对象")
public class HotelPriceCalendarRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房间ID", required = true)
    @NotNull(message = "房间ID不能为空")
    private Integer roomId;

    @ApiModelProperty(value = "年份", required = true)
    @NotNull(message = "年份不能为空")
    private Integer year;

    @ApiModelProperty(value = "月份", required = true)
    @NotNull(message = "月份不能为空")
    private Integer month;
}
