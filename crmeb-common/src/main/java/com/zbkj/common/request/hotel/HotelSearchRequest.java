package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店搜索请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelSearchRequest", description = "酒店搜索请求对象")
public class HotelSearchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关键词搜索（酒店名称）")
    private String keyword;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @ApiModelProperty(value = "星级筛选（1-5星）")
    @Min(value = 1, message = "星级不能小于1")
    @Max(value = 5, message = "星级不能大于5")
    private Integer starLevel;

    @ApiModelProperty(value = "最低价格")
    @Min(value = 0, message = "最低价格不能小于0")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最高价格")
    @Min(value = 0, message = "最高价格不能小于0")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "排序类型（1-价格升序，2-价格降序，3-距离，4-评分）")
    @Min(value = 1, message = "排序类型值错误")
    @Max(value = 4, message = "排序类型值错误")
    private Integer sortType;

    @ApiModelProperty(value = "经度（用于距离排序）")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度（用于距离排序）")
    private BigDecimal latitude;
}
