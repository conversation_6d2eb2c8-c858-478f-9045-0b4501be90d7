package com.zbkj.common.request.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 问卷调查填报模板管理
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "BizQnrFillItemMgmtSearchRequest", description = "问卷调查填报模板管理保存对象")
public class BizQnrFillMgmtRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 填报模板id
     */
    @ApiModelProperty("填报模板id")
    private String modelId;

    /**
     * 填报用户
     */
    @ApiModelProperty("填报用户")
    private String fillUser;

    /**
     * 填报用户id
     */
    @ApiModelProperty("填报用户id")
    private String fillUserId;

} 