package com.zbkj.common.request.hotel;

import com.zbkj.common.request.PageParamRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 酒店房型搜索请求对象
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HotelRoomSearchRequest", description = "酒店房型搜索请求对象")
public class HotelRoomSearchRequest extends PageParamRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "房型名称(模糊搜索)")
    private String roomName;

    @ApiModelProperty(value = "房型类型")
    private String roomType;

    @ApiModelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;

    @ApiModelProperty(value = "床型")
    private String bedType;

    @ApiModelProperty(value = "最大入住人数")
    private Integer maxGuests;
}
