package com.zbkj.common.response.busines;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: BizReceiveRecordResponse
 * @Description: 获取记录返回对象
 * @Author: zlj
 * @Date: 2025-05-29 13:50
 * @Version: 1.0
 **/
@Data
public class BizReceiveRecordResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 获取的热点或名称
     */
    @ApiModelProperty(value = "获取的热点或名称")
    private String receiveHotspotName;
    /**
     * 获取的数量
     */
    @ApiModelProperty(value = "获取的数量")
    private Integer receiveNumber;
    /**
     * 获取人的姓名或昵称
     */
    @ApiModelProperty(value = "获取人的姓名或昵称")
    private String receiveUserName;
    /**
     * 获取人的id
     */
    @ApiModelProperty(value = "获取人的id")
    private String receiveUserId;
    /**
     * 获取时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "获取时间")
    private Date receiveTime;

    /**
     * 累计获取的数量
     */
    @ApiModelProperty(value = "累计获取的数量")
    private Integer receiveNumberTotal;
}
