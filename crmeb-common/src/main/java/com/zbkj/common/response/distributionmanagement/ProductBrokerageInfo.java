package com.zbkj.common.response.distributionmanagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品佣金信息响应类
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ProductBrokerageInfo对象", description = "商品佣金信息响应类")
public class ProductBrokerageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "一级佣金比例（%）")
    private BigDecimal firstBrokerageRatio;

    @ApiModelProperty(value = "二级佣金比例（%）")
    private BigDecimal secondBrokerageRatio;

    @ApiModelProperty(value = "一级佣金金额")
    private BigDecimal firstBrokerageAmount;

    @ApiModelProperty(value = "二级佣金金额")
    private BigDecimal secondBrokerageAmount;

    @ApiModelProperty(value = "是否使用全局佣金设置")
    private Boolean isGlobalBrokerage;

    @ApiModelProperty(value = "佣金计算基价（商品价格）")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "分销功能是否开启")
    private Boolean isRetailStoreOpen;
}
