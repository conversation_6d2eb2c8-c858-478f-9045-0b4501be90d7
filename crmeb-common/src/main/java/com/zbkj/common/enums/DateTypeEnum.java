package com.zbkj.common.enums;

import com.zbkj.common.constants.CalendarConstants;

/**
 * 日期类型枚举
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public enum DateTypeEnum {
    
    /**
     * 工作日（周一至周五）
     */
    WORKDAY(CalendarConstants.DATE_TYPE_WORKDAY, "工作日"),
    
    /**
     * 周末（周六周日）
     */
    WEEKEND(CalendarConstants.DATE_TYPE_WEEKEND, "周末"),
    
    /**
     * 节假日（法定节假日）
     */
    HOLIDAY(CalendarConstants.DATE_TYPE_HOLIDAY, "节假日"),
    
    /**
     * 调休补班（调休工作日）
     */
    TRANSFER_WORKDAY(CalendarConstants.DATE_TYPE_TRANSFER_WORKDAY, "调休补班");
    
    private final Integer code;
    private final String desc;
    
    DateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 代码
     * @return 日期类型枚举
     */
    public static DateTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DateTypeEnum dateType : values()) {
            if (dateType.getCode().equals(code)) {
                return dateType;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为工作日（包含调休补班）
     * @return 是否为工作日
     */
    public boolean isWorkday() {
        return this == WORKDAY || this == TRANSFER_WORKDAY;
    }
    
    /**
     * 判断是否为休息日（周末或节假日）
     * @return 是否为休息日
     */
    public boolean isRestDay() {
        return this == WEEKEND || this == HOLIDAY;
    }
    
    /**
     * 判断是否为自然周末
     * @return 是否为自然周末
     */
    public boolean isNaturalWeekend() {
        return this == WEEKEND;
    }
    
    /**
     * 判断是否为法定节假日
     * @return 是否为法定节假日
     */
    public boolean isLegalHoliday() {
        return this == HOLIDAY;
    }
}
