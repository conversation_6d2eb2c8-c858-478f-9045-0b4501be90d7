{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?72f8", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?8573", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?eaa6", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?0ed1", "uni-app:///pages/me/questionnaire.vue", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?4bd4", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?71ef", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?0991", "webpack:///D:/dljs-app/pages/me/questionnaire.vue?82b5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniDataPicker", "uniDataSelect", "data", "questionnaire", "modelTitle", "modelDesc", "itemList", "id", "answers", "validationErrors", "hasSubmitted", "isQuestionnaireEditable", "submitState", "created", "onLoad", "watch", "handler", "Object", "questionId", "answer", "oldAnswers", "JSON", "deep", "methods", "getQuestionnaireFun", "uni", "title", "res", "templateCode", "item", "response", "answersFromApi", "answerData", "value", "questionType", "console", "initAnswers", "initialValue", "question", "Array", "handleMatrixRadioChange", "isMatrixRadioChecked", "handleMatrixCheckboxChange", "validateQuestionnaire", "firstErrorQuestion", "validateQuestion", "isEmpty", "rows", "anyRowFilled", "allRowsFilled", "multiRows", "anyMultiRowFilled", "allMultiRowsFilled", "submitQuestionnaire", "selector", "duration", "itemId", "fillResult", "modelId", "then", "code", "message", "icon", "setTimeout", "url", "catch", "noConflict", "updateTextInput", "handleRadioChange", "handleCheckboxChange", "handleRateChange", "handleDropdownChange", "handleRegionChange", "text", "debugValidation", "debugRegionData", "isSelected", "debugMatrixData", "isFilled", "handleMatrixCellClick", "handleMatrixCheckboxCellClick", "currentV<PERSON>ues"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;AACzB;;;AAGzE;AACyK;AACzK,gBAAgB,sLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,4WAEN;AACP,KAAK;AACL;AACA,aAAa,yQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/KA;AAAA;AAAA;AAAA;AAAwoB,CAAgB,oqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACoN5pB;AAIA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAP;MACAQ;QAAA;QACA;QACA;;QAEA;QACAC;UAAA;UAAA;YAAAC;YAAAC;UACA;UACA,IACAC,cACAA,0BACAC,uLACA;YACA;cAAA;YAAA;YACA;YACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA,YACAA;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACA;gBACAH;gBAAA;cAAA;gBAGA;gBACA;gBACA;kBACAI;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,iBACAA;gBACA;kBACA;kBACA;kBACA;kBACAC,qBAEA;kBACAC;oBACA;sBACA;sBACA;sBACA;wBACAC;sBACA;wBACAA;sBACA;;sBAEA;sBACAF;wBACAE;wBACAC;wBAAA;wBACAhB;sBACA;oBACA;sBACAiB;oBACA;kBACA;;kBAEA;kBACA;oBACAA;oBACA;oBACAA;;oBAEA;oBACA;sBACAA;oBACA;kBACA;gBACA;kBACA;kBACAV;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAA;cAAA;gBAGA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;gBAAA;gBAEAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAW;MACA;QACA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;QACA;QAEA;UACA;UACA;;UAEA;UACA;UACA;YACA;cAAA;cACAC;cACA;YACA;cAAA;cACAA;cACA;YACA;cAAA;cACAA;cACA;YACA;cAAA;cACAA;cACA;YACA;cAAA;cACAA;cACA;YACA;cAAA;cACAA;cACA;YACA;cAAA;cACA;cACAA;cACA,IACAC,6BACAA,kCACAC,+CACA;gBACAD;kBACA;oBACAD;kBACA;gBACA;cACA;cACA;YACA;cAAA;cACA;cACAA;cACA,IACAC,6BACAA,kCACAC,+CACA;gBACAD;kBACA;oBACAD;kBACA;gBACA;cACA;cACA;YACA;cACAA;UAAA;;UAGA;UACA7B;YACAU;YACAgB;YACAD;UACA;;UAEA;UACAxB;QACA;QAEA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACA+B;MAAA;MACA;QACA;UAAA;QAAA;QACA,IACA,aACA,8BACA,mCACA,2CAEA;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;YACA;YACA;cACA;cACA;cACA;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA,IACA,aACA,8BACA,mCACA,2CAEA;QAEA;QACA;QAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UAAA;QAAA;QACA,IACA,aACA,8BACA,mCACA,2CAEA;QAEA;;QAEA;QACA;;QAEA;QACA;UACA;YACA;YACA,kDACA;cAAA,OACA,gDACA;YAAA,EACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA1B;UACAR;QACA;QACA;QAEA;;QAEA;QAAA,2CACA;UAAA;QAAA;UAAA;YAAA;YACA;YAEA;cACA;cACA;cAEA;gBACA;gBACA;;gBAEA;gBACA;kBACAmC;gBACA;cACA;YACA;UACA;;UAEA;QAAA;UAAA;QAAA;UAAA;QAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;;QAEA;QACA;QAEA;UACA;YAAA;YACAC;YACA;UACA;YAAA;YACAA;YACA;UACA;YAAA;YACAA;YACA;UACA;YAAA;YACAA;YACA;UACA;YAAA;YACAA;YACA;UACA;YAAA;YACAA;YACA;UACA;YAAA;YACA;YACA;YACA,IACA,8BACA,mCACA,gDACA;cACA;YACA;;YAEA;YACAA;;YAEA;YACA;cACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YAAA,4CACAC;cAAA;YAAA;cAAA;gBAAA;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;gBACA;gBAEA;kBACAC;gBACA;kBACAC;gBACA;cACA;;cAEA;YAAA;cAAA;YAAA;cAAA;YAAA;YACAH;YACA;UAEA;YAAA;YACA;;YAEA;YACA,IACA,8BACA,mCACA,gDACA;cACA;YACA;;YAEA;YACAA;;YAEA;YACA;cACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YAAA,4CACAI;cAAA;YAAA;cAAA;gBAAA;gBACA;;gBAEA;gBACA;gBACA;gBAEA;kBACAC;gBACA;kBACAC;gBACA;cACA;;cAEA;YAAA;cAAA;YAAA;cAAA;YAAA;YACAN;YACA;UACA;YACAA;UAAA;QAAA;;QAGA;MACA;QACA;MACA;IACA;IAEA;IACAO;MAAA;MACA;MACA;MACA5B;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;;QAEA;QACA;QAEA;UACA;UACA;;UAEA;UACAD;YACA6B;YACAC;UACA;UAEA;QACA;QACA;QACA,iEACA;UAAA;YAAArC;YAAAC;UACA;YACAqC;YACAC;YACAvB;YACAwB;UACA;QACA,EACA;QAEA,4DACAC;UACAlC;UACA;YAAAmC;YAAAC;UACA;YACApC;YACA;UACA;UACA;UACA;UACAA;YACAC;YACAoC;YACAP;UACA;UACA;UACAQ;YACAtC;cACAuC;YACA;YACA;UACA;QACA,GACAC;UACAxC;YACAyC;UACA;UACAzC;QACA;MACA;QACAA;UACAC;UACAoC;QACA;MACA;QACArC;UACAyC;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;QAEA;;QAEA;QACA;QACA,IACAvC,SACA,6CACAA,gBACAM,mCACA;UACAO;;UAEA;UACA;YACA;YACA;YACAb;cAAA;YAAA;YACA;YACAA;UACA;QACA;;QAEA;QACA;UAAAA;UAAAwC;QAAA;QACA;UACA;UACA;QACA;MACA;QACAtC;MACA;IACA;IAEA;IACAuC;MACA;QAAA;MAAA;MACA;QACA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;;MAEA;MACA;MACA;QACAC;MACA;MACA;MAAA,KACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MACA;MAAA,KACA;QACAA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;;QAEA;QACA,uDACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;MAEA;MAEA;MAEA;QACA;MACA;;MAEA;MACAvC;QACA;QAEA;QACA;QAEA;UACA;UACAwC;QACA;UACA;UACAA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;UAAA;QAAA;QACA,IACA,aACA,8BACA,mCACA,2CAEA;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;QACA5C;MACA;IACA;IAEA;IACA6C;MACA;MACA;MAEA;QACA;UAAA;QAAA;QACA,IACA,aACA,8BACA,mCACA,2CAEA;QAEA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACAC;QACA;UACAA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;YACA;YACA;UACA;QACA;MACA;QACA9C;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5kCA;AAAA;AAAA;AAAA;AAAmuC,CAAgB,+sCAAG,EAAC,C;;;;;;;;;;;ACAvvC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAu6B,CAAgB,26BAAG,EAAC,C;;;;;;;;;;;ACA37B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me/questionnaire.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me/questionnaire.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./questionnaire.vue?vue&type=template&id=03ebdbe5&scoped=true&\"\nvar renderjs\nimport script from \"./questionnaire.vue?vue&type=script&lang=js&\"\nexport * from \"./questionnaire.vue?vue&type=script&lang=js&\"\nimport style0 from \"./questionnaire.vue?vue&type=style&index=0&id=03ebdbe5&lang=scss&scoped=true&\"\nimport style1 from \"./questionnaire.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"03ebdbe5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me/questionnaire.vue\"\nexport default component.exports", "export * from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=template&id=03ebdbe5&scoped=true&\"", "var components\ntry {\n  components = {\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-parse/u-parse\" */ \"@/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-radio/u-radio\" */ \"@/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"@/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-textarea/u-textarea\" */ \"@/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uRate: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-rate/u-rate\" */ \"@/uview-ui/components/u-rate/u-rate.vue\"\n      )\n    },\n    uniDataSelect: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select\" */ \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\n      )\n    },\n    uniDataPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker\" */ \"@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-button/u-button\" */ \"@/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.questionnaire.itemList, function (question, __i0__) {\n    var $orig = _vm.__get_orig(question)\n    var g0 = _vm.questionnaire.itemList.indexOf(question)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function (value, question) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        question = _temp2.question\n      var _temp, _temp2\n      return _vm.handleRadioChange(question.id, value)\n    }\n    _vm.e1 = function (value, question) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp3 = args[args.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        question = _temp4.question\n      var _temp3, _temp4\n      return _vm.handleCheckboxChange(question.id, value)\n    }\n    _vm.e2 = function (value, question) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp5 = args[args.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        question = _temp6.question\n      var _temp5, _temp6\n      return _vm.handleRateChange(question.id, value)\n    }\n    _vm.e3 = function (value, question) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp7 = args[args.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        question = _temp8.question\n      var _temp7, _temp8\n      return _vm.handleDropdownChange(question.id, value)\n    }\n    _vm.e4 = function (value, question) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp9 = args[args.length - 1].currentTarget.dataset,\n        _temp10 = _temp9.eventParams || _temp9[\"event-params\"],\n        question = _temp10.question\n      var _temp9, _temp10\n      return _vm.handleRegionChange(question.id, value)\n    }\n    _vm.e5 = function (value, question, rowIndex) {\n      var args = [],\n        len = arguments.length - 3\n      while (len-- > 0) args[len] = arguments[len + 3]\n\n      var _temp11 = args[args.length - 1].currentTarget.dataset,\n        _temp12 = _temp11.eventParams || _temp11[\"event-params\"],\n        question = _temp12.question,\n        rowIndex = _temp12.rowIndex\n      var _temp11, _temp12\n      return _vm.handleMatrixRadioChange(question.id, rowIndex, value)\n    }\n    _vm.e6 = function (value, question, rowIndex) {\n      var args = [],\n        len = arguments.length - 3\n      while (len-- > 0) args[len] = arguments[len + 3]\n\n      var _temp13 = args[args.length - 1].currentTarget.dataset,\n        _temp14 = _temp13.eventParams || _temp13[\"event-params\"],\n        question = _temp14.question,\n        rowIndex = _temp14.rowIndex\n      var _temp13, _temp14\n      return _vm.handleMatrixCheckboxChange(question.id, rowIndex, value)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"questionnaire-container\">\r\n\t\t<!-- 问卷头部 -->\r\n\t\t<view class=\"questionnaire-header\">\r\n\t\t\t<text class=\"questionnaire-title\">{{ questionnaire.modelTitle || \"\" }}</text>\r\n\t\t\t<!-- <view class=\"questionnaire-desc\"></view> -->\r\n\t\t\t<u-parse :content=\"questionnaire.modelDesc\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 问题列表 -->\r\n\t\t<view class=\"question-list\">\r\n\t\t\t<view\r\n\t\t\t\tv-for=\"question in questionnaire.itemList\"\r\n\t\t\t\t:key=\"question.id\"\r\n\t\t\t\tclass=\"question-item\"\r\n\t\t\t\t:id=\"`question-${question.id}`\">\r\n\t\t\t\t<!-- 问题标题 -->\r\n\t\t\t\t<view class=\"question-title\">\r\n\t\t\t\t\t<text v-if=\"question.itemIsRequired == 1\" class=\"question-required\">*</text>\r\n\t\t\t\t\t<text class=\"question-index\">{{ questionnaire.itemList.indexOf(question) + 1 }}、</text>\r\n\t\t\t\t\t<text class=\"question-text\">{{ question.itemTitle }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 单选题 -->\r\n\t\t\t\t<view v-if=\"question.itemResultType === 'single-choice'\" class=\"question-content\">\r\n\t\t\t\t\t<u-radio-group\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t@change=\"(value) => handleRadioChange(question.id, value)\"\r\n\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\tplacement=\"column\">\r\n\t\t\t\t\t\t<u-radio\r\n              :customStyle=\"{\r\n                marginRight: '30px',\r\n                padding: '8rpx 0'\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\tv-for=\"(option, optIndex) in question.itemResultOption\"\r\n\t\t\t\t\t\t\t:key=\"optIndex\"\r\n\t\t\t\t\t\t\t:name=\"option.value\"\r\n\t\t\t\t\t\t\t:label=\"option.text\"\r\n              labelSize=\"30rpx\"\r\n              iconSize=\"36rpx\"></u-radio>\r\n\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 多选题 -->\r\n\t\t\t\t<view v-else-if=\"question.itemResultType === 'multiple-choice'\" class=\"question-content\">\r\n\t\t\t\t\t<u-checkbox-group\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t@change=\"(value) => handleCheckboxChange(question.id, value)\"\r\n\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\tplacement=\"column\">\r\n\t\t\t\t\t\t<u-checkbox\r\n              :customStyle=\"{\r\n                marginRight: '30px',\r\n                padding: '8rpx 0'\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\tv-for=\"(option, optIndex) in question.itemResultOption\"\r\n\t\t\t\t\t\t\t:key=\"optIndex\"\r\n\t\t\t\t\t\t\t:name=\"option.value\"\r\n\t\t\t\t\t\t\t:label=\"option.text\"\r\n              labelSize=\"30rpx\"\r\n              iconSize=\"36rpx\"></u-checkbox>\r\n\t\t\t\t\t</u-checkbox-group>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 文本域 -->\r\n\t\t\t\t<view v-else-if=\"question.itemResultType === 'text'\">\r\n\t\t\t\t\t<u-textarea\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t@input=\"updateTextInput(question.id, $event)\"\r\n\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\tplaceholder=\"请输入\"\r\n\t\t\t\t\t\t:maxlength=\"question.maxlength || 200\"></u-textarea>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 评分题 -->\r\n\t\t\t\t<view v-else-if=\"question.itemResultType === 'nps-scale'\" class=\"question-content\">\r\n\t\t\t\t\t<u-rate\r\n            :customStyle=\"{\r\n              width: '100%',\r\n              justifyContent: 'space-between',\r\n              padding: '20rpx 0'\r\n            }\"\r\n            :size=\"26\"\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t@change=\"(value) => handleRateChange(question.id, value)\"\r\n\t\t\t\t\t\t:readonly=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\t:count=\"question.itemResultOption.end || 5\"></u-rate>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 选择题 -->\r\n\t\t\t\t<view v-else-if=\"question.itemResultType === 'dropdown'\">\r\n\t\t\t\t\t<uni-data-select\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t:localdata=\"question.itemResultOption\"\r\n\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\t@change=\"(value) => handleDropdownChange(question.id, value)\">\r\n\t\t\t\t\t</uni-data-select>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 地区选择题 -->\r\n\t\t\t\t<view v-else-if=\"question.itemResultType === 'region-select'\">\r\n\t\t\t\t\t<uni-data-picker\r\n\t\t\t\t\t\tv-model=\"answers[question.id].value\"\r\n\t\t\t\t\t\t:localdata=\"question.itemResultOption\"\r\n\t\t\t\t\t\t:readonly=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\tplaceholder=\"请选择\"\r\n\t\t\t\t\t\t@change=\"(value) => handleRegionChange(question.id, value)\">\r\n\t\t\t\t\t</uni-data-picker>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 单选矩阵 -->\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-else-if=\"question.itemResultType === 'matrix-single'\"\r\n\t\t\t\t\tclass=\"question-content matrix-simple\">\r\n\t\t\t\t\t<!-- 选项标题行 -->\r\n\t\t\t\t\t<view class=\"matrix-simple-header\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(column, colIndex) in question.itemResultOption.cols\"\r\n\t\t\t\t\t\t\t:key=\"colIndex\"\r\n\t\t\t\t\t\t\tclass=\"matrix-simple-column\">\r\n\t\t\t\t\t\t\t{{ column.text }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 问题行 -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(row, rowIndex) in question.itemResultOption.rows\"\r\n\t\t\t\t\t\t:key=\"rowIndex\"\r\n\t\t\t\t\t\tclass=\"matrix-simple-row\">\r\n\t\t\t\t\t\t<view class=\"matrix-simple-row-title\">{{ row.text }}</view>\r\n\t\t\t\t\t\t<view class=\"matrix-simple-options\">\r\n\t\t\t\t\t\t\t<u-radio-group\r\n\t\t\t\t\t\t\t\t:value=\"answers[question.id].value[row.value]\"\r\n\t\t\t\t\t\t\t\t@change=\"(value) => handleMatrixRadioChange(question.id, rowIndex, value)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\t\t\tplacement=\"row\">\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tv-for=\"(column, colIndex) in question.itemResultOption.cols\"\r\n\t\t\t\t\t\t\t\t\t:key=\"colIndex\"\r\n\t\t\t\t\t\t\t\t\tclass=\"matrix-simple-option\">\r\n                  <u-radio\r\n                      :name=\"column.value\"\r\n                      :customStyle=\"{ marginBottom: '0' }\"\r\n                      iconSize=\"40rpx\"\r\n                      labelSize=\"28rpx\"> </u-radio>\r\n                  <view class=\"radio-overlay\" @tap=\"handleMatrixCellClick(question.id, rowIndex, column.value)\">\r\n                  </view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 多选矩阵 -->\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-else-if=\"question.itemResultType === 'matrix-multiple'\"\r\n\t\t\t\t\tclass=\"question-content matrix-simple\">\r\n\t\t\t\t\t<view class=\"matrix-checkbox-header\">\r\n\t\t\t\t\t\t<view class=\"matrix-simple-options\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tv-for=\"(column, colIndex) in question.itemResultOption.cols\"\r\n\t\t\t\t\t\t\t\t:key=\"colIndex\"\r\n\t\t\t\t\t\t\t\tclass=\"matrix-simple-column\">\r\n\t\t\t\t\t\t\t\t{{ column.text }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 问题行 -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(row, rowIndex) in question.itemResultOption.rows\"\r\n\t\t\t\t\t\t:key=\"rowIndex\"\r\n\t\t\t\t\t\tclass=\"matrix-checkbox-row\">\r\n\t\t\t\t\t\t<view class=\"matrix-checkbox-row-title\">{{ row.text }}</view>\r\n\t\t\t\t\t\t<view class=\"matrix-checkbox-options\">\r\n\t\t\t\t\t\t\t<u-checkbox-group\r\n\t\t\t\t\t\t\t\t:value=\"answers[question.id].value[row.value] || []\"\r\n\t\t\t\t\t\t\t\t@change=\"(value) => handleMatrixCheckboxChange(question.id, rowIndex, value)\"\r\n\t\t\t\t\t\t\t\t:disabled=\"!isQuestionnaireEditable\"\r\n\t\t\t\t\t\t\t\tplacement=\"row\">\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tv-for=\"(column, colIndex) in question.itemResultOption.cols\"\r\n\t\t\t\t\t\t\t\t\t:key=\"colIndex\"\r\n\t\t\t\t\t\t\t\t\tclass=\"matrix-checkbox-option\">\r\n\t\t\t\t\t\t\t\t\t<u-checkbox :name=\"column.value\" :customStyle=\"{ marginBottom: '0' }\">\r\n\t\t\t\t\t\t\t\t\t</u-checkbox>\r\n                  <!-- 添加透明覆盖层，增大点击区域 -->\r\n                  <view\r\n                      class=\"checkbox-overlay\"\r\n                      @tap=\"handleMatrixCheckboxCellClick(question.id, rowIndex, column.value)\">\r\n                  </view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</u-checkbox-group>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 统一错误提示 -->\r\n\t\t\t\t<view v-if=\"hasSubmitted && validationErrors[question.id]\" class=\"error-tip\">\r\n\t\t\t\t\t<text>此题为必填项，请回答此题</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 提交按钮 -->\r\n\t\t<view class=\"submit-area\" v-if=\"isQuestionnaireEditable\">\r\n\t\t\t<u-button type=\"primary\" :disabled=\"submitState\" :text=\"submitState ? '正在提交问卷，请稍后...' :'提交问卷'\" @click=\"submitQuestionnaire\"></u-button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tgetQuestionnaire,\r\n\tgetQuestionnaireIsFill,\r\n\tsubmitQuestionnaire,\r\n} from \"@/nxTemp/apis/common\";\r\nimport uniDataPicker from \"@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue\";\r\nimport uniDataSelect from \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\";\r\nexport default {\r\n\tcomponents: {\r\n\t\tuniDataPicker,\r\n\t\tuniDataSelect,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 问卷数据\r\n\t\t\tquestionnaire: {\r\n\t\t\t\tmodelTitle: \"\",\r\n\t\t\t\tmodelDesc: \"\",\r\n\t\t\t\titemList: [],\r\n\t\t\t\tid: \"\",\r\n\t\t\t},\r\n\t\t\t// 答案数据 - 改为基于id的对象映射\r\n\t\t\tanswers: {},\r\n\t\t\t// 验证错误状态 - 改为基于id的对象映射，true表示有错误\r\n\t\t\tvalidationErrors: {},\r\n\t\t\t// 是否已提交过，用于控制错误提示的显示\r\n\t\t\thasSubmitted: false,\r\n\t\t\t// 是否可编辑问卷\r\n\t\t\tisQuestionnaireEditable: true,\r\n      submitState: false,\r\n\t\t};\r\n\t},\r\n\tcreated() {\r\n\t\t// 初始化答案数组\r\n\t\tthis.initAnswers();\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.getQuestionnaireFun();\r\n\t},\r\n\twatch: {\r\n\t\t// 监听答案变化，实时验证并更新错误状态\r\n\t\tanswers: {\r\n\t\t\thandler(newAnswers, oldAnswers) {\r\n\t\t\t\t// 只有在已提交后才进行验证\r\n\t\t\t\tif (!this.hasSubmitted) return;\r\n\r\n\t\t\t\t// 遍历检查每个答案是否发生变化\r\n\t\t\t\tObject.entries(newAnswers).forEach(([questionId, answer]) => {\r\n\t\t\t\t\t// 如果不是初始化阶段且答案发生变化\r\n\t\t\t\t\tif (\r\n\t\t\t\t\t\toldAnswers &&\r\n\t\t\t\t\t\toldAnswers[questionId] &&\r\n\t\t\t\t\t\tJSON.stringify(answer.value) !== JSON.stringify(oldAnswers[questionId]?.value)\r\n\t\t\t\t\t) {\r\n\t\t\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\t\t\t// 只对必填题进行验证\r\n\t\t\t\t\t\tif (question && question.itemIsRequired == 1) {\r\n\t\t\t\t\t\t\t// 验证问题并更新错误状态\r\n\t\t\t\t\t\t\tconst isEmpty = this.validateQuestion(question);\r\n\t\t\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdeep: true,\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取问卷调查模板数据\r\n\t\tasync getQuestionnaireFun() {\r\n\t\t\ttry {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: \"加载中...\",\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 先获取问卷模板\r\n\t\t\t\tconst res = await getQuestionnaire();\r\n\t\t\t\tconst { data: templateData, code: templateCode, message: templateMessage } = res.data;\r\n\r\n\t\t\t\tif (templateCode !== 200) {\r\n\t\t\t\t\t// 直接使用接口返回的message进行提示\r\n\t\t\t\t\tuni.$u.toast(templateMessage || \"获取失败\");\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 设置问卷模板数据\r\n\t\t\t\tthis.questionnaire = templateData || {};\r\n\t\t\t\tthis.questionnaire?.itemList?.forEach((item) => {\r\n\t\t\t\t\titem.itemResultOption = JSON.parse(item.itemResultOption);\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 检查用户是否已填写问卷\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst response = await getQuestionnaireIsFill(templateData.id);\r\n\t\t\t\t\tconst { data: answerData, code, message } = response.data;\r\n\t\t\t\t\tif (code === 200 && answerData.list && answerData.list.length > 0) {\r\n\t\t\t\t\t\t// 标记问卷为不可编辑\r\n\t\t\t\t\t\tthis.isQuestionnaireEditable = false;\r\n\t\t\t\t\t\t// 遍历答案列表，根据itemId找到对应的题目，设置fillResult为答案\r\n\t\t\t\t\t\tconst answersFromApi = {};\r\n\r\n\t\t\t\t\t\t// 确保答案数据格式正确\r\n\t\t\t\t\t\tanswerData.list.forEach((item) => {\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t// 尝试解析JSON字符串，如果失败则使用原始值\r\n\t\t\t\t\t\t\t\tlet value;\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tvalue = JSON.parse(item.fillResult);\r\n\t\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\t\tvalue = item.fillResult;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 存储到临时对象中\r\n\t\t\t\t\t\t\t\tanswersFromApi[item.itemId] = {\r\n\t\t\t\t\t\t\t\t\tvalue: value,\r\n\t\t\t\t\t\t\t\t\tquestionType: item.itemResultType, // 默认为文本类型\r\n\t\t\t\t\t\t\t\t\tquestionId: item.itemId,\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\tconsole.error(\"处理答案数据出错:\", e);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 一次性赋值，避免多次触发watch\r\n\t\t\t\t\t\tif (Object.keys(answersFromApi).length > 0) {\r\n\t\t\t\t\t\t\tconsole.log(\"设置答案数据前:\", Object.keys(answersFromApi));\r\n\t\t\t\t\t\t\tthis.answers = answersFromApi;\r\n\t\t\t\t\t\t\tconsole.log(\"设置答案数据后:\", this.answers);\r\n\r\n\t\t\t\t\t\t\t// 确保数据能够正确显示到页面上\r\n\t\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"nextTick中的答案数据:\", this.answers);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (code !== 200) {\r\n\t\t\t\t\t\t// 获取答案数据失败，但可以继续使用模板\r\n\t\t\t\t\t\tuni.$u.toast(message || \"获取失败\");\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (answerErr) {\r\n\t\t\t\t\t// 获取答案数据出错，但可以继续使用模板\r\n\t\t\t\t\tuni.$u.toast(answerErr.message || \"获取失败\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果没有获取到答案数据或获取失败，初始化空白答案数组\r\n\t\t\t\tif (!this.answers || Object.keys(this.answers).length === 0) {\r\n\t\t\t\t\tthis.initAnswers();\r\n\t\t\t\t}\r\n\t\t\t} catch (err) {\r\n\t\t\t\tuni.$u.toast(err.message || \"获取失败\");\r\n\t\t\t} finally {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 初始化答案数组\r\n\t\tinitAnswers() {\r\n\t\t\ttry {\r\n\t\t\t\t// 确保 questionnaire.itemList 存在且是数组\r\n\t\t\t\tif (!this.questionnaire.itemList || !Array.isArray(this.questionnaire.itemList)) {\r\n\t\t\t\t\tthis.answers = {};\r\n\t\t\t\t\tthis.validationErrors = {};\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 创建基于id的答案对象\r\n\t\t\t\tconst answers = {};\r\n\t\t\t\tconst validationErrors = {};\r\n\r\n\t\t\t\tthis.questionnaire.itemList.forEach((question) => {\r\n\t\t\t\t\t// 确保问题有id\r\n\t\t\t\t\tif (!question.id) return;\r\n\r\n\t\t\t\t\t// 根据问题类型设置初始值\r\n\t\t\t\t\tlet initialValue;\r\n\t\t\t\t\tswitch (question.itemResultType) {\r\n\t\t\t\t\t\tcase \"single-choice\": // 单选题\r\n\t\t\t\t\t\t\tinitialValue = \"\";\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"multiple-choice\": // 多选题\r\n\t\t\t\t\t\t\tinitialValue = [];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"text\": // 文本框\r\n\t\t\t\t\t\t\tinitialValue = \"\";\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"nps-scale\": // 评分\r\n\t\t\t\t\t\t\tinitialValue = 0;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"dropdown\": // 下拉选择\r\n\t\t\t\t\t\t\tinitialValue = \"\";\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"region-select\": // 地区选择\r\n\t\t\t\t\t\t\tinitialValue = \"\";\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"matrix-single\": // 矩阵单选\r\n\t\t\t\t\t\t\t// 使用对象形式，以行的value为键\r\n\t\t\t\t\t\t\tinitialValue = {};\r\n\t\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption &&\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption.rows &&\r\n\t\t\t\t\t\t\t\tArray.isArray(question.itemResultOption.rows)\r\n\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption.rows.forEach((row) => {\r\n\t\t\t\t\t\t\t\t\tif (row && row.value) {\r\n\t\t\t\t\t\t\t\t\t\tinitialValue[row.value] = \"\";\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase \"matrix-multiple\": // 矩阵多选\r\n\t\t\t\t\t\t\t// 使用对象形式，以行的value为键\r\n\t\t\t\t\t\t\tinitialValue = {};\r\n\t\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption &&\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption.rows &&\r\n\t\t\t\t\t\t\t\tArray.isArray(question.itemResultOption.rows)\r\n\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\tquestion.itemResultOption.rows.forEach((row) => {\r\n\t\t\t\t\t\t\t\t\tif (row && row.value) {\r\n\t\t\t\t\t\t\t\t\t\tinitialValue[row.value] = [];\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\tinitialValue = \"\";\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用问题ID作为键存储答案\r\n\t\t\t\t\tanswers[question.id] = {\r\n\t\t\t\t\t\tquestionId: question.id,\r\n\t\t\t\t\t\tquestionType: question.itemResultType,\r\n\t\t\t\t\t\tvalue: initialValue,\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// 初始化验证错误状态为false（无错误）\r\n\t\t\t\t\tvalidationErrors[question.id] = false;\r\n\t\t\t\t});\r\n\r\n\t\t\t\tthis.answers = answers;\r\n\t\t\t\tthis.validationErrors = validationErrors;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.answers = {};\r\n\t\t\t\tthis.validationErrors = {};\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理单选矩阵题型的变化\r\n\t\thandleMatrixRadioChange(questionId, rowIndex, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (\r\n\t\t\t\t\t!question ||\r\n\t\t\t\t\t!question.itemResultOption ||\r\n\t\t\t\t\t!question.itemResultOption.rows ||\r\n\t\t\t\t\t!question.itemResultOption.rows[rowIndex]\r\n\t\t\t\t)\r\n\t\t\t\t\treturn;\r\n\r\n\t\t\t\tconst rowValue = question.itemResultOption.rows[rowIndex].value;\r\n\t\t\t\tif (!this.answers[questionId] || !this.answers[questionId].value) return;\r\n\r\n\t\t\t\t// 记录选择前的值\r\n\t\t\t\tconst oldValue = this.answers[questionId].value[rowValue];\r\n\r\n\t\t\t\t// 如果当前值已经被选中，则取消选择\r\n\t\t\t\tif (this.answers[questionId].value[rowValue] === value) {\r\n\t\t\t\t\tthis.$set(this.answers[questionId].value, rowValue, \"\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 否则更新为新的选中值\r\n\t\t\t\t\tthis.$set(this.answers[questionId].value, rowValue, value);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 验证更新\r\n\t\t\t\tif (this.hasSubmitted) {\r\n\t\t\t\t\tif (question && question.itemIsRequired == 1) {\r\n\t\t\t\t\t\t// 改进的检查，只判断是否为undefined或null\r\n\t\t\t\t\t\tconst isEmpty = question.itemResultOption.rows.some((row) => {\r\n\t\t\t\t\t\t\tconst val = this.answers[questionId].value[row.value];\r\n\t\t\t\t\t\t\t// 恢复对空字符串的检查，与validateQuestion保持一致\r\n\t\t\t\t\t\t\tconst empty = val === undefined || val === null || val === \"\";\r\n\t\t\t\t\t\t\treturn empty;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 检查单选矩阵题型是否被选中\r\n\t\tisMatrixRadioChecked(questionId, rowIndex, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (\r\n\t\t\t\t\t!question ||\r\n\t\t\t\t\t!question.itemResultOption ||\r\n\t\t\t\t\t!question.itemResultOption.rows ||\r\n\t\t\t\t\t!question.itemResultOption.rows[rowIndex]\r\n\t\t\t\t)\r\n\t\t\t\t\treturn false;\r\n\r\n\t\t\t\tconst rowValue = question.itemResultOption.rows[rowIndex].value;\r\n\t\t\t\tif (!this.answers[questionId] || !this.answers[questionId].value) return false;\r\n\r\n\t\t\t\treturn this.answers[questionId].value[rowValue] === value;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理多选矩阵题型的变化\r\n\t\thandleMatrixCheckboxChange(questionId, rowIndex, values) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (\r\n\t\t\t\t\t!question ||\r\n\t\t\t\t\t!question.itemResultOption ||\r\n\t\t\t\t\t!question.itemResultOption.rows ||\r\n\t\t\t\t\t!question.itemResultOption.rows[rowIndex]\r\n\t\t\t\t)\r\n\t\t\t\t\treturn;\r\n\r\n\t\t\t\tconst rowValue = question.itemResultOption.rows[rowIndex].value;\r\n\r\n\t\t\t\t// 直接更新对应行的值\r\n\t\t\t\tthis.$set(this.answers[questionId].value, rowValue, values);\r\n\r\n\t\t\t\t// 验证更新\r\n\t\t\t\tif (this.hasSubmitted) {\r\n\t\t\t\t\tif (question && question.itemIsRequired == 1) {\r\n\t\t\t\t\t\t// 检查是否每一行都至少有一个选择\r\n\t\t\t\t\t\tconst isEmpty = question.itemResultOption.rows.some(\r\n\t\t\t\t\t\t\t(row) =>\r\n\t\t\t\t\t\t\t\t!this.answers[questionId].value[row.value] ||\r\n\t\t\t\t\t\t\t\tthis.answers[questionId].value[row.value].length === 0\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 验证问卷\r\n\t\tvalidateQuestionnaire() {\r\n\t\t\ttry {\r\n\t\t\t\t// 重置验证状态\r\n\t\t\t\tconst validationErrors = {};\r\n\t\t\t\tObject.keys(this.answers).forEach((questionId) => {\r\n\t\t\t\t\tvalidationErrors[questionId] = false;\r\n\t\t\t\t});\r\n\t\t\t\tthis.validationErrors = validationErrors;\r\n\r\n\t\t\t\tlet firstErrorQuestion = null;\r\n\r\n\t\t\t\t// 检查必填项\r\n\t\t\t\tfor (const question of this.questionnaire.itemList) {\r\n\t\t\t\t\tif (!question || !question.id) continue;\r\n\r\n\t\t\t\t\tif (question.itemIsRequired == 1) {\r\n\t\t\t\t\t\t// 验证问题\r\n\t\t\t\t\t\tconst isEmpty = this.validateQuestion(question);\r\n\r\n\t\t\t\t\t\tif (isEmpty) {\r\n\t\t\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\t\t\tthis.$set(this.validationErrors, question.id, true);\r\n\r\n\t\t\t\t\t\t\t// 记录第一个错误项\r\n\t\t\t\t\t\t\tif (!firstErrorQuestion) {\r\n\t\t\t\t\t\t\t\tfirstErrorQuestion = question;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 返回第一个错误项\r\n\t\t\t\treturn firstErrorQuestion;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 验证单个问题是否已完成\r\n\t\tvalidateQuestion(question) {\r\n\t\t\ttry {\r\n\t\t\t\tif (!question || !question.id || !this.answers[question.id]) return true; // 如果没有问题或答案，视为未完成\r\n\r\n\t\t\t\tconst answer = this.answers[question.id];\r\n\t\t\t\tlet isEmpty = false;\r\n\r\n\t\t\t\tswitch (question.itemResultType) {\r\n\t\t\t\t\tcase \"single-choice\": // 单选题\r\n\t\t\t\t\t\tisEmpty = !answer.value;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"multiple-choice\": // 多选题\r\n\t\t\t\t\t\tisEmpty = !answer.value || answer.value.length === 0;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"text\": // 文本框\r\n\t\t\t\t\t\tisEmpty = !answer.value || answer.value.trim() === \"\";\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"nps-scale\": // 评分\r\n\t\t\t\t\t\tisEmpty = answer.value === 0;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"dropdown\": // 下拉选择\r\n\t\t\t\t\t\tisEmpty = !answer.value;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"region-select\": // 地区选择\r\n\t\t\t\t\t\tisEmpty = !answer.value || answer.value.length === 0;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"matrix-single\": // 矩阵单选\r\n\t\t\t\t\t\t// 检查是否每一行都有选择\r\n\t\t\t\t\t\t// 确保存在必要的数据结构\r\n\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t!question.itemResultOption ||\r\n\t\t\t\t\t\t\t!question.itemResultOption.rows ||\r\n\t\t\t\t\t\t\t!Array.isArray(question.itemResultOption.rows)\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\treturn true; // 结构不完整，视为未完成\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 初始化默认为未填写\r\n\t\t\t\t\t\tisEmpty = true;\r\n\r\n\t\t\t\t\t\t// 如果答案值不是对象，直接视为未填写\r\n\t\t\t\t\t\tif (!answer.value || typeof answer.value !== \"object\") {\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 获取所有行，检查是否每一行都已填写\r\n\t\t\t\t\t\tconst rows = question.itemResultOption.rows;\r\n\t\t\t\t\t\tlet allRowsFilled = true;\r\n\t\t\t\t\t\tlet anyRowFilled = false;\r\n\r\n\t\t\t\t\t\t// 检查每一行的填写状态\r\n\t\t\t\t\t\tfor (const row of rows) {\r\n\t\t\t\t\t\t\tif (!row || !row.value) continue; // 跳过无效行\r\n\r\n\t\t\t\t\t\t\t// 检查当前行是否已填写\r\n\t\t\t\t\t\t\tconst rowValue = answer.value[row.value];\r\n\r\n\t\t\t\t\t\t\t// 改进的空值检查：恢复对空字符串的检查，但要正确处理0等falsy值\r\n\t\t\t\t\t\t\t// 使用严格相等检查空字符串，同时检查undefined和null\r\n\t\t\t\t\t\t\tconst isRowFilled = rowValue !== undefined && rowValue !== null && rowValue !== \"\";\r\n\r\n\t\t\t\t\t\t\tif (isRowFilled) {\r\n\t\t\t\t\t\t\t\tanyRowFilled = true;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tallRowsFilled = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 必须所有行都已填写，否则视为未完成\r\n\t\t\t\t\t\tisEmpty = !allRowsFilled;\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tcase \"matrix-multiple\": // 矩阵多选\r\n\t\t\t\t\t\t// 检查是否每一行都至少有一个选择\r\n\r\n\t\t\t\t\t\t// 确保存在必要的数据结构\r\n\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t!question.itemResultOption ||\r\n\t\t\t\t\t\t\t!question.itemResultOption.rows ||\r\n\t\t\t\t\t\t\t!Array.isArray(question.itemResultOption.rows)\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\treturn true; // 结构不完整，视为未完成\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 初始化默认为未填写\r\n\t\t\t\t\t\tisEmpty = true;\r\n\r\n\t\t\t\t\t\t// 如果答案值不是对象，直接视为未填写\r\n\t\t\t\t\t\tif (!answer.value || typeof answer.value !== \"object\") {\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 获取所有行，检查是否每一行都已填写\r\n\t\t\t\t\t\tconst multiRows = question.itemResultOption.rows;\r\n\t\t\t\t\t\tlet allMultiRowsFilled = true;\r\n\t\t\t\t\t\tlet anyMultiRowFilled = false;\r\n\r\n\t\t\t\t\t\t// 检查每一行的填写状态\r\n\t\t\t\t\t\tfor (const row of multiRows) {\r\n\t\t\t\t\t\t\tif (!row || !row.value) continue; // 跳过无效行\r\n\r\n\t\t\t\t\t\t\t// 检查当前行是否已填写（至少选择一项）\r\n\t\t\t\t\t\t\tconst rowValues = answer.value[row.value];\r\n\t\t\t\t\t\t\tconst isRowFilled = Array.isArray(rowValues) && rowValues.length > 0;\r\n\r\n\t\t\t\t\t\t\tif (isRowFilled) {\r\n\t\t\t\t\t\t\t\tanyMultiRowFilled = true;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tallMultiRowsFilled = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 必须所有行都已填写，否则视为未完成\r\n\t\t\t\t\t\tisEmpty = !allMultiRowsFilled;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tisEmpty = true; // 未知题型视为未完成\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn isEmpty;\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn true; // 出错视为未完成\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 提交问卷\r\n\t\tsubmitQuestionnaire() {\r\n      // 显示提交中\r\n      this.submitState = true;\r\n      uni.showLoading({\r\n        title: \"提交中...\",\r\n      });\r\n      // const bizQnrFillItemMgmtRequest = Object.entries(this.answers).map(([questionId, answer]) => {\r\n      //   return {\r\n      //     itemId: questionId,\r\n      //     fillResult: JSON.stringify(answer.value),\r\n      //     questionType: answer.questionType,\r\n      //     modelId: this.questionnaire.id,\r\n      //     text: answer.text || \"\",\r\n      //   };\r\n      // });\r\n      try {\r\n\t\t\t\t// 设置已提交标志\r\n\t\t\t\tthis.hasSubmitted = true;\r\n\r\n\t\t\t\t// 验证问卷\r\n\t\t\t\tconst errorQuestion = this.validateQuestionnaire();\r\n\r\n\t\t\t\tif (errorQuestion) {\r\n\t\t\t\t\t// 获取错误问题的ID\r\n\t\t\t\t\tconst errorQuestionId = errorQuestion.id;\r\n\r\n\t\t\t\t\t// 滚动到第一个错误的问题位置\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tselector: `#question-${errorQuestionId}`,\r\n\t\t\t\t\t\tduration: 300,\r\n\t\t\t\t\t});\r\n\r\n          throw new Error('验证失败');\r\n\t\t\t\t}\r\n\t\t\t\t// 构建提交数据\r\n\t\t\t\tconst bizQnrFillItemMgmtRequest = Object.entries(this.answers).map(\r\n\t\t\t\t\t([questionId, answer]) => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\titemId: questionId,\r\n\t\t\t\t\t\t\tfillResult: JSON.stringify(answer.value),\r\n\t\t\t\t\t\t\tquestionType: answer.questionType,\r\n\t\t\t\t\t\t\tmodelId: this.questionnaire.id,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\r\n\t\t\t\tsubmitQuestionnaire(bizQnrFillItemMgmtRequest)\r\n\t\t\t\t\t.then((res) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tconst { code, message } = res.data;\r\n\t\t\t\t\t\tif (code !== 200) {\r\n\t\t\t\t\t\t\tuni.$u.toast(message || \"提交失败\");\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.hasSubmitted = false;\r\n\t\t\t\t\t\tthis.initAnswers();\r\n            uni.showToast({\r\n              title: \"保存成功\",\r\n              icon: \"success\",\r\n              duration: 2000,\r\n            });\r\n            // // 添加延迟，让用户看到成功提示后再跳转\r\n            setTimeout(() => {\r\n              uni.switchTab({\r\n                url: \"/pages/me/index\",\r\n              });\r\n              // uni.navigateBack();\r\n            }, 2000);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((err) => {\r\n\t\t\t\t\t\tuni.hideLoading({\r\n              noConflict: true\r\n            });\r\n\t\t\t\t\t\tuni.$u.toast(err.message || \"提交失败\");\r\n\t\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: \"error || 提交失败，请重试\",\r\n\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n        uni.hideLoading({\r\n          noConflict: true\r\n        });\r\n        this.submitState = true;\r\n      }\r\n\t\t},\r\n\r\n\t\t// 更新文本输入\r\n\t\tupdateTextInput(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tconst isEmpty = !value;\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 处理单选题变化\r\n\t\thandleRadioChange(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tconst isEmpty = !value;\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 处理多选题变化\r\n\t\thandleCheckboxChange(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tconst isEmpty = !value || value.length === 0;\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 处理评分题变化\r\n\t\thandleRateChange(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tconst isEmpty = value === 0;\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 处理选择题变化\r\n\t\thandleDropdownChange(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tconst isEmpty = !value;\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t},\r\n\r\n\t\t// 处理地区选择题变化\r\n\t\thandleRegionChange(questionId, value) {\r\n\t\t\ttry {\r\n\t\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\t\tif (!question) return;\r\n\r\n\t\t\t\tlet isEmpty = true;\r\n\r\n\t\t\t\t// 与validateQuestion使用相同的逻辑\r\n\t\t\t\t// 如果值是对象且有detail.value数组，则检查数组长度\r\n\t\t\t\tif (\r\n\t\t\t\t\tvalue &&\r\n\t\t\t\t\ttypeof value === \"object\" &&\r\n\t\t\t\t\tvalue.detail &&\r\n\t\t\t\t\tArray.isArray(value.detail.value)\r\n\t\t\t\t) {\r\n\t\t\t\t\tisEmpty = value.detail.value.length === 0;\r\n\r\n\t\t\t\t\t// 保存文本数据\r\n\t\t\t\t\tif (!isEmpty && value.detail.value && Array.isArray(value.detail.value)) {\r\n\t\t\t\t\t\t// 添加text字段保存文本数据\r\n            let daList = value.detail.value\r\n\t\t\t\t\t\tvalue.text = daList.map(item => item.text).join('/');\r\n            // 获取现有的data数据\r\n            value.data = this.answers[questionId].value;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 更新答案\r\n\t\t\t\tthis.answers[questionId].value = {value: value.data, text: value.text};\r\n\t\t\t\tif (question.itemIsRequired == 1 && this.hasSubmitted) {\r\n\t\t\t\t\t// 更新验证状态\r\n\t\t\t\t\tthis.$set(this.validationErrors, questionId, isEmpty);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"处理地区选择出错:\", error);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调试辅助函数：手动检查问题验证状态\r\n\t\tdebugValidation(questionId) {\r\n\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\tif (!question) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// 调用验证函数\r\n\t\t\tconst isEmpty = this.validateQuestion(question);\r\n\r\n\t\t\t// 特别处理不同题型的数据结构\r\n\t\t\tswitch (question.itemResultType) {\r\n\t\t\t\tcase \"region-select\":\r\n\t\t\t\t\tthis.debugRegionData(questionId);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"matrix-single\":\r\n\t\t\t\tcase \"matrix-multiple\":\r\n\t\t\t\t\tthis.debugMatrixData(questionId);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tdefault:\r\n\t\t\t\t// 一般题型直接显示答案即可\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调试地区选择数据\r\n\t\tdebugRegionData(questionId) {\r\n\t\t\tconst answer = this.answers[questionId];\r\n\t\t\tif (!answer) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tlet isSelected = false;\r\n\r\n\t\t\t// 与validateQuestion使用相同的逻辑判断是否已选择\r\n\t\t\t// 1. 字符串类型（包括空字符串）\r\n\t\t\tif (typeof answer.value === \"string\") {\r\n\t\t\t\tisSelected = false;\r\n\t\t\t}\r\n\t\t\t// 2. 对象类型\r\n\t\t\telse if (typeof answer.value === \"object\" && answer.value !== null) {\r\n\t\t\t\tif (answer.value.detail && Array.isArray(answer.value.detail.value)) {\r\n\t\t\t\t\tisSelected = answer.value.detail.value.length > 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tisSelected = false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 3. 其他情况\r\n\t\t\telse {\r\n\t\t\t\tisSelected = false;\r\n\t\t\t}\r\n\r\n\t\t\t// 调用validateQuestion测试\r\n\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\tif (question) {\r\n\t\t\t\tconst isEmpty = this.validateQuestion(question);\r\n\r\n\t\t\t\t// 检查两个结果是否一致\r\n\t\t\t\tif ((isEmpty && isSelected) || (!isEmpty && !isSelected)) {\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调试矩阵题数据\r\n\t\tdebugMatrixData(questionId) {\r\n\t\t\tconst question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n\t\t\tconst answer = this.answers[questionId];\r\n\r\n\t\t\tif (!question || !answer) return;\r\n\r\n\t\t\tconst isMultiple = question.itemResultType === \"matrix-multiple\";\r\n\r\n\t\t\tif (!question.itemResultOption || !question.itemResultOption.rows) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查每一行的填写状态\r\n\t\t\tquestion.itemResultOption.rows.forEach((row) => {\r\n\t\t\t\tif (!row || !row.value) return;\r\n\r\n\t\t\t\tconst rowValue = answer.value[row.value];\r\n\t\t\t\tlet isFilled = false;\r\n\r\n\t\t\t\tif (isMultiple) {\r\n\t\t\t\t\t// 多选矩阵\r\n\t\t\t\t\tisFilled = Array.isArray(rowValue) && rowValue.length > 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 单选矩阵 - 使用与validateQuestion相同的判断\r\n\t\t\t\t\tisFilled = rowValue !== undefined && rowValue !== null && rowValue !== \"\";\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// 验证测试\r\n\t\t\tconst isEmpty = this.validateQuestion(question);\r\n\t\t},\r\n\r\n    // 处理矩阵单元格点击\r\n    handleMatrixCellClick(questionId, rowIndex, columnValue) {\r\n      // 如果问卷不可编辑，则不处理点击\r\n      if (!this.isQuestionnaireEditable) return;\r\n\r\n      try {\r\n        const question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n        if (\r\n            !question ||\r\n            !question.itemResultOption ||\r\n            !question.itemResultOption.rows ||\r\n            !question.itemResultOption.rows[rowIndex]\r\n        )\r\n          return;\r\n\r\n        const rowValue = question.itemResultOption.rows[rowIndex].value;\r\n        if (!this.answers[questionId] || !this.answers[questionId].value) return;\r\n\r\n        // 记录选择前的值\r\n        const oldValue = this.answers[questionId].value[rowValue];\r\n\r\n        // // 如果当前值已经被选中，则取消选择\r\n        // if (this.answers[questionId].value[rowValue] === columnValue) {\r\n        //   this.$set(this.answers[questionId].value, rowValue, \"\");\r\n        // } else {\r\n        //   // 否则更新为新的选中值\r\n        //   this.$set(this.answers[questionId].value, rowValue, columnValue);\r\n        // }\r\n        this.$set(this.answers[questionId].value, rowValue, columnValue);\r\n\r\n        // 验证更新\r\n        if (this.hasSubmitted) {\r\n          if (question && question.itemIsRequired == 1) {\r\n            const isEmpty = this.validateQuestion(question);\r\n            this.$set(this.validationErrors, questionId, isEmpty);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"处理矩阵单元格点击出错:\", error);\r\n      }\r\n    },\r\n\r\n    // 处理矩阵多选单元格点击\r\n    handleMatrixCheckboxCellClick(questionId, rowIndex, columnValue) {\r\n      // 如果问卷不可编辑，则不处理点击\r\n      if (!this.isQuestionnaireEditable) return;\r\n\r\n      try {\r\n        const question = this.questionnaire.itemList.find((q) => q.id === questionId);\r\n        if (\r\n            !question ||\r\n            !question.itemResultOption ||\r\n            !question.itemResultOption.rows ||\r\n            !question.itemResultOption.rows[rowIndex]\r\n        )\r\n          return;\r\n\r\n        const rowValue = question.itemResultOption.rows[rowIndex].value;\r\n        if (!this.answers[questionId] || !this.answers[questionId].value) return;\r\n\r\n        // 确保当前行的值是数组\r\n        if (!Array.isArray(this.answers[questionId].value[rowValue])) {\r\n          this.$set(this.answers[questionId].value, rowValue, []);\r\n        }\r\n\r\n        // 获取当前行的选中值数组\r\n        const currentValues = [...(this.answers[questionId].value[rowValue] || [])];\r\n\r\n        // 检查值是否已经存在\r\n        const valueIndex = currentValues.indexOf(columnValue);\r\n\r\n        // 如果值已存在，则移除；否则添加\r\n        if (valueIndex !== -1) {\r\n          currentValues.splice(valueIndex, 1);\r\n        } else {\r\n          currentValues.push(columnValue);\r\n        }\r\n\r\n        // 更新值\r\n        this.$set(this.answers[questionId].value, rowValue, currentValues);\r\n\r\n        // 验证更新\r\n        if (this.hasSubmitted) {\r\n          if (question && question.itemIsRequired == 1) {\r\n            const isEmpty = this.validateQuestion(question);\r\n            this.$set(this.validationErrors, questionId, isEmpty);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"处理矩阵多选单元格点击出错:\", error);\r\n      }\r\n    },\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.questionnaire-container {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.questionnaire-header {\r\n\tbackground-color: #ffffff;\r\n\tpadding: 30rpx;\r\n\tborder-radius: 12rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.questionnaire-title {\r\n\tfont-size: 46rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #7f88e7;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n\ttext-align: center;\r\n}\r\n\r\n.questionnaire-desc {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tline-height: 1.5;\r\n\twhite-space: pre-wrap;\r\n\twidth: 100%;\r\n}\r\n\r\n.questionnaire-tips {\r\n\tmargin-top: 20rpx;\r\n\ttext-align: right;\r\n}\r\n\r\n.questionnaire-tips-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.question-list {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.question-item {\r\n\tbackground-color: #ffffff;\r\n\tpadding: 30rpx;\r\n\tborder-radius: 12rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.question-title {\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tfont-size: 34rpx;\r\n\tcolor: #4e7ee7;\r\n}\r\n\r\n.question-index {\r\n\tfont-weight: bold;\r\n\tmargin-right: 5rpx;\r\n}\r\n\r\n.question-required {\r\n\tcolor: #fa3534;\r\n\tmargin-right: 5rpx;\r\n\tmargin-top: 15rpx;\r\n}\r\n\r\n.question-text {\r\n\tflex: 1;\r\n}\r\n\r\n.question-content {\r\n\tpadding-left: 40rpx;\r\n}\r\n\r\n.error-tip {\r\n\tfont-size: 24rpx;\r\n\tcolor: #fa3534;\r\n\tmargin-top: 10rpx;\r\n\tpadding: 10rpx;\r\n}\r\n\r\n.submit-area {\r\n\tpadding: 30rpx 0;\r\n}\r\n\r\n.error-summary {\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbackground-color: rgba(250, 53, 52, 0.1);\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.error-summary text {\r\n\tcolor: #fa3534;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n/* 矩阵题型样式 */\r\n.matrix-simple {\r\n\tpadding-left: 0;\r\n}\r\n\r\n.matrix-simple-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 30rpx;\r\n\tbackground-color: #f8f8f8;\r\n\tpadding: 20rpx 0;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.matrix-simple-column {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.matrix-simple-row {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.matrix-simple-row-title {\r\n\ttext-align: left;\r\n\tfont-weight: 500;\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 0 15rpx;\r\n}\r\n\r\n.matrix-simple-options {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.matrix-simple-option {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n/* 透明覆盖层样式 */\r\n.radio-overlay {\r\n  position: absolute;\r\n  top: -13px;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 10;\r\n  background-color: transparent;\r\n  /* 增加点击区域的大小 */\r\n  min-width: 80rpx;\r\n  min-height: 80rpx;\r\n}\r\n\r\n/* 多选矩阵特殊样式 */\r\n.matrix-checkbox-header {\r\n\tbackground-color: #f8f8f8;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 20rpx 0;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.matrix-simple-option-empty {\r\n\twidth: 200rpx;\r\n\tflex: none;\r\n}\r\n\r\n.matrix-checkbox-row {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.matrix-checkbox-row-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 0 15rpx;\r\n}\r\n\r\n.matrix-checkbox-options {\r\n\twidth: 100%;\r\n}\r\n\r\n.matrix-checkbox-option {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tpadding: 0 5rpx;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n/* 透明覆盖层样式 */\r\n.checkbox-overlay {\r\n  position: absolute;\r\n  top: -13px;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 10;\r\n  background-color: transparent;\r\n  /* 增加点击区域的大小 */\r\n  min-width: 80rpx;\r\n  min-height: 80rpx;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 全局样式，不使用scoped，直接覆盖组件样式 */\r\n.u-radio {\r\n\tmargin-bottom: 16rpx !important;\r\n}\r\n\r\n.u-radio:last-child {\r\n\tmargin-bottom: 0 !important;\r\n}\r\n\r\n.u-checkbox {\r\n\tmargin-bottom: 16rpx !important;\r\n}\r\n\r\n.u-checkbox:last-child {\r\n\tmargin-bottom: 0 !important;\r\n}\r\n\r\n/* 矩阵题型中的单选框和复选框样式 */\r\n.matrix-simple-option .u-radio {\r\n\tmargin-bottom: 0 !important;\r\n  position: relative;\r\n  z-index: 5;\r\n}\r\n\r\n.matrix-simple-option .u-checkbox {\r\n\tmargin-bottom: 0 !important;\r\n  position: relative;\r\n  z-index: 5;\r\n}\r\n\r\n/* 修复多选矩阵中复选框的样式 */\r\n.matrix-simple .u-checkbox__icon-wrap {\r\n\tmargin-right: 0 !important;\r\n}\r\n\r\n.matrix-simple .u-checkbox__label {\r\n\tdisplay: none !important;\r\n}\r\n\r\n/* 确保多选矩阵中的复选框组平均分布 */\r\n.matrix-checkbox-group .u-checkbox-group {\r\n\twidth: 100% !important;\r\n\tdisplay: flex !important;\r\n\tjustify-content: space-between !important;\r\n}\r\n\r\n.matrix-checkbox-group .u-checkbox-group__wrapper {\r\n\twidth: 100% !important;\r\n\tdisplay: flex !important;\r\n\tjustify-content: space-between !important;\r\n}\r\n\r\n.matrix-checkbox-group .u-checkbox {\r\n\tflex: 1 !important;\r\n\tdisplay: flex !important;\r\n\tjustify-content: center !important;\r\n}\r\n\r\n::v-deep .u-radio__text {\r\n\tfont-size: 30rpx !important;\r\n}\r\n\r\n::v-deep .u-checkbox text {\r\n\tfont-size: 30rpx !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=style&index=0&id=03ebdbe5&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=style&index=0&id=03ebdbe5&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752574417008\n      var cssReload = require(\"D:/Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./questionnaire.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752574415914\n      var cssReload = require(\"D:/Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}