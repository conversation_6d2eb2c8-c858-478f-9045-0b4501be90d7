{"version": 3, "sources": ["webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?1293", "webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?8eb0", "webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?5ee1", "webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?9334", "uni-app:///uview-ui/components/u-navbar/u-navbar.vue", "webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?d109", "webpack:///D:/dljs-app/uview-ui/components/u-navbar/u-navbar.vue?c7c8"], "names": ["name", "mixins", "data", "methods", "leftClick", "uni", "rightClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC4K;AAC5K,gBAAgB,sLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,+pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2EtqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,eAwBA;EACAA;EACAC;EACAC;IACA,QAEA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1HA;AAAA;AAAA;AAAA;AAAyvC,CAAgB,0sCAAG,EAAC,C;;;;;;;;;;;ACA7wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-navbar/u-navbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-navbar.vue?vue&type=template&id=1194bf80&scoped=true&\"\nvar renderjs\nimport script from \"./u-navbar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-navbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-navbar.vue?vue&type=style&index=0&id=1194bf80&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1194bf80\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-navbar/u-navbar.vue\"\nexport default component.exports", "export * from \"-!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=template&id=1194bf80&scoped=true&\"", "var components\ntry {\n  components = {\n    uStatusBar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-status-bar/u-status-bar\" */ \"@/uview-ui/components/u-status-bar/u-status-bar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.fixed && _vm.placeholder\n      ? _vm.$u.addUnit(\n          _vm.$u.getPx(_vm.height) + _vm.$u.sys().statusBarHeight,\n          \"px\"\n        )\n      : null\n  var g1 = _vm.$u.addUnit(_vm.height)\n  var s0 = _vm.__get_style([\n    {\n      width: _vm.$u.addUnit(_vm.titleWidth),\n    },\n    _vm.$u.addStyle(_vm.titleStyle),\n  ])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-navbar\">\r\n\t\t<view\r\n\t\t\tclass=\"u-navbar__placeholder\"\r\n\t\t\tv-if=\"fixed && placeholder\"\r\n\t\t\t:style=\"{\r\n\t\t\t\theight: $u.addUnit($u.getPx(height) + $u.sys().statusBarHeight,'px'),\r\n\t\t\t}\"\r\n\t\t></view>\r\n\t\t<view :class=\"[fixed && 'u-navbar--fixed']\">\r\n\t\t\t<u-status-bar\r\n\t\t\t\tv-if=\"safeAreaInsetTop\"\r\n\t\t\t\t:bgColor=\"bgColor\"\r\n\t\t\t></u-status-bar>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-navbar__content\"\r\n\t\t\t\t:class=\"[border && 'u-border-bottom']\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\theight: $u.addUnit(height),\r\n\t\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-navbar__content__left\"\r\n\t\t\t\t\thover-class=\"u-navbar__content__left--hover\"\r\n\t\t\t\t\thover-start-time=\"150\"\r\n\t\t\t\t\t@tap=\"leftClick\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\tv-if=\"leftIcon\"\r\n\t\t\t\t\t\t\t:name=\"leftIcon\"\r\n\t\t\t\t\t\t\t:size=\"leftIconSize\"\r\n\t\t\t\t\t\t\t:color=\"leftIconColor\"\r\n\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tv-if=\"leftText\"\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\tcolor: leftIconColor\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\tclass=\"u-navbar__content__left__text\"\r\n\t\t\t\t\t\t>{{ leftText }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot name=\"center\">\r\n\t\t\t\t\t<text\r\n\t\t\t\t\t\tclass=\"u-line-1 u-navbar__content__title\"\r\n\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\twidth: $u.addUnit(titleWidth),\r\n\t\t\t\t\t\t}, $u.addStyle(titleStyle)]\"\r\n\t\t\t\t\t>{{ title }}</text>\r\n\t\t\t\t</slot>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-navbar__content__right\"\r\n\t\t\t\t\tv-if=\"$slots.right || rightIcon || rightText\"\r\n\t\t\t\t\t@tap=\"rightClick\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\tv-if=\"rightIcon\"\r\n\t\t\t\t\t\t\t:name=\"rightIcon\"\r\n\t\t\t\t\t\t\tsize=\"20\"\r\n\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tv-if=\"rightText\"\r\n\t\t\t\t\t\t\tclass=\"u-navbar__content__right__text\"\r\n\t\t\t\t\t\t>{{ rightText }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * Navbar 自定义导航栏\r\n\t * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uni-app带的导航栏。\r\n\t * @tutorial https://www.uviewui.com/components/navbar.html\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetTop\t是否开启顶部安全区适配  （默认 true ）\r\n\t * @property {Boolean}\t\t\tplaceholder\t\t\t固定在顶部时，是否生成一个等高元素，以防止塌陷 （默认 false ）\r\n\t * @property {Boolean}\t\t\tfixed\t\t\t\t导航栏是否固定在顶部 （默认 false ）\r\n\t * @property {Boolean}\t\t\tborder\t\t\t\t导航栏底部是否显示下边框 （默认 false ）\r\n\t * @property {String}\t\t\tleftIcon\t\t\t左边返回图标的名称，只能为uView自带的图标 （默认 'arrow-left' ）\r\n\t * @property {String}\t\t\tleftText\t\t\t左边的提示文字\r\n\t * @property {String}\t\t\trightText\t\t\t右边的提示文字\r\n\t * @property {String}\t\t\trightIcon\t\t\t右边返回图标的名称，只能为uView自带的图标\r\n\t * @property {String}\t\t\ttitle\t\t\t\t导航栏标题，如设置为空字符，将会隐藏标题占位区域\r\n\t * @property {String}\t\t\tbgColor\t\t\t\t导航栏背景设置 （默认 '#ffffff' ）\r\n\t * @property {String | Number}\ttitleWidth\t\t\t导航栏标题的最大宽度，内容超出会以省略号隐藏 （默认 '400rpx' ）\r\n\t * @property {String | Number}\theight\t\t\t\t导航栏高度(不包括状态栏高度在内，内部自动加上)（默认 '44px' ）\r\n\t * @property {String | Number}\tleftIconSize\t\t左侧返回图标的大小（默认 20px ）\r\n\t * @property {String | Number}\tleftIconColor\t\t左侧返回图标的颜色（默认 #303133 ）\r\n\t * @property {Boolean}\t        autoBack\t\t\t点击左侧区域(返回图标)，是否自动返回上一页（默认 false ）\r\n\t * @property {Object | String}\ttitleStyle\t\t\t标题的样式，对象或字符串\r\n\t * @event {Function} leftClick\t\t点击左侧区域\r\n\t * @event {Function} rightClick\t\t点击右侧区域\r\n\t * @example <u-navbar title=\"剑未配妥，出门已是江湖\" left-text=\"返回\" right-text=\"帮助\" @click-left=\"onClickBack\" @click-right=\"onClickRight\"></u-navbar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-navbar',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击左侧区域\r\n\t\t\tleftClick() {\r\n\t\t\t\t// 如果配置了autoBack，自动返回上一页\r\n\t\t\t\tthis.$emit('leftClick')\r\n\t\t\t\tif(this.autoBack) {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击右侧区域\r\n\t\t\trightClick() {\r\n\t\t\t\tthis.$emit('rightClick')\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-navbar {\r\n\r\n\t\t&--fixed {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tz-index: 11;\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\theight: 44px;\r\n\t\t\tbackground-color: #9acafc;\r\n\t\t\tposition: relative;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__left,\r\n\t\t\t&__right {\r\n\t\t\t\tpadding: 0 13px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\t@include flex(row);\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\r\n\t\t\t&__left {\r\n\t\t\t\tleft: 0;\r\n\t\t\t\t\r\n\t\t\t\t&--hover {\r\n\t\t\t\t\topacity: 0.7;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\tmargin-left: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&__title {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t}\r\n\r\n\t\t\t&__right {\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\tmargin-left: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=1194bf80&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=1194bf80&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752574416980\n      var cssReload = require(\"D:/Hbulider/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}