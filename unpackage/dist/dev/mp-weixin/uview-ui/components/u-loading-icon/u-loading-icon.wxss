@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-1b800240, scroll-view.data-v-1b800240, swiper-item.data-v-1b800240 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-loading-icon.data-v-1b800240 {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.u-loading-icon__text.data-v-1b800240 {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.u-loading-icon__spinner.data-v-1b800240 {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  -webkit-animation: u-rotate-data-v-1b800240 1s linear infinite;
          animation: u-rotate-data-v-1b800240 1s linear infinite;
}
.u-loading-icon__spinner--semicircle.data-v-1b800240 {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.u-loading-icon__spinner--circle.data-v-1b800240 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.u-loading-icon--vertical.data-v-1b800240 {
  flex-direction: column;
}
.data-v-1b800240:host {
  font-size: 0px;
  line-height: 1;
}
.u-loading-icon__spinner--spinner.data-v-1b800240 {
  -webkit-animation-timing-function: steps(12);
          animation-timing-function: steps(12);
}
.u-loading-icon__text.data-v-1b800240:empty {
  display: none;
}
.u-loading-icon--vertical .u-loading-icon__text.data-v-1b800240 {
  margin: 6px 0 0;
  color: #606266;
}
.u-loading-icon__dot.data-v-1b800240 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.u-loading-icon__dot.data-v-1b800240:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(1) {
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg);
  opacity: 1;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(2) {
  -webkit-transform: rotate(60deg);
          transform: rotate(60deg);
  opacity: 0.9375;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(3) {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  opacity: 0.875;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(4) {
  -webkit-transform: rotate(120deg);
          transform: rotate(120deg);
  opacity: 0.8125;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(5) {
  -webkit-transform: rotate(150deg);
          transform: rotate(150deg);
  opacity: 0.75;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(6) {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  opacity: 0.6875;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(7) {
  -webkit-transform: rotate(210deg);
          transform: rotate(210deg);
  opacity: 0.625;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(8) {
  -webkit-transform: rotate(240deg);
          transform: rotate(240deg);
  opacity: 0.5625;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(9) {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
  opacity: 0.5;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(10) {
  -webkit-transform: rotate(300deg);
          transform: rotate(300deg);
  opacity: 0.4375;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(11) {
  -webkit-transform: rotate(330deg);
          transform: rotate(330deg);
  opacity: 0.375;
}
.u-loading-icon__dot.data-v-1b800240:nth-of-type(12) {
  -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
  opacity: 0.3125;
}
@-webkit-keyframes u-rotate-data-v-1b800240 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(1turn);
            transform: rotate(1turn);
}
}
@keyframes u-rotate-data-v-1b800240 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(1turn);
            transform: rotate(1turn);
}
}

