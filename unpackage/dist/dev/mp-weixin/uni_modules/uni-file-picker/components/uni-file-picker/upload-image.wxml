<view class="uni-file-picker__container"><block wx:for="{{filesList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="file-picker__box" style="{{(boxStyle)}}"><view class="file-picker__box-content" style="{{(borderStyle)}}"><image class="file-image" src="{{item.url}}" mode="aspectFill" data-event-opts="{{[['tap',[['prviewImage',['$0',index],[[['filesList','',index]]]]]]]}}" catchtap="__e"></image><block wx:if="{{delIcon&&!readonly}}"><view data-event-opts="{{[['tap',[['delFile',[index]]]]]}}" class="icon-del-box" catchtap="__e"><view class="icon-del"></view><view class="icon-del rotate"></view></view></block><block wx:if="{{item.progress&&item.progress!==100||item.progress===0}}"><view class="file-picker__progress"><progress class="file-picker__progress-item" percent="{{item.progress===-1?0:item.progress}}" stroke-width="4" backgroundColor="{{item.errMsg?'#ff5a5f':'#EBEBEB'}}"></progress></view></block><block wx:if="{{item.errMsg}}"><view data-event-opts="{{[['tap',[['uploadFiles',['$0',index],[[['filesList','',index]]]]]]]}}" class="file-picker__mask" catchtap="__e">点击重试</view></block></view></view></block><block wx:if="{{$root.g0}}"><view class="file-picker__box" style="{{(boxStyle)}}"><view data-event-opts="{{[['tap',[['choose',['$event']]]]]}}" class="file-picker__box-content is-add" style="{{(borderStyle)}}" bindtap="__e"><slot></slot></view></view></block></view>