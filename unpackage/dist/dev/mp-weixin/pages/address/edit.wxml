<view class="edit-address-container data-v-6c4d65be"><view class="form-container data-v-6c4d65be"><view class="form-item data-v-6c4d65be"></view><view class="form-item data-v-6c4d65be"><view class="form-label data-v-6c4d65be">收货人</view><input class="form-input data-v-6c4d65be" placeholder="请输入收货人姓名" maxlength="20" placeholder-style="color: #cccccc;" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item data-v-6c4d65be"><view class="form-label data-v-6c4d65be">手机号</view><input class="form-input data-v-6c4d65be" placeholder="请输入手机号" type="number" maxlength="11" placeholder-style="color: #cccccc;" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" value="{{formData.phone}}" bindinput="__e"/></view><view class="form-item region-item data-v-6c4d65be"><view class="form-label data-v-6c4d65be">所在地区</view><view class="region-selector data-v-6c4d65be"><block wx:if="{{regionText}}"><text data-event-opts="{{[['tap',[['selectRegion',['$event']]]]]}}" class="region-text data-v-6c4d65be" bindtap="__e">{{regionText}}</text></block><block wx:else><text data-event-opts="{{[['tap',[['selectRegion',['$event']]]]]}}" class="placeholder data-v-6c4d65be" bindtap="__e">请选择省市街道</text></block><view data-event-opts="{{[['tap',[['getLocation',['$event']]]]]}}" class="location-icon data-v-6c4d65be" bindtap="__e"><u-icon vue-id="1d5fd308-1" name="map" color="#FF6C05" size="20" class="data-v-6c4d65be" bind:__l="__l"></u-icon><text class="location-te data-v-6c4d65be">定位</text></view></view></view><view class="form-item detail-item data-v-6c4d65be"><view class="form-label data-v-6c4d65be">详细地址</view><textarea class="form-textarea data-v-6c4d65be" placeholder="请输入小区楼栋、门牌号、村等" maxlength="100" auto-height="{{true}}" placeholder-style="color: #cccccc;" data-event-opts="{{[['input',[['__set_model',['$0','detail','$event',[]],['formData']]]]]}}" value="{{formData.detail}}" bindinput="__e"></textarea></view><view class="smart-paste data-v-6c4d65be"><block wx:if="{{showSmartPaste}}"><view class="smart-paste-card data-v-6c4d65be"><view class="smart-paste-header data-v-6c4d65be"><image class="feedback-icon _img data-v-6c4d65be" src="{{$root.m0}}" alt></image><text class="smart-paste-title data-v-6c4d65be">智能贴贴</text></view><view class="smart-paste-content data-v-6c4d65be"><text class="smart-paste-text data-v-6c4d65be">粘贴文本到此处，将自动识别收货信息</text><textarea class="smart-paste-textarea data-v-6c4d65be" placeholder="例:张三，139*******，云南省大理市金桔路...." placeholder-style="color: #cccccc;" auto-height="{{true}}" maxlength="200" data-event-opts="{{[['input',[['__set_model',['','pasteContent','$event',[]]]]]]}}" value="{{pasteContent}}" bindinput="__e"></textarea></view><view class="smart-paste-actions data-v-6c4d65be"><view data-event-opts="{{[['tap',[['clearPaste',['$event']]]]]}}" class="paste-btn clear-btn data-v-6c4d65be" bindtap="__e"><text class="data-v-6c4d65be">清除</text></view><view data-event-opts="{{[['tap',[['confirmAddress',['$0'],['pasteContent']]]]]}}" class="paste-btn confirm-btn data-v-6c4d65be" bindtap="__e"><text class="data-v-6c4d65be">粘贴并识别</text></view></view></view></block></view><view class="default-address-item data-v-6c4d65be"><text class="default-label data-v-6c4d65be">设为默认地址</text><view class="custom-switch data-v-6c4d65be"><u-switch vue-id="1d5fd308-2" activeColor="#007aff" inactiveColor="#E4E4E4" size="25" space="0" value="{{formData.isDefault}}" data-event-opts="{{[['^change',[['toggleDefault']]],['^input',[['__set_model',['$0','isDefault','$event',[]],['formData']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-6c4d65be" bind:__l="__l"></u-switch></view></view></view><view data-event-opts="{{[['tap',[['saveAddress',['$event']]]]]}}" class="save-btn data-v-6c4d65be" bindtap="__e"><text class="data-v-6c4d65be">保存地址</text></view><block wx:if="{{showRegionPicker}}"><view data-event-opts="{{[['tap',[['closeRegionPicker',['$event']]]]]}}" class="region-popup data-v-6c4d65be" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="region-popup-content data-v-6c4d65be" catchtap="__e"><view class="popup-header data-v-6c4d65be"><text class="popup-title data-v-6c4d65be">所在地区</text><view data-event-opts="{{[['tap',[['closeRegionPicker',['$event']]]]]}}" class="close-btn data-v-6c4d65be" bindtap="__e"><u-icon vue-id="1d5fd308-3" name="close-circle-fill" color="#666666" size="20" class="data-v-6c4d65be" bind:__l="__l"></u-icon></view></view><view class="curr-location data-v-6c4d65be"><view class="current-location data-v-6c4d65be"><view class="location-info data-v-6c4d65be"><text class="location-text data-v-6c4d65be">当前定位</text><text class="location-address data-v-6c4d65be">{{currentLocation}}</text></view><view data-event-opts="{{[['tap',[['useCurrentLocation',['$event']]]]]}}" class="use-btn data-v-6c4d65be" bindtap="__e"><text class="data-v-6c4d65be">使用</text></view></view></view><view class="region-tabs-container data-v-6c4d65be"><u-tabs vue-id="1d5fd308-4" list="{{regionTabsList}}" current="{{currentTabIndex}}" scrollable="{{true}}" activeColor="#FF6600" inactiveColor="#666666" lineColor="#FF6600" lineWidth="40" lineHeight="4" data-event-opts="{{[['^change',[['switchTab']]]]}}" bind:change="__e" class="data-v-6c4d65be" bind:__l="__l"></u-tabs></view><scroll-view class="region-list data-v-6c4d65be" scroll-y="{{true}}"><block wx:for="{{currentRegionList[currentTabIndex]}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectRegionItem',['$0'],[[['currentRegionList.'+currentTabIndex+'','',index]]]]]]]}}" class="region-item data-v-6c4d65be" bindtap="__e"><view class="region-content data-v-6c4d65be"><text class="region-simple data-v-6c4d65be">{{item.simple}}</text><text class="region-name data-v-6c4d65be" style="{{'color:'+(item.id===regionTabs[currentTabIndex].id?'#FF6600':'#1A1A1A')+';'}}">{{item.name}}</text></view><block wx:if="{{item.id===regionTabs[currentTabIndex].id}}"><u-icon vue-id="{{'1d5fd308-5-'+index}}" name="checkmark" color="#FF6600" size="20" class="data-v-6c4d65be" bind:__l="__l"></u-icon></block></view></block></scroll-view></view></view></block></view>