@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.edit-address-container.data-v-6c4d65be {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 140rpx;
}
.form-container.data-v-6c4d65be {
  padding: 30rpx;
}
/* 表单项样式 */
.form-item.data-v-6c4d65be {
  background: #FFFFFF;
  padding: 14rpx 30rpx;
  display: flex;
  align-items: flex-start;
}
.form-label.data-v-6c4d65be {
  width: 131rpx;
  flex-shrink: 0;
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  line-height: 78rpx;
  height: 78rpx;
}
.form-input.data-v-6c4d65be {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  padding: 20rpx;
  border: none;
  background: #F0F0F0;
  border-radius: 9rpx;
  height: 80rpx;
}
/* 地区选择项 */
.region-item.data-v-6c4d65be {
  align-items: center;
}
.region-selector.data-v-6c4d65be {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F0F0F0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.region-text.data-v-6c4d65be {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.placeholder.data-v-6c4d65be {
  font-size: 28rpx;
  color: #CCCCCC;
}
.location-icon.data-v-6c4d65be {
  display: flex;
  align-items: center;
}
.location-te.data-v-6c4d65be {
  font-size: 28rpx;
  color: #FF6C05;
  font-weight: 400;
  white-space: nowrap;
  /* 禁止文字换行 */
  overflow: hidden;
  /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}
/* 详细地址项 */
.detail-item.data-v-6c4d65be {
  align-items: flex-start;
}
.form-textarea.data-v-6c4d65be {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: #F0F0F0;
  border-radius: 8rpx;
  padding: 20rpx;
  min-height: 120rpx;
  border: none;
}
/* 智能贴贴卡片 */
.smart-paste.data-v-6c4d65be {
  background: #FFFFFF;
  padding: 30rpx;
}
.smart-paste-card.data-v-6c4d65be {
  border-radius: 16rpx;
  padding: 30rpx;
  border: 2rpx solid #FF6600;
}
.smart-paste-header.data-v-6c4d65be {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.smart-paste-title.data-v-6c4d65be {
  margin-left: 12rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #FF6C05;
}
.smart-paste-content.data-v-6c4d65be {
  margin-bottom: 30rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #696F73;
  line-height: 38rpx;
}
.smart-paste-text.data-v-6c4d65be {
  display: block;
  margin-bottom: 10rpx;
}
.smart-paste-example.data-v-6c4d65be {
  display: block;
}
.smart-paste-actions.data-v-6c4d65be {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.paste-btn.data-v-6c4d65be {
  padding: 12rpx 24rpx;
  border-radius: 10rpx;
  text-align: center;
}
.clear-btn.data-v-6c4d65be {
  background: #FFFFFF;
  border: 1rpx solid #FF6C05;
}
.clear-btn text.data-v-6c4d65be {
  color: #FF6C05;
  font-weight: 400;
  font-size: 28rpx;
}
.confirm-btn.data-v-6c4d65be {
  background: #FF6C05;
}
.confirm-btn text.data-v-6c4d65be {
  color: #FFFFFF;
  font-weight: 400;
  font-size: 28rpx;
}
/* 默认地址设置 */
.default-address-item.data-v-6c4d65be {
  background: #FFFFFF;
  padding: 30rpx;
  padding-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.default-label.data-v-6c4d65be {
  font-size: 28rpx;
  color: #333333;
}
/* 保存按钮 */
.save-btn.data-v-6c4d65be {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 130rpx;
  background: #FFFFFF;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}
.save-btn text.data-v-6c4d65be {
  font-weight: 400;
  font-size: 34rpx;
  color: #FD9218;
}
.save-btn.data-v-6c4d65be:active {
  background: #E55A00;
}
.feedback-icon.data-v-6c4d65be {
  width: 30rpx;
  height: 30rpx;
}
.form-textarea.form-textarea.data-v-6c4d65be {
  padding: 10rpx !important;
}
.uni-input-placeholder.data-v-6c4d65be {
  color: #cccccc !important;
}
.uni-textarea-placeholder.data-v-6c4d65be {
  color: #cccccc !important;
}
/* 地区选择弹出框样式 */
.region-popup.data-v-6c4d65be {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}
.region-popup-content.data-v-6c4d65be {
  width: 100%;
  background: #FFFFFF;
  height: 980rpx;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
/* 弹窗头部 */
.popup-header.data-v-6c4d65be {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}
.popup-title.data-v-6c4d65be {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.close-btn.data-v-6c4d65be {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 当前定位区域 */
.curr-location.data-v-6c4d65be {
  padding: 0rpx 30rpx;
  height: 74rpx;
}
.current-location.data-v-6c4d65be {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0rpx 18rpx;
  justify-content: space-between;
  border-bottom: 1rpx solid #F5F5F5;
  background: #F5F5F5;
  border-radius: 9rpx;
}
.location-info.data-v-6c4d65be {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}
.location-text.data-v-6c4d65be {
  margin: 0 16rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #999999;
  line-height: 74rpx;
  white-space: nowrap;
  /* 禁止文字换行 */
  overflow: hidden;
  /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  width: 21%;
}
.location-address.data-v-6c4d65be {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 74rpx;
  white-space: nowrap;
  /* 禁止文字换行 */
  overflow: hidden;
  /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}
.use-btn.data-v-6c4d65be {
  background: #FF6600;
  color: #FFFFFF;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}
.use-btn text.data-v-6c4d65be {
  font-size: 24rpx;
  color: #FFFFFF;
}
/* 地区tabs容器 */
.region-tabs-container.data-v-6c4d65be {
  border-bottom: 2rpx solid #cccccc;
  margin-top: 32rpx;
  height: 76rpx;
}
/* 地区列表 */
.region-list.data-v-6c4d65be {
  flex: 1;
  height: 400rpx;
  padding-bottom: 40rpx;
}
.region-item.data-v-6c4d65be {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 30rpx;
}
.region-content.data-v-6c4d65be {
  display: flex;
  align-items: center;
  flex: 1;
}
.region-letter.data-v-6c4d65be {
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
  margin-right: 24rpx;
}
.region-simple.data-v-6c4d65be {
  font-weight: 400;
  font-size: 30rpx;
  color: #666666;
  line-height: 32rpx;
  margin-left: 8rpx;
}
.region-name.data-v-6c4d65be {
  font-weight: 400;
  font-size: 30rpx;
  color: #1A1A1A;
  line-height: 32rpx;
  margin-left: 63rpx;
}
.region-item.data-v-6c4d65be:active {
  background: #F5F5F5;
}
.data-v-6c4d65be .u-tabs__wrapper__nav__item__text {
  font-weight: 400;
  font-size: 30rpx;
  color: #FF6C05 !important;
  line-height: 32rpx;
}

