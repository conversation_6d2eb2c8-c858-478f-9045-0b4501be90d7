<view class="questionnaire-container data-v-03ebdbe5"><view class="questionnaire-header data-v-03ebdbe5"><text class="questionnaire-title data-v-03ebdbe5">{{questionnaire.modelTitle||""}}</text><u-parse vue-id="799021c1-1" content="{{questionnaire.modelDesc}}" class="data-v-03ebdbe5" bind:__l="__l"></u-parse></view><view class="question-list data-v-03ebdbe5"><block wx:for="{{$root.l0}}" wx:for-item="question" wx:for-index="__i0__" wx:key="id"><view class="question-item data-v-03ebdbe5" id="{{'question-'+question.$orig.id}}"><view class="question-title data-v-03ebdbe5"><block wx:if="{{question.$orig.itemIsRequired==1}}"><text class="question-required data-v-03ebdbe5">*</text></block><text class="question-index data-v-03ebdbe5">{{question.g0+1+"、"}}</text><text class="question-text data-v-03ebdbe5">{{question.$orig.itemTitle}}</text></view><block wx:if="{{question.$orig.itemResultType==='single-choice'}}"><view class="question-content data-v-03ebdbe5"><u-radio-group vue-id="{{'799021c1-2-'+__i0__}}" disabled="{{!isQuestionnaireEditable}}" placement="column" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^change',[['e0']]],['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']]]]]}}" data-event-params="{{({question:question.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{question.$orig.itemResultOption}}" wx:for-item="option" wx:for-index="optIndex" wx:key="optIndex"><u-radio vue-id="{{('799021c1-3-'+__i0__+'-'+optIndex)+','+('799021c1-2-'+__i0__)}}" customStyle="{{({marginRight:'30px',padding:'8rpx 0'})}}" name="{{option.value}}" label="{{option.text}}" labelSize="30rpx" iconSize="36rpx" class="data-v-03ebdbe5" bind:__l="__l"></u-radio></block></u-radio-group></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='multiple-choice'}}"><view class="question-content data-v-03ebdbe5"><u-checkbox-group vue-id="{{'799021c1-4-'+__i0__}}" disabled="{{!isQuestionnaireEditable}}" placement="column" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^change',[['e1']]],['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']]]]]}}" data-event-params="{{({question:question.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{question.$orig.itemResultOption}}" wx:for-item="option" wx:for-index="optIndex" wx:key="optIndex"><u-checkbox vue-id="{{('799021c1-5-'+__i0__+'-'+optIndex)+','+('799021c1-4-'+__i0__)}}" customStyle="{{({marginRight:'30px',padding:'8rpx 0'})}}" name="{{option.value}}" label="{{option.text}}" labelSize="30rpx" iconSize="36rpx" class="data-v-03ebdbe5" bind:__l="__l"></u-checkbox></block></u-checkbox-group></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='text'}}"><view class="data-v-03ebdbe5"><u-textarea vue-id="{{'799021c1-6-'+__i0__}}" disabled="{{!isQuestionnaireEditable}}" placeholder="请输入" maxlength="{{question.$orig.maxlength||200}}" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']],['updateTextInput',['$0','$event'],[[['questionnaire.itemList','id',question.$orig.id,'id']]]]]]]}}" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l"></u-textarea></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='nps-scale'}}"><view class="question-content data-v-03ebdbe5"><u-rate vue-id="{{'799021c1-7-'+__i0__}}" customStyle="{{({width:'100%',justifyContent:'space-between',padding:'20rpx 0'})}}" size="{{26}}" readonly="{{!isQuestionnaireEditable}}" count="{{question.$orig.itemResultOption.end||5}}" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^change',[['e2']]],['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']]]]]}}" data-event-params="{{({question:question.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l"></u-rate></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='dropdown'}}"><view class="data-v-03ebdbe5"><uni-data-select vue-id="{{'799021c1-8-'+__i0__}}" localdata="{{question.$orig.itemResultOption}}" disabled="{{!isQuestionnaireEditable}}" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^change',[['e3']]],['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']]]]]}}" data-event-params="{{({question:question.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l"></uni-data-select></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='region-select'}}"><view class="data-v-03ebdbe5"><uni-data-picker vue-id="{{'799021c1-9-'+__i0__}}" localdata="{{question.$orig.itemResultOption}}" readonly="{{!isQuestionnaireEditable}}" placeholder="请选择" value="{{answers[question.$orig.id].value}}" data-event-opts="{{[['^change',[['e4']]],['^input',[['__set_model',['$0','value','$event',[]],['answers.'+question.$orig.id+'']]]]]}}" data-event-params="{{({question:question.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-03ebdbe5" bind:__l="__l"></uni-data-picker></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='matrix-single'}}"><view class="question-content matrix-simple data-v-03ebdbe5"><view class="matrix-simple-header data-v-03ebdbe5"><block wx:for="{{question.$orig.itemResultOption.cols}}" wx:for-item="column" wx:for-index="colIndex" wx:key="colIndex"><view class="matrix-simple-column data-v-03ebdbe5">{{''+column.text+''}}</view></block></view><block wx:for="{{question.$orig.itemResultOption.rows}}" wx:for-item="row" wx:for-index="rowIndex" wx:key="rowIndex"><view class="matrix-simple-row data-v-03ebdbe5"><view class="matrix-simple-row-title data-v-03ebdbe5">{{row.text}}</view><view class="matrix-simple-options data-v-03ebdbe5"><u-radio-group vue-id="{{'799021c1-10-'+__i0__+'-'+rowIndex}}" value="{{answers[question.$orig.id].value[row.value]}}" disabled="{{!isQuestionnaireEditable}}" placement="row" data-event-opts="{{[['^change',[['e5']]]]}}" data-event-params="{{({question:question.$orig,rowIndex})}}" bind:change="__e" class="data-v-03ebdbe5" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{question.$orig.itemResultOption.cols}}" wx:for-item="column" wx:for-index="colIndex" wx:key="colIndex"><view class="matrix-simple-option data-v-03ebdbe5"><u-radio vue-id="{{('799021c1-11-'+__i0__+'-'+rowIndex+'-'+colIndex)+','+('799021c1-10-'+__i0__+'-'+rowIndex)}}" name="{{column.value}}" customStyle="{{({marginBottom:'0'})}}" iconSize="40rpx" labelSize="28rpx" class="data-v-03ebdbe5" bind:__l="__l"></u-radio><view data-event-opts="{{[['tap',[['handleMatrixCellClick',['$0',rowIndex,'$1'],[[['questionnaire.itemList','id',question.$orig.id,'id']],[['questionnaire.itemList','id',question.$orig.id],['itemResultOption.cols','',colIndex,'value']]]]]]]}}" class="radio-overlay data-v-03ebdbe5" bindtap="__e"></view></view></block></u-radio-group></view></view></block></view></block><block wx:else><block wx:if="{{question.$orig.itemResultType==='matrix-multiple'}}"><view class="question-content matrix-simple data-v-03ebdbe5"><view class="matrix-checkbox-header data-v-03ebdbe5"><view class="matrix-simple-options data-v-03ebdbe5"><block wx:for="{{question.$orig.itemResultOption.cols}}" wx:for-item="column" wx:for-index="colIndex" wx:key="colIndex"><view class="matrix-simple-column data-v-03ebdbe5">{{''+column.text+''}}</view></block></view></view><block wx:for="{{question.$orig.itemResultOption.rows}}" wx:for-item="row" wx:for-index="rowIndex" wx:key="rowIndex"><view class="matrix-checkbox-row data-v-03ebdbe5"><view class="matrix-checkbox-row-title data-v-03ebdbe5">{{row.text}}</view><view class="matrix-checkbox-options data-v-03ebdbe5"><u-checkbox-group vue-id="{{'799021c1-12-'+__i0__+'-'+rowIndex}}" value="{{answers[question.$orig.id].value[row.value]||[]}}" disabled="{{!isQuestionnaireEditable}}" placement="row" data-event-opts="{{[['^change',[['e6']]]]}}" data-event-params="{{({question:question.$orig,rowIndex})}}" bind:change="__e" class="data-v-03ebdbe5" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{question.$orig.itemResultOption.cols}}" wx:for-item="column" wx:for-index="colIndex" wx:key="colIndex"><view class="matrix-checkbox-option data-v-03ebdbe5"><u-checkbox vue-id="{{('799021c1-13-'+__i0__+'-'+rowIndex+'-'+colIndex)+','+('799021c1-12-'+__i0__+'-'+rowIndex)}}" name="{{column.value}}" customStyle="{{({marginBottom:'0'})}}" class="data-v-03ebdbe5" bind:__l="__l"></u-checkbox><view data-event-opts="{{[['tap',[['handleMatrixCheckboxCellClick',['$0',rowIndex,'$1'],[[['questionnaire.itemList','id',question.$orig.id,'id']],[['questionnaire.itemList','id',question.$orig.id],['itemResultOption.cols','',colIndex,'value']]]]]]]}}" class="checkbox-overlay data-v-03ebdbe5" bindtap="__e"></view></view></block></u-checkbox-group></view></view></block></view></block></block></block></block></block></block></block></block><block wx:if="{{hasSubmitted&&validationErrors[question.$orig.id]}}"><view class="error-tip data-v-03ebdbe5"><text class="data-v-03ebdbe5">此题为必填项，请回答此题</text></view></block></view></block></view><block wx:if="{{isQuestionnaireEditable}}"><view class="submit-area data-v-03ebdbe5"><u-button vue-id="799021c1-14" type="primary" disabled="{{submitState}}" text="{{submitState?'正在提交问卷，请稍后...':'提交问卷'}}" data-event-opts="{{[['^click',[['submitQuestionnaire']]]]}}" bind:click="__e" class="data-v-03ebdbe5" bind:__l="__l"></u-button></view></block></view>