<view class="complaint-list-container data-v-685865bb"><scroll-view class="complaint-scroll data-v-685865bb" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" scroll-top="{{scrollTop}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['scroll',[['onScroll',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindscroll="__e" bindrefresherrefresh="__e"><view class="complaint-my data-v-685865bb"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="complaint-item data-v-685865bb"><view class="tag-top data-v-685865bb">{{''+item.m0+''}}</view><view class="item-top data-v-685865bb"><view class="data-v-685865bb">投诉和建议内容：</view><view class="data-v-685865bb">{{item.m1}}</view></view><view class="item-top-content data-v-685865bb"><text class="data-v-685865bb">{{item.$orig.cmContent||""}}</text></view><block wx:if="{{item.$orig.cmImg}}"><view class="item-img-box data-v-685865bb"><block wx:for="{{item.l0}}" wx:for-item="img" wx:for-index="index" wx:key="index"><view class="item-img-item data-v-685865bb"><image src="{{BASE_IMG_URL+img.$orig}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',[img.g0,index]]]]]}}" bindtap="__e" class="data-v-685865bb"></image></view></block></view></block><block wx:if="{{item.$orig.cmUserPhone}}"><view data-event-opts="{{[['tap',[['callPhone',['$0'],[[['complaintList','id',item.$orig.id,'cmUserPhone']]]]]]]}}" class="item-phone data-v-685865bb" bindtap="__e"><text class="phone-label data-v-685865bb">投诉人电话：</text><text class="phone-number data-v-685865bb">{{item.$orig.cmUserPhone}}</text><text class="phone-icon data-v-685865bb">拨打</text></view></block><block wx:if="{{item.$orig.cmReplyTime}}"><view class="item-reply-header data-v-685865bb"><view class="reply-title data-v-685865bb">回复内容：</view><view class="data-v-685865bb">{{item.m2}}</view></view></block><block wx:if="{{item.$orig.cmReplyContent}}"><view class="item-reply-content data-v-685865bb"><text class="data-v-685865bb">{{item.$orig.cmReplyContent||""}}</text></view></block><view class="reply-actions data-v-685865bb"><block wx:if="{{replyingId!==item.$orig.id}}"><view data-event-opts="{{[['tap',[['showReplyInput',['$0','$1'],[[['complaintList','id',item.$orig.id,'id']],[['complaintList','id',item.$orig.id,'cmReplyContent']]]]]]]}}" class="reply-btn data-v-685865bb" bindtap="__e">{{''+(item.$orig.cmReplyContent?"修改":"回复")+''}}</view></block><block wx:if="{{replyingId===item.$orig.id}}"><view class="reply-input-container data-v-685865bb"><u--textarea bind:input="__e" vue-id="{{'ba965aaa-1-'+__i0__}}" placeholder="请输入回复内容" height="200rpx" disabled="{{isSubmitting}}" value="{{replyContent}}" data-event-opts="{{[['^input',[['__set_model',['','replyContent','$event',[]]]]]]}}" class="data-v-685865bb" bind:__l="__l"></u--textarea><view class="reply-btn-group data-v-685865bb"><view data-event-opts="{{[['tap',[['cancelReply',['$event']]]]]}}" class="cancel-btn data-v-685865bb" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitReply',['$0'],[[['complaintList','id',item.$orig.id,'id']]]]]]]}}" class="{{['save-btn','data-v-685865bb',(isSubmitting)?'disabled':'']}}" bindtap="__e">{{''+(isSubmitting?"提交中...":"保存")+''}}</view></view></view></block></view></view></block></view><view class="loading-more data-v-685865bb"><block wx:if="{{isLoading}}"><text class="data-v-685865bb">加载中...</text></block><block wx:else><block wx:if="{{!hasMore}}"><text class="data-v-685865bb">没有更多数据了</text></block></block></view></scroll-view><block wx:if="{{showBackToTop}}"><view data-event-opts="{{[['tap',[['gotoTop',['$event']]]]]}}" class="back-to-top data-v-685865bb" bindtap="__e"><image src="/static/images/icons/totop.png" mode="scaleToFill" class="data-v-685865bb"></image></view></block></view>