/* 初始化样式
------------------------------- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
}
:root {
  --prev-bg-menuBar: #282c34;
  --prev-bg-menuBarColor: #ffffff;
  --prev-bg-topBar: #ffffff;
  --prev-bg-topBarColor: #282c34;
  --prev-bg-columnsMenuBar: #282c34;
  --prev-bg-columnsMenuBarColor: #e6e6e6;
  --prev-bg-columnsMenuActiveColor: #ffffff;
  --prev-bg-menu-hover-ba-color: rgba(0, 0, 0, 0.2);
  --prev-bg-menu-active-color: #1890ff;
  --prev-bg-main-color: #f0f2f5;
  --prev-bg-color: #f5f7fa;
  --prev-bg-white: #ffffff;
  --prev-color-primary: #1890ff;
  --prev-color-text-white: #ffffff;
  --prev-color-text-black: #000000;
  --prev-color-text-primary: #606266;
  --prev-color-text-regular: #303133;
  --prev-color-text-secondary: #909399;
  --prev-color-text-placeholder: #c0c4cc;
  --prev-color-hover: rgba(0, 0, 0, 0.04);
  --prev-color-seting-main: #e9eef3;
  --prev-color-seting-aside: #d3dce6;
  --prev-color-seting-header: #b3c0d1;
  --prev-border-color-hover: #c0c4cc;
  --prev-border-color-base: #dcdfe6;
  --prev-border-color-light: #e4e7ed;
  --prev-border-color-lighter: #ebeef5;
  --prev-border-color-extra-light: #f2f6fc;
  --prev-tag-active-color: #fff;
  --prev-MenuActiveColor: #fff;
  --prev-table-header-bag: #f2f6ff;
  --prev-color-Auxiliary-color: #19be6b;
  --prev-color-other-color: #9fdb1d;
  --prev-color-prompt-color: #f56464;
  --prev-color-warning-color: #ff7d00;
  --prev-color-endTag-color: #666666;
  --prev-color-endTag-border: #cccccc;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  background-color: var(--prev-bg-main-color);
  font-size: 14px;
}

/* 主布局样式
------------------------------- */
.layout-container {
  width: 100%;
  height: 100%;

  .layout-aside {
    background: var(--prev-bg-menuBar);
    // box-shadow: 0px 0 1px rgba(0, 21, 41, 1%);
    height: inherit;
    position: relative;
    z-index: 11;
    display: flex;
    flex-direction: column;
    overflow-x: hidden !important;

    .el-scrollbar__view {
      overflow: hidden;
    }
  }

  .layout-header {
    padding: 0 !important;
  }

  .layout-main {
    padding: 0 !important;
    overflow: hidden;
    width: 100%;
    background-color: var(--prev-bg-main-color);
    position: relative;
  }

  .el-scrollbar {
    width: 100%;
  }

  .layout-view-bg-white {
    background: var(--prev-bg-white);
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 1px solid var(--prev-border-color-lighter);
  }

  .layout-el-aside-br-color {
    // border-right: 1px solid rgb(238, 238, 238);
  }

  .layout-aside-width-default {
    width: 206px !important;
    transition: width 0.2s ease;
    box-shadow: 1px 1px 4px rgba(0, 21, 41, 0.08);
  }

  .layout-aside-width64 {
    width: 64px !important;
    transition: width 0.2s ease;
  }

  .layout-aside-width1 {
    width: 0px !important;
    transition: width 0.2s ease;
  }

  .layout-scrollbar {
    @extend .el-scrollbar;
    padding: 14px;
  }

  .layout-mian-height-50 {
    height: calc(100vh - 50px);
  }

  .layout-columns-warp {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .layout-hide {
    display: none;
  }
}

/* 进度条颜色
------------------------------- */
#nprogress .bar {
  background: var(--prev-color-primary) !important;
}

/* flex 弹性布局
------------------------------- */
.flex {
  display: flex;
}

.flex-auto {
  flex: 1;
}

.flex-center {
  @extend .flex;
  flex-direction: column !important;
  width: 100%;
  overflow: hidden;
}

.flex-margin {
  margin: auto;
}

.flex-warp {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  margin: 0 -5px;

  .flex-warp-item {
    padding: 5px;

    .flex-warp-item-box {
      width: 100%;
      height: 100%;
    }
  }
}

/* 宽高 100%
------------------------------- */
.w100 {
  width: 100% !important;
}

.h100 {
  height: 100% !important;
  min-height: calc(100vh - 186px);
}

.h101 {
  height: 100% !important;
  min-height: calc(100vh - 150px);
}

.vh100 {
  height: 100vh !important;
}

.max100vh {
  max-height: 100vh !important;
}

.min100vh {
  min-height: 100vh !important;
}

/* 颜色值
------------------------------- */
.color-primary {
  color: var(--prev-color-primary);
}

.color-success {
  color: var(--prev-color-success);
}

.color-warning {
  color: var(--prev-color-warning-color);
}
.bg-warning-color {
  background: var(--prev-color-warning-color);
}
.bg-prompt-color {
  background-color: var(--prev-color-prompt-color);
}
.color-danger {
  color: var(--prev-color-danger);
}

.color-info {
  color: var(--prev-color-info);
}

//辅助色
.colorAuxiliary {
  color: var(--prev-color-Auxiliary-color);
}

//提示色
.colorPrompt {
  color: var(--prev-color-prompt-color);
}

.color-text {
  color: var(--prev-color-text-regular) !important;
}

/* 溢出省略号
------------------------------- */
.one-text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.two-text-overflow {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.overflow {
  overflow: hidden !important;
}

.p30 {
  padding: 30px;
}

.p20 {
  padding: 20px;
}

.p40 {
  padding: 40px;
}

/* 字体大小全局样式
------------------------------- */
@for $i from 10 through 32 {
  .font#{$i} {
    font-size: #{$i}px !important;
  }
}

/* 外边距、内边距全局样式
------------------------------- */
@for $i from 5 through 35 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }

  .mr#{$i} {
    margin-right: #{$i}px !important;
  }

  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }

  .ml#{$i} {
    margin-left: #{$i}px !important;
  }

  .pt#{$i} {
    padding-top: #{$i}px !important;
  }

  .pr#{$i} {
    padding-right: #{$i}px !important;
  }

  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }

  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}
