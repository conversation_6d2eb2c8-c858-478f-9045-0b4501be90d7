// 拼接img的url地址
export function getImgUrl(url) {
    if (url) {
        if (url.indexOf(",") !== -1) {
            return url.split(",").map(item => {
                return isImageFile(item) && process.env.VUE_APP_BASE_API.replace('api/', 'downloads/') + item;
            });
        } else {
            return isImageFile(url) && [process.env.VUE_APP_BASE_API.replace('api/', 'downloads/') + url];
        }
    } else {
        return '';
    }
}

function isImageFile(filename) {
    // 使用正则表达式匹配图片文件扩展名
    // 图片:jpg|jpeg|png|gif|bmp|webp|svg|ico
    // 视频:mp4|m2v|mkv|rmvb|wmv|avi|flv|mov|m4v
    const imageRegex = /\.(jpg|jpeg|png|gif|bmp|webp|svg|ico|mp4|m2v|mkv|rmvb|wmv|avi|flv|mov|m4v)$/i;
    return imageRegex.test(filename);
}
