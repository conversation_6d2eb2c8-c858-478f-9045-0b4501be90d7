import request from '@/utils/request';

/**
 * 新增
 * @param
 */
export function addDict(pram) {
    const data = {
        dictName: pram.dictName,
        dictCode: pram.dictCode,
        dictDesc: pram.dictDesc,
    };
    return request({
        url: '/sys/dict/save',
        method: 'POST',
        data: data,
    });
}


/**
 * 修改
 * @param
 */
export function updateDict(pram) {
    const data = {
        id: pram.id,
        dictName: pram.dictName,
        dictCode: pram.dictCode,
        dictDesc: pram.dictDesc,
    };
    return request({
        url: 'sys/dict/update',
        method: 'post',
        params: { id: pram.id },
        data: data,
    });
}

/**
 * 删除
 * @param
 */
export function delDict(id) {
    return request({
        url: `sys/dict/delete/${id}`,
        method: 'post',
    });
}

/**
 * 详情
 * @param
 */
export function getInfo(pram) {
    return request({
        url: `/sys/dict/info/${pram}`,
        method: 'GET',
    });
}

/**
 * 分页列表
 * @param
 */
export function getDictList(pram) {
    const data = {
        page: pram.page,
        limit: pram.limit,
        dictName: pram.dictName,
        dictCode: pram.dictCode,
    };
    return request({
        url: '/sys/dict/list',
        method: 'get',
        params: data,
    });
}

/**
 * 分页字典子项列表
 * @param
 */
export function getDictItemList(pram) {
    const data = {
        page: pram.page,
        limit: pram.limit,
        itemName: pram.itemName,
        dictId: pram.dictId,
        itemEnable: pram.itemEnable,
    };
    return request({
        url: '/sys/dictItem/list',
        method: 'get',
        params: data,
    });
}

/**
 * 新增子项
 * @param
 */
export function addDictItem(pram) {
    const data = {
        itemName: pram.itemName,
        itemCode: pram.itemCode,
        itemDesc: pram.itemDesc,
        itemSort: pram.itemSort,
        dictId: pram.dictId,
        itemEnable: pram.itemEnable,
    };
    return request({
        url: '/sys/dictItem/save',
        method: 'POST',
        data: data,
    });
}


/**
 * 修改子项
 * @param
 */
export function updateDictItem(pram) {
    const data = {
        id: pram.id,
        itemName: pram.itemName,
        itemCode: pram.itemCode,
        itemDesc: pram.itemDesc,
        itemSort: pram.itemSort,
        itemEnable: pram.itemEnable,
    };
    return request({
        url: '/sys/dictItem/update',
        method: 'post',
        params: { id: pram.id },
        data: data,
    });
}

/**
 * 详情
 * @param
 */
export function getDictItemInfo(pram) {
    return request({
        url: `/sys/dictItem/info/${pram}`,
        method: 'GET',
    });
}

/**
 * 删除
 * @param
 */
export function delDictItem(id) {
    return request({
        url: `sys/dictItem/delete/${id}`,
        method: 'post',
    });
}

/**
 * 根据字典编码查询字典项
 * @param
 */
export function getDictItems(dictCode) {
    return request({
        url: `sys/dict/getDictItems/${dictCode}`,
        method: 'GET',
    });
}