<template>
  <div class="divBox">
    <el-card
      class="box-card"
      :body-style="{ padding: 0 }"
      :bordered="false"
      shadow="never"
      v-hasPermi="['platform:admin:list']"
    >
      <div class="padding-add">
        <el-form inline size="small" @submit.native.prevent>
          <el-form-item label="手机号码：">
            <el-input
              v-model="listPram.verifyUserPhone"
              placeholder="请输入手机号码"
              class="selWidth"
              size="small"
              @keyup.enter.native="handleGetAdminList"
              clearable />
          </el-form-item>
          <el-form-item label="核销商家：">
            <el-input
              v-model="listPram.verifyActionMerchant"
              placeholder="请输入核销商家"
              class="selWidth"
              size="small"
              @keyup.enter.native="handleGetAdminList"
              clearable />
          </el-form-item>
          <el-form-item label="核销编码：">
            <el-input
              v-model="listPram.verifyCode"
              placeholder="请输入核销编码"
              class="selWidth"
              size="small"
              @keyup.enter.native="handleGetAdminList"
              clearable />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="reset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
      <el-button size="mini" type="primary" @click="handlerOpenEdit(0)">添加{{apiConfig.name}}</el-button>
      <el-table v-loading="listLoading" class="operation mt20" :data="listData.list" size="small">
        <el-table-column type="index" fixed="left" width="50"/>
        <el-table-column label="手机号码" prop="verifyUserPhone"/>
        <el-table-column label="昵称" prop="verifyUserName" v-if="false"/>
        <el-table-column label="核销编码" prop="verifyCode"/>
        <el-table-column label="核销时间" width="220" prop="verifyTime"/>
        <el-table-column label="核销数量" prop="verifyNumber"/>
        <el-table-column label="核销商家" prop="verifyActionMerchant"/>
        <el-table-column label="核销商品" prop="verifyProductName"/>
        <el-table-column label="核销商品编号" prop="verifyProductCode"/>
        <el-table-column label="操作人" prop="verifyActionUser"/>
        <el-table-column label="操作" width="80" fixed="right">
          <template slot-scope="scope">
            <template v-if="scope.row.isDel">
              <span>-</span>
            </template>
            <template v-else>
              <el-button
                :disabled="scope.row.roles === '1'"
                type="text"
                size="mini"
                @click="handlerOpenEdit(1, scope.row)"
                v-hasPermi="['platform:admin:update', 'platform:admin:info']"
              >撤销
              </el-button>
<!--              <el-divider direction="vertical"></el-divider>-->
<!--              <el-button-->
<!--                type="text"-->
<!--                size="mini"-->
<!--                :disabled="scope.row.roles === '1'"-->
<!--                @click="handlerOpenDel(scope.row)"-->
<!--                v-hasPermi="['platform:admin:delete']"-->
<!--              >删除-->
<!--              </el-button-->
<!--              >-->
            </template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="listPram.page"
        :page-sizes="constants.page.limit"
        :layout="constants.page.layout"
        :total="listData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!--编辑-->
    <el-dialog
      top="40px"
      :visible.sync="editDialogConfig.visible"
      :title="editDialogConfig.isCreate === 0 ?  '创建' + apiConfig.name : apiConfig.name"
      destroy-on-close
      :close-on-click-modal="false"
      width="700px"
      class="dialog-bottom"
    >
      <edit
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        :dict-data="{dataConfig:  this.dict}"
        @hideEditDialog="hideEditDialog"
      />
    </el-dialog>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { verifyRecord as apiConf} from '@/utils/api-config';
import { getImgUrl } from "@/utils/imgUtil";
import * as dataApi from '@/api/business/common';
import * as dictApi from '@/api/dict';
import edit from './edit';

export default {
  // name: "index"
  components: {edit},
  computed: {
    apiConfig() {
      return apiConf || {name: '', path: ''}
    }
  },
  data() {
    return {
      constants: this.$constants,
      listData: {list: []},
      listPram: {
        verifyUserPhone: null,
        verifyActionMerchant: null,
        verifyCode: null,
        page: 1,
        limit: this.$constants.page.limit[0],
      },
      realName: '',
      roleList: [],
      menuList: [],
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
      //修改密码
      editPassWordDialogConfig: {
        visible: false,
        editData: {},
      },
      adminId: 0, //管理员id
      listLoading: false,
      keyIndex: 0,
      dict: {
        itemList: [],
        dictMap: {}
      },
    };
  },
  mounted() {
    this.handleGetAdminList();
    this.initDict();
  },
  methods: {
    getImgUrl,
    async handleGetAdminList() {
      this.listLoading = true;
      // 查询list
      this.listData = await dataApi.list(apiConf.path, this.listPram);
      this.listLoading = false;
    },
    async initDict() {
      this.dict = await dictApi.getDictItems("hotspotType");
    },
    handleSearch() {
      this.listPram.page = 1;
      this.handleGetAdminList();
    },
    handleSizeChange(val) {
      this.listPram.limit = val;
      this.handleGetAdminList();
    },
    handleCurrentChange(val) {
      this.listPram.page = val;
      this.handleGetAdminList();
    },
    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(async () => {
        try {
          await dataApi.remove(apiConf.path, rowData.id);
          this.$message.success('删除数据成功');
          this.hideEditDialog();
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    },
    //重置
    reset() {
      this.listPram.page = 1;
      this.listPram.verifyUserPhone = null;
      this.listPram.verifyActionMerchant = null;
      this.listPram.verifyCode = null;
      this.handleGetAdminList();
    },
    handlerOpenEdit(isCreate, editDate) {
      this.editDialogConfig.editData = editDate;
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetAdminList();
    },
  },
};
</script>

<style scoped lang="scss"></style>
