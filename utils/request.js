// HTTP请求工具
const BASE_URL = 'http://localhost:8080' // 根据实际后端地址修改

/**
 * 封装的请求方法
 * @param {Object} options 请求配置
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {Object} options.params 查询参数
 * @param {Object} options.header 请求头
 */
function request(options = {}) {
  return new Promise((resolve, reject) => {
    // 处理URL
    let url = options.url
    if (!url.startsWith('http')) {
      url = BASE_URL + url
    }

    // 处理查询参数
    if (options.params) {
      const queryString = Object.keys(options.params)
        .map(key => `${key}=${encodeURIComponent(options.params[key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + queryString
    }

    // 默认请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }

    // 获取token（如果有）
    const token = uni.getStorageSync('token')
    if (token) {
      header.Authorization = `Bearer ${token}`
    }

    // 发起请求
    uni.request({
      url,
      method: options.method || 'GET',
      data: options.data,
      header,
      success: (res) => {
        if (res.statusCode === 200) {
          // 根据后端返回格式调整
          if (res.data.code === 200 || res.data.success) {
            resolve(res.data.data || res.data)
          } else {
            uni.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else {
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

export default request
