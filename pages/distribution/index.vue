<template>
  <view class="distribution-container">
    <!-- 用户信息 -->
    <view class="user-info">
      <view class="avatar-container">
        <image class="avatar" src="/static/images/icons/avatar.png"></image>
      </view>
      <view class="user-name">分销员238</view>
      <view class="share-btn">
        <u-icon name="share" color="#FFFFFF" size="28"></u-icon>
        <text>分享</text>
      </view>
    </view>

    <!-- 收益卡片 -->
    <view class="earnings-card">
      <view class="earnings-row">
        <view class="earnings-item">
          <view class="earnings-label">
            累计佣金(元)  >
          </view>
          <view class="earnings-value primary">38457.98</view>
        </view>
        <view class="earnings-item">
          <view class="earnings-label">
            下线总数(人)  >
          </view>
          <view class="earnings-value">870</view>
        </view>
      </view>
    </view>

    <view class="earnings-card">
      <view class="withdraw-info">
        <view class="withdraw-item">
          <view class="withdraw-label">
            可提现(元)  >
          </view>
          <view class="withdraw-value">980.98</view>
        </view>
        <view class="withdraw-item">
        </view>
        <view class="withdraw-item">
          <view class="withdraw-label">
            提现记录  >
          </view>
        </view>
      </view>

      <view class="withdraw-info">
        <view class="withdraw-item">
          <view class="withdraw-label">待入账(元)</view>
          <view class="withdraw-value">78.90</view>
        </view>
        <view class="withdraw-item">
          <view class="withdraw-label">提现中(元)</view>
          <view class="withdraw-value">78.90</view>
        </view>
        <view class="withdraw-item">
          <view class="withdraw-label">已提现(元)</view>
          <view class="withdraw-value">78.90</view>
        </view>
      </view>

      <view class="withdraw-btn" @click="handleWithdraw">立即提现</view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-card">
      <view class="menu-item" @click="navigateTo('/pages/distribution/orders')">
        <img class="feedback-icon" src="@/static/images/me/delivery_address.png" alt="" />
        <view class="menu-text">分销订单</view>
        <u-icon name="arrow-right" color="#CCCCCC" size="14"></u-icon>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/distribution/team')">
        <img class="feedback-icon" src="@/static/images/me/delivery_address.png" alt="" />
        <view class="menu-text">我的团队</view>
        <u-icon name="arrow-right" color="#CCCCCC" size="14"></u-icon>
      </view>

      <view class="menu-item" @click="navigateTo('/pages/distribution/ranking')">
        <img class="feedback-icon" src="@/static/images/me/delivery_address.png" alt="" />
        <view class="menu-text">佣金排名</view>
        <u-icon name="arrow-right" color="#CCCCCC" size="14"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions, mapGetters } from "vuex";

export default {
  components: {},
  data() {
    return {
      statusBarHeight: 20
    };
  },
  onLoad() {
    // 获取状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
  },
  onShow() {
    // 页面显示时加载数据
    this.loadDistributionData();
  },
  computed: {
    ...mapState(["userInfo"]),
    ...mapGetters(["hasLogin"])
  },
  methods: {
    // 加载分销数据
    loadDistributionData() {
      // 这里添加获取分销数据的API调用
      console.log("加载分销数据");
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    },

    // 页面导航
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },

    // 处理提现
    handleWithdraw() {
      uni.navigateTo({
        url: '/pages/distribution/withdraw'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.distribution-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #FF6600 0%, #FF9966 50%, #F5F5F5 50%);
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: 88rpx;
  position: relative;
}

.nav-bar-left, .nav-bar-right {
  display: flex;
  align-items: center;
}

.nav-bar-title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.back-icon, .more-icon, .share-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  position: relative;
}

.avatar-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar {
  width: 100%;
  height: 100%;
}

.user-name {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.share-btn {
  position: absolute;
  right: 30rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.share-btn text {
  color: #FFFFFF;
  font-size: 28rpx;
  margin-left: 8rpx;
}

/* 收益卡片样式 */
.earnings-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.earnings-row {
  display: flex;
  justify-content: space-between;
}

.earnings-item {
  flex: 1;
}

.earnings-label {
  font-size: 28rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.earnings-value {
  font-size: 48rpx;
  color: #333333;
  font-weight: bold;
  margin-top: 10rpx;
  display: flex;
  justify-content: center;
}

.earnings-value.primary {
  color: #FF6600;
}

.divider {
  height: 1rpx;
  background: #EEEEEE;
  margin: 30rpx 0;
}

.withdraw-info {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.withdraw-item {
  flex: 1;
  text-align: center;
}

.withdraw-label {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  flex-direction: row;
  align-items: center;
  white-space: nowrap;
  justify-content: center;
}

.icon-spacer{
  display: inline-block;
  width: 5px;
}

.withdraw-value {
  font-size: 32rpx;
  color: #333333;
  margin-top: 10rpx;
}

.withdraw-btn {
  background: #FF6600;
  color: #FFFFFF;
  font-size: 32rpx;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 14rpx;
  margin-top: 30rpx;
}

/* 功能菜单样式 */
.menu-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 0 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  height: 120rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
}

.feedback-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
</style>