<template>
	<view class="notice-container">
		<view class="safety-item" v-for="item in noticeList" :key="item.id">
			<view class="safety-item-title">
				<text>{{ item.noticeTitle || "" }}</text>
				<image src="@/static/images/icons/line-b.png" mode="aspectFill" alt="" />
			</view>
			<view class="safety-item-content">
				<u-parse :content="item.noticeContent"></u-parse>
			</view>
			<view class="safety-item-time">
				{{ formatDateSimple(item.noticePushTime) }}
			</view>
		</view>
	</view>
</template>

<script>
import { getNoticeList } from "@/nxTemp/apis/common";
import tools from "@/nxTemp/utils/tools";
export default {
	data() {
		return {
			noticeList: [],
		};
	},
	onLoad() {
		this.getNoticeList();
	},
	methods: {
		// 注册工具函数到methods中，这样可以在模板中直接使用
		formatDateSimple(dateStr) {
			return tools.formatDateSimple(dateStr);
		},
		getNoticeList() {
			uni.showLoading({
				title: "加载中",
			});
			getNoticeList()
				.then((res) => {
					const { data, code, message } = res.data;
					if (code == 200) {
						this.noticeList = data && data.list ? data.list : [];
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},
	},
};
</script>

<style>
.notice-container {
	background-color: #fff;
	width: 100%;
	min-height: 100vh;
	padding: 30rpx;
}

.safety-item {
	background: #eff9fc;
	border-radius: 15rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.safety-item-title {
	margin-bottom: 27rpx;
	position: relative;
}

.safety-item-title text {
	font-weight: bold;
	font-size: 32rpx;
	color: #262626;
	line-height: 32rpx;
	z-index: 2;
	display: inline-block;
	position: relative;
}

.safety-item-title image {
	position: absolute;
	top: 22rpx;
	left: 0;
	width: 152rpx;
	height: 15rpx;
	z-index: 1;
}

.safety-item-content {
	font-weight: 400;
	font-size: 30rpx;
	color: #333;
	line-height: 42rpx;
	margin-bottom: 27rpx;
}

.safety-item-time {
	font-weight: 400;
	font-size: 28rpx;
	color: #adadad;
}
</style>
