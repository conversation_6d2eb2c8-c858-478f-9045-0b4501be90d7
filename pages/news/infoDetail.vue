<template>
	<view class="info-detail-page">
		<view class="info-detail">
			<view class="info-detail-title">{{ infoDetail.informTitle || '' }}</view>
			<view class="info-detail-box">
				<view class="info-detail-author">{{ infoDetail.informAuthor||'' }}</view>
				<view class="info-detail-time">{{ infoDetail.informPushTime||'' }}</view>
				<view class="info-detail-source">{{ infoDetail.informDataSource||'' }}</view>
			</view>
			<view class="info-detail-content">
				<u-parse :content="infoDetail.informContent"></u-parse>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getNewsInfo
	} from "@/nxTemp/apis/common.js";
	import {
		BASE_IMG_URL
	} from "@/env.js";
	export default {
		data() {
			return {
				id: "",
				title: "金梭资讯",
				infoDetail: {
					informTitle: "", // 标题
					informAuthor: "", // 作者
					informPushTime: "", // 时间
					informDataSource: "", // 来源
					informContent: "", // 内容
				}, // 资讯详情
			};
		},
		onLoad(options) {
			this.id = options?.id;
			uni.setNavigationBarTitle({
				title: this.title,
			});
		},
		watch: {
			id: {
				handler(newVal) {
					this.getInfoDetail(newVal);
				},
			},
		},
		methods: {
			// 获取详情
			getInfoDetail() {
				uni.showLoading({
					title: "加载中...",
				});
				getNewsInfo({
						id: this.id,
					})
					.then((res) => {
						const {
							data,
							code,
							message
						} = res.data;
						if (code === 200) {
							this.infoDetail = {
								...data
							};
						} else {
							uni.$u.toast(message);
						}
					})
					.finally(() => {
						uni.hideLoading();
					});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.info-detail-page {
		width: 100%;
		min-height: 100vh;
		padding: 0 30rpx;
		padding-top: 58rpx;
		padding-bottom: 30rpx;
    background-color: #E9F4F7;
	}

	.info-detail {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 45rpx 30rpx;
    margin-bottom: 30rpx;
	}

	.info-detail-title {
		font-weight: 500;
		font-size: 42rpx;
		color: #262626;
		line-height: 56rpx;
	}

	.info-detail-box {
		display: flex;
		align-items: center;
		padding-top: 35rpx;
		padding-bottom: 41rpx;
	}

	.info-detail-author {
		font-weight: 400;
		font-size: 28rpx;
		color: #2da6c4;
		line-height: 56rpx;
	}

	.info-detail-time {
		font-weight: 400;
		font-size: 28rpx;
		color: #adadad;
		line-height: 56rpx;
		padding-left: 30rpx;
		padding-right: 40rpx;
	}

	.info-detail-source {
		font-weight: 400;
		font-size: 28rpx;
		color: #adadad;
		line-height: 56rpx;
	}
</style>