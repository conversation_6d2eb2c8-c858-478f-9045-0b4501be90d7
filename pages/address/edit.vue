<template>
  <view class="edit-address-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <view class="form-item">
      </view>

      <!-- 收货人 -->
      <view class="form-item">
        <view class="form-label">收货人</view>
        <input
          class="form-input"
          v-model="formData.name"
          placeholder="请输入收货人姓名"
          maxlength="20"
          placeholder-style="color: #cccccc;"
        />
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <view class="form-label">手机号</view>
        <input
          class="form-input"
          v-model="formData.phone"
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          placeholder-style="color: #cccccc;"
        />
      </view>

      <!-- 所在地区 -->
      <view class="form-item region-item">
        <view class="form-label">所在地区</view>
        <view class="region-selector">
          <text @click="selectRegion" v-if="regionText" class="region-text">{{ regionText }}</text>
          <text @click="selectRegion" v-else class="placeholder">请选择省市街道</text>
          <view class="location-icon" @click="getLocation">
            <u-icon name="map" color="#FF6C05" size="20"></u-icon>
            <text class="location-te">定位</text>
          </view>
        </view>
      </view>

      <!-- 详细地址 -->
      <view class="form-item detail-item">
        <view class="form-label">详细地址</view>
        <textarea
          class="form-textarea"
          v-model="formData.detail"
          placeholder="请输入小区楼栋、门牌号、村等"
          maxlength="100"
          auto-height
          placeholder-style="color: #cccccc;"
        ></textarea>
      </view>

      <!-- 智能贴贴 -->
      <view class="smart-paste">
        <view class="smart-paste-card" v-if="showSmartPaste">
          <view class="smart-paste-header">
            <img class="feedback-icon" src="@/static/images/me/discern.png" alt="" />
            <text class="smart-paste-title">智能贴贴</text>
          </view>
          <view class="smart-paste-content">
            <text class="smart-paste-text">粘贴文本到此处，将自动识别收货信息</text>
            <textarea
                class="smart-paste-textarea"
                placeholder="例:张三，139*******，云南省大理市金桔路...."
                placeholder-style="color: #cccccc;"
                v-model="pasteContent"
                auto-height
                maxlength="200"
            ></textarea>
          </view>
          <view class="smart-paste-actions">
            <view class="paste-btn clear-btn" @click="clearPaste">
              <text>清除</text>
            </view>
            <view class="paste-btn confirm-btn" @click="confirmAddress(pasteContent)">
              <text>粘贴并识别</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 设为默认地址 -->
      <view class="default-address-item">
        <text class="default-label">设为默认地址</text>
        <view class="custom-switch">
          <u-switch
              v-model="formData.isDefault"
              @change="toggleDefault"
              activeColor="#007aff"
              inactiveColor="#E4E4E4"
              size="25"
              space="0"
          ></u-switch>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-btn" @click="saveAddress">
      <text>保存地址</text>
    </view>

    <!-- 地区选择弹出框 -->
    <view class="region-popup" v-if="showRegionPicker" @click="closeRegionPicker">
      <view class="region-popup-content" @click.stop>
        <!-- 标题和关闭按钮 -->
        <view class="popup-header">
          <text class="popup-title">所在地区</text>
          <view class="close-btn" @click="closeRegionPicker">
            <u-icon name="close-circle-fill" color="#666666" size="20"></u-icon>
          </view>
        </view>

        <!-- 当前定位区域 -->
        <view class="curr-location">
          <view class="current-location">
            <view class="location-info">
              <text class="location-text">当前定位</text>
              <text class="location-address">{{ currentLocation }}</text>
            </view>
            <view class="use-btn" @click="useCurrentLocation">
              <text>使用</text>
            </view>
          </view>
        </view>

        <!-- 地区选择tabs -->
        <view class="region-tabs-container">
          <u-tabs
            :list="regionTabsList"
            :current="currentTabIndex"
            @change="switchTab"
            :scrollable="true"
            activeColor="#FF6600"
            inactiveColor="#666666"
            lineColor="#FF6600"
            lineWidth="40"
            lineHeight="4"
          ></u-tabs>
        </view>

        <!-- 地区列表 -->
        <scroll-view class="region-list" scroll-y>
          <view
            class="region-item"
            v-for="(item, index) in currentRegionList[currentTabIndex]"
            :key="index"
            @click="selectRegionItem(item)"
          >
            <view class="region-content">
              <text class="region-simple">{{item.simple}}</text>
              <text class="region-name" :style="{color: item.id === regionTabs[currentTabIndex].id ? '#FF6600' : '#1A1A1A'}">{{item.name}}</text>
            </view>
            <u-icon
              v-if="item.id === regionTabs[currentTabIndex].id"
              name="checkmark"
              color="#FF6600"
              size="20"
            ></u-icon>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAreaList } from "@/nxTemp/apis/shopping";
import {
  AddressParse
} from "@/uni_modules/Winglau14-address-auto-parse/components/Winglau14-address-auto-parse/lib/addressParseBundle";

export default {
  data() {
    return {
      isEdit: false,
      editIndex: -1,
      formData: {
        id: null,
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detail: '',
        isDefault: false
      },
      showRegionPicker: false,
      showSmartPaste: true,
      currentLocation: '',
      currentTabIndex: 0,
      selectedRegion: {
        province: '',
        city: '',
        district: '',
        street: ''
      },
      regionTabs: [
        { id: '', name: '请选择', pid: '' }
      ],
      currentRegionList:[],
      // 地区数据
      regionData: {
      },
      // 粘贴内容
      pasteContent: '',
    };
  },
  computed: {
    regionText() {
      const parts = [];
      if (this.selectedRegion.province) parts.push(this.selectedRegion.province);
      if (this.selectedRegion.city) parts.push(this.selectedRegion.city);
      if (this.selectedRegion.district) parts.push(this.selectedRegion.district);
      if (this.selectedRegion.street) parts.push(this.selectedRegion.street);
      return parts.join(' ');
    },

    regionTabsList() {
      return this.regionTabs.map(tab => ({
        name: tab.name
      }));
    },
  },
  onLoad(options) {
    if (options.id) {
      this.isEdit = true;
      this.editIndex = parseInt(options.index || -1);
      this.loadAddressData(options.id);
    }
    this.initArea("", 0);
  },
  methods: {
    // init 地区的信息
    async initArea(pid, index) {
      try {
        const res = await getAreaList(pid);
        const { data, code, message } = res.data;
        if (code === 200) {
          if (data && data.list && data.list.length > 0) {
            this.currentRegionList[index] = data.list;
          }
        } else {
          throw new Error(message)
        }
      } catch (error) {
        console.error("获取地区的信息列表失败", error);
      }
    },


    // 加载地址数据
    loadAddressData(id) {
      // 这里应该从API获取地址数据，现在模拟数据
      this.formData = {
        id: id,
        name: '赵三',
        phone: '13700987907',
        province: '云南省',
        city: '昆明市',
        district: '五华区',
        detail: '丽阳新城34栋7单元403',
        isDefault: false
      };
    },
    
    // 选择地区
    selectRegion() {
      this.showRegionPicker = true;
      this.initRegionTabs();
    },

    // 初始化地区tabs
    initRegionTabs() {
      this.regionTabs = [{ id: '', name: '请选择', pid: '' }];
      this.currentTabIndex = 0;
    },

    // 关闭地区选择器
    closeRegionPicker() {
      this.showRegionPicker = false;
    },

    // 使用当前定位
    useCurrentLocation() {
      const locationParts = this.currentLocation.split(' ');
      this.selectedRegion.province = locationParts[0] || '';
      this.selectedRegion.city = locationParts[1] || '';
      this.selectedRegion.district = locationParts[2] || '';
      this.selectedRegion.street = locationParts[3] || '';

      // 更新表单数据
      this.formData.province = this.selectedRegion.province;
      this.formData.city = this.selectedRegion.city;
      this.formData.district = this.selectedRegion.district;

      this.closeRegionPicker();
    },

    // 切换tab
    switchTab(e) {
      this.currentTabIndex = e.index;
      // this.removeTab();
    },

    // 选择地区项
    async selectRegionItem(item) {
      const index = this.currentTabIndex
      // 修改上一项的信息
      this.regionTabs[index] =  {...item};
      // 获取下一级的对象
      await this.initArea(item.id, index + 1);
      if (index < 3) {
        // 添加tab
        this.addTab({id: '', name: '请选择', pid: ''});
      } else {
        this.refreshTab(item);
      }
      this.setCurrentLocation(item.name, index);
    },

    // 添加tab
    addTab(item) {
      // 移除当前tab之后的所有tab
      this.regionTabs = this.regionTabs.slice(0, this.currentTabIndex + 1);
      // 添加新tab
      this.regionTabs.push(item);
      // 切换到新tab
      this.currentTabIndex = this.regionTabs.length - 1;
    },

    // 更新tab
    refreshTab(item) {
      // 获取到当前的tab
      const tabs = [];
      const length = this.regionTabs.length - 1;
      for (let i=0; i < length; i++) {
        tabs.push(this.regionTabs[i]);
      }
      tabs.push(item);
      this.regionTabs = tabs;
    },

    // 设置当前位置的字符
    setCurrentLocation(name, index) {
      const location = this.currentLocation.split(' ');
      location[index] = name;
      location.splice(index + 1);
      this.currentLocation = location.join(' ');
    },

    // 根据名称获取代码
    getCodeByName(name, list) {
      const item = list.find(item => item.name === name);
      return item ? item.code : '';
    },

    // 获取当前城市列表
    getCurrentCities() {
      const provinceCode = this.getCodeByName(this.selectedRegion.province, this.regionData.provinces);
      return this.regionData.cities[provinceCode] || [];
    },

    // 获取当前区县列表
    getCurrentDistricts() {
      const cityCode = this.getCodeByName(this.selectedRegion.city, this.getCurrentCities());
      return this.regionData.districts[cityCode] || [];
    },
    
    // 切换默认地址
    toggleDefault(e) {
      this.formData.isDefault = e.detail.value;
    },

    // 跳转到map
    getLocation() {
      uni.navigateTo({
        url: '/pages/address/map'
      });
    },

    // 获取map页面传递回来的数据
    setAddressFromMap(mapData) {
      if (mapData) {
        // 设置经纬度
        this.formData.latitude = mapData.latitude;
        this.formData.longitude = mapData.longitude;

        // 解析地址信息
        if (mapData.address) {
          // 尝试从地址中提取省市区信息
          const addressParts = mapData.address.split(',');
          if (addressParts && addressParts.length > 0) {
            this.confirmAddress(addressParts[0], mapData.name)
          }
        }
      }
    },

    // 智能自动获取信息
    confirmAddress(address, detail) {
      const addr = this.confirmPaste(address);
      if (addr && addr.length > 0) {
        const {province, city, area, details, mobile, name, phone} = addr[0];
        // 更新选中的地区信息
        this.selectedRegion.province = province;
        this.selectedRegion.city = city;
        this.selectedRegion.district = area;
        if (detail) {
          this.selectedRegion.street = details;
          // 设置详细地址
          this.formData.detail = detail;
        } else {
          // 设置详细地址
          this.formData.detail = details;
        }
        // 设置收件人
        this.formData.name = name;
        // 设置联系电话
        this.formData.phone = phone || mobile;
      }
    },
    
    // 验证表单
    validateForm() {
      if (!this.formData.name.trim()) {
        uni.showToast({
          title: '请输入收货人姓名',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.phone.trim()) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        });
        return false;
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.province || !this.formData.city || !this.formData.district) {
        uni.showToast({
          title: '请选择所在地区',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.detail.trim()) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 清除智能贴贴
    clearPaste() {
      this.pasteContent = "";
    },

    // 确认粘贴并识别
    confirmPaste(text) {
      return AddressParse.parse(text)
    },

    // 保存地址
    saveAddress() {
      if (!this.validateForm()) {
        return;
      }

      uni.showLoading({
        title: '保存中...'
      });

      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: this.isEdit ? '修改成功' : '添加成功',
          icon: 'success'
        });

        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
.edit-address-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 140rpx;
}

.form-container {
  padding: 30rpx;
}
/* 表单项样式 */
.form-item {
  background: #FFFFFF;
  padding: 14rpx 30rpx;
  display: flex;
  align-items: flex-start;
}

.form-label {
  width: 131rpx;
  flex-shrink: 0;
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  line-height: 78rpx;
  height: 78rpx;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  padding: 20rpx;
  border: none;
  background: #F0F0F0;
  border-radius: 9rpx;
  height: 80rpx;
}

/* 地区选择项 */
.region-item {
  align-items: center;
}

.region-selector {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F0F0F0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
}

.region-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.placeholder {
  font-size: 28rpx;
  color: #CCCCCC;
}

.location-icon {
  display: flex;
  align-items: center;

}

.location-te {
  font-size: 28rpx;
  color: #FF6C05;
  font-weight: 400;
  white-space: nowrap; /* 禁止文字换行 */
  overflow: hidden; /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

/* 详细地址项 */
.detail-item {
  align-items: flex-start;
}

.form-textarea {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: #F0F0F0;
  border-radius: 8rpx;
  padding: 20rpx;
  min-height: 120rpx;
  border: none;
}

/* 智能贴贴卡片 */
.smart-paste {
  background: #FFFFFF;
  padding: 30rpx;
}

.smart-paste-card {
  border-radius: 16rpx;
  padding: 30rpx;
  border: 2rpx solid #FF6600;
}

.smart-paste-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.smart-paste-title {
  margin-left: 12rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #FF6C05;
}

.smart-paste-content {
  margin-bottom: 30rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #696F73;
  line-height: 38rpx;
}

.smart-paste-text {
  display: block;
  margin-bottom: 10rpx;
}

.smart-paste-example {
  display: block;
}

.smart-paste-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.paste-btn {
  padding: 12rpx 24rpx;
  border-radius: 10rpx;
  text-align: center;
}

.clear-btn {
  background: #FFFFFF;
  border: 1rpx solid #FF6C05;
}

.clear-btn text {
  color: #FF6C05;
  font-weight: 400;
  font-size: 28rpx;
}

.confirm-btn {
  background: #FF6C05;
}

.confirm-btn text {
  color: #FFFFFF;
  font-weight: 400;
  font-size: 28rpx;
}

/* 默认地址设置 */
.default-address-item {
  background: #FFFFFF;
  padding: 30rpx;
  padding-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.default-label {
  font-size: 28rpx;
  color: #333333;
}

/* 保存按钮 */
.save-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 130rpx;;
  background: #FFFFFF;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.save-btn text {
  font-weight: 400;
  font-size: 34rpx;
  color: #FD9218;
}

.save-btn:active {
  background: #E55A00;
}

.feedback-icon {
  width: 30rpx;
  height: 30rpx;
}

.form-textarea.form-textarea {
  padding: 10rpx !important;
}

.uni-input-placeholder {
  color: #cccccc !important;
}

.uni-textarea-placeholder {
  color: #cccccc !important;
}

/* 地区选择弹出框样式 */
.region-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.region-popup-content {
  width: 100%;
  background: #FFFFFF;
  height: 980rpx;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.popup-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 当前定位区域 */
.curr-location {
  padding: 0rpx 30rpx;
  height: 74rpx;
}
.current-location {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0rpx 18rpx;
  justify-content: space-between;
  border-bottom: 1rpx solid #F5F5F5;
  background: #F5F5F5;
  border-radius: 9rpx;
}

.location-info {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.location-text {
  margin: 0 16rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #999999;
  line-height: 74rpx;
  white-space: nowrap; /* 禁止文字换行 */
  overflow: hidden; /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis; /* 显示省略号 */
  width: 21%;
}

.location-address {
  //width: 81%;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 74rpx;
  white-space: nowrap; /* 禁止文字换行 */
  overflow: hidden; /* 隐藏超出容器宽度的部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.use-btn {
  background: #FF6600;
  color: #FFFFFF;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.use-btn text {
  font-size: 24rpx;
  color: #FFFFFF;
}

/* 地区tabs容器 */
.region-tabs-container {
  border-bottom: 2rpx solid #cccccc;
  margin-top: 32rpx;
  height: 76rpx;
}

/* 地区列表 */
.region-list {
  flex: 1;
  height: 400rpx;
  padding-bottom: 40rpx;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 30rpx;
}

.region-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.region-letter {
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
  margin-right: 24rpx;
}

.region-simple {
  font-weight: 400;
  font-size: 30rpx;
  color: #666666;
  line-height: 32rpx;
  margin-left: 8rpx;
}

.region-name {
  font-weight: 400;
  font-size: 30rpx;
  color: #1A1A1A;
  line-height: 32rpx;
  margin-left: 63rpx;
}

.region-item:active {
  background: #F5F5F5;
}

::v-deep .u-tabs__wrapper__nav__item__text {
  font-weight: 400;
  font-size: 30rpx;
  color: #FF6C05 !important;
  line-height: 32rpx;
}
</style>
