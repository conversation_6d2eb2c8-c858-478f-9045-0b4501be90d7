<template>
	<view class="complaint-list-container">
		<!-- 使用uniapp提供的下拉刷新组件 -->
		<scroll-view
			scroll-y
			class="complaint-scroll"
			@scrolltolower="loadMore"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@scroll="onScroll"
			:scroll-top="scrollTop"
			@refresherrefresh="onRefresh">
			<!-- 投诉列表 -->
			<view class="complaint-my">
				<view class="complaint-item" v-for="item in complaintList" :key="item.id">
					<view class="tag-top">
						{{ getDictLabel(item.cmType) }}
					</view>
					<view class="item-top">
						<view>投诉和建议内容：</view>
						<view>{{ formatDateSimple(item.cmTime) }}</view>
					</view>
					<!-- 投诉内容 -->
					<view class="item-top-content">
						<text>{{ item.cmContent || "" }}</text>
					</view>
					<!-- 图片 -->
					<view class="item-img-box" v-if="item.cmImg">
						<view class="item-img-item" v-for="(img, index) in item.cmImg.split(',')" :key="index">
							<image
								:src="BASE_IMG_URL + img"
								mode="aspectFit"
								@click="previewImage(item.cmImg.split(','), index)"></image>
						</view>
					</view>

					<!-- 添加投诉人电话到图片区域下方 -->
					<view class="item-phone" v-if="item.cmUserPhone" @click="callPhone(item.cmUserPhone)">
						<text class="phone-label">投诉人电话：</text>
						<text class="phone-number">{{ item.cmUserPhone }}</text>
						<text class="phone-icon">拨打</text>
					</view>

					<!-- 回复 -->
					<view v-if="item.cmReplyTime" class="item-reply-header">
						<view class="reply-title">回复内容：</view>
						<view>{{ formatDateSimple(item.cmReplyTime) }}</view>
					</view>
					<view class="item-reply-content" v-if="item.cmReplyContent">
						<text>{{ item.cmReplyContent || "" }}</text>
					</view>

					<!-- 添加回复按钮和文本域 - 已修改为支持编辑 -->
					<view class="reply-actions">
						<view
							class="reply-btn"
							@click="showReplyInput(item.id, item.cmReplyContent)"
							v-if="replyingId !== item.id">
							{{ item.cmReplyContent ? "修改" : "回复" }}
						</view>
						<view class="reply-input-container" v-if="replyingId === item.id">
							<u--textarea
								v-model="replyContent"
								placeholder="请输入回复内容"
								height="200rpx"
								:disabled="isSubmitting"></u--textarea>
							<view class="reply-btn-group">
								<view class="cancel-btn" @click="cancelReply">取消</view>
								<view
									class="save-btn"
									@click="submitReply(item.id)"
									:class="{ disabled: isSubmitting }">
									{{ isSubmitting ? "提交中..." : "保存" }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-more">
				<text v-if="isLoading">加载中...</text>
				<text v-else-if="!hasMore">没有更多数据了</text>
			</view>
		</scroll-view>
		<view class="back-to-top" v-if="showBackToTop" @click="gotoTop">
			<image src="/static/images/icons/totop.png" mode="scaleToFill"></image>
		</view>
	</view>
</template>

<script>
import { getComplaintsList, getDictList, postComplaintReply } from "@/nxTemp/apis/common";
import { mapGetters } from "vuex";
import { BASE_IMG_URL } from "@/env.js";
import tools from "@/nxTemp/utils/tools";

export default {
	data() {
		return {
			complaintList: [], // 投诉列表数据
			complaintTypeList: [], // 投诉类型列表
			page: 1, // 当前页码
			limit: 10, // 每页数量
			hasMore: true, // 是否有更多数据
			isLoading: false, // 是否正在加载
			isRefreshing: false, // 是否正在刷新
			replyingId: null, // 当前正在回复的投诉ID
			replyContent: "", // 回复内容
			isSubmitting: false, // 是否正在提交回复
			// 返回顶部
			showBackToTop: false,
			scrollTop: 0,
			old: {
				scrollTop: 0,
			},
		};
	},
	onLoad() {
		this.getDictListFun();
		this.getComplaintList(true);
	},
	computed: {
		...mapGetters(["token"]),
		BASE_IMG_URL() {
			return BASE_IMG_URL;
		},
	},
	methods: {
		onScroll(e) {
			this.showBackToTop = e.detail.scrollTop > 500;
			this.old.scrollTop = e.detail.scrollTop;
		},
		gotoTop() {
			this.scrollTop = this.old.scrollTop;
			this.$nextTick(function () {
				this.scrollTop = 0;
			});
		},
		// 工具函数 - 日期格式化
		formatDateSimple(dateStr) {
			return tools.formatDateSimple(dateStr);
		},

		// 工具函数 - 获取字典标签
		getDictLabel(val) {
			let text = "";
			if (val && this.complaintTypeList.length > 0) {
				text = this.complaintTypeList.find((item) => item.value == val)?.label || "";
			}
			return text;
		},

		// 获取字典列表
		async getDictListFun() {
			try {
				const res = await getDictList("complaintsType");
				const { data, code, message } = res.data;
				if (code === 200) {
					this.complaintTypeList = data.itemList || [];
				}
			} catch (error) {
				console.error("获取字典列表失败", error);
			}
		},

		// 获取投诉列表
		getComplaintList(isRefresh = false) {
			if (this.isLoading) return;

			// 如果是刷新，重置页码
			if (isRefresh) {
				this.page = 1;
				this.hasMore = true;
			}

			this.isLoading = true;

			// 显示加载中
			if (!this.isRefreshing) {
				uni.showLoading({
					title: "加载中",
				});
			}

			getComplaintsList({ page: this.page, limit: this.limit })
				.then((res) => {
					const { data, code, message } = res.data;
					if (code === 200) {
						const list = data.list || [];

						// 如果是刷新，替换列表；否则追加到列表
						if (isRefresh) {
							this.complaintList = list;
						} else {
							this.complaintList = [...this.complaintList, ...list];
						}

						// 判断是否有更多数据
						this.hasMore = list.length >= this.limit;

						// 如果有更多数据，页码加1
						if (this.hasMore) {
							this.page++;
						}
					} else {
						uni.$u.toast(message);
					}
				})
				.catch((error) => {
					console.error("获取投诉列表失败", error);
					uni.$u.toast("获取数据失败，请稍后再试");
				})
				.finally(() => {
					this.isLoading = false;
					this.isRefreshing = false;
					uni.hideLoading();
				});
		},

		// 下拉刷新
		onRefresh() {
			this.isRefreshing = true;
			this.getComplaintList(true);
		},

		// 上拉加载更多
		loadMore() {
			if (!this.isLoading && this.hasMore) {
				this.getComplaintList();
			}
		},

		// 预览图片
		previewImage(urls, current) {
			const fullUrls = urls.map((url) => BASE_IMG_URL + url);
			uni.previewImage({
				urls: fullUrls,
				current: current,
				indicator: "default",
				loop: true,
			});
		},

		// 拨打电话
		callPhone(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber: phoneNumber,
				success: () => {
					console.log("拨打电话成功");
				},
				fail: (err) => {
					console.log("拨打电话失败", err);
				},
			});
		},

		// 显示回复输入框 - 已修改为支持预填内容
		showReplyInput(id, existingContent) {
			this.replyingId = id;
			this.replyContent = existingContent || "";
		},

		// 取消回复
		cancelReply() {
			this.replyingId = null;
			this.replyContent = "";
		},

		// 提交回复
		submitReply(id) {
			// 验证回复内容
			if (!this.replyContent.trim()) {
				uni.$u.toast("请输入回复内容");
				return;
			}
			uni.showLoading({
				title: "正在提交...",
			});

			this.isSubmitting = true;

			// 调用API提交回复
			postComplaintReply({
				id: id,
				cmReplyContent: this.replyContent,
			})
				.then((res) => {
					uni.hideLoading();
					const { code, message, data } = res.data;
					if (code === 200) {
						// 提交成功，更新本地数据
						// 更新本地数据
						const index = this.complaintList.findIndex((item) => item.id === id);
						if (index !== -1) {
							// 更新回复内容
							this.complaintList[index].cmReplyContent = this.replyContent;

							// 直接使用后端返回的时间，不管是修改还是首次回复
							if (data) {
								this.complaintList[index].cmReplyTime = data;
							}
							// 如果后端没有返回时间，则不更新时间字段
						}

						// 隐藏文本域
						this.replyingId = null;
						this.replyContent = "";
					} else {
						uni.$u.toast(message);
					}
				})
				.catch((error) => {
					uni.hideLoading();
					console.error("提交回复失败", error);
					uni.$u.toast("提交回复失败，请稍后再试");
				})
				.finally(() => {
					this.isSubmitting = false;
				});
		},
	},
};
</script>

<style scoped>
.complaint-list-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
	position: relative;
}

.complaint-scroll {
	flex: 1;
	box-sizing: border-box;
	padding: 30rpx;
	height: 100%;
}

.complaint-my {
	padding-bottom: 30rpx;
}

.complaint-item {
	background: #fff;
	border-radius: 15rpx;
	position: relative;
	padding: 30rpx 25rpx;
	margin-bottom: 30rpx;
}

.tag-top {
	position: absolute;
	top: 0;
	left: 0;
	width: 99rpx;
	height: 46rpx;
	background: #f89235;
	border-radius: 15rpx 0rpx 15rpx 0rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 24rpx;
	color: #ffffff;
	line-height: 1;
}

.item-top {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 400;
	font-size: 26rpx;
	color: #adadad;
	line-height: 1;
	margin-top: 36rpx;
	margin-bottom: 21rpx;
}

.item-top-content {
	font-weight: 400;
	font-size: 28rpx;
	color: #484848;
	line-height: 44rpx;
}

.item-img-box {
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 20rpx;
}

.item-img-item {
	width: 170rpx;
	height: 170rpx;
	background: #def0f0;
	border-radius: 15rpx;
	overflow: hidden;
}

.item-img-item image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.item-reply-header {
	margin-top: 40rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 400;
	font-size: 24rpx;
	color: #2da6c4;
	line-height: 1;
}

.reply-title {
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 26rpx;
	color: #adadad;
	line-height: 1;
}

.item-reply-content {
	font-weight: 400;
	font-size: 28rpx;
	color: #484848;
	line-height: 44rpx;
}

/* 添加电话样式 */
.item-phone {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 15rpx 20rpx;
	background: rgba(45, 166, 196, 0.1);
	border-radius: 10rpx;
	margin-top: 30rpx;
}

.phone-label {
	font-size: 26rpx;
	color: #adadad;
	font-weight: 400;
}

.phone-number {
	flex: 1;
	font-size: 28rpx;
	color: #2da6c4;
	margin-left: 10rpx;
}

.phone-icon {
	font-size: 24rpx;
	color: #ffffff;
	background-color: #2da6c4;
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
}

.reply-actions {
	margin-top: 40rpx;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.reply-btn {
	width: 117rpx;
	height: 50rpx;
	background: #60b2ff;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 26rpx;
	color: #ffffff;
	line-height: 1;
}

.reply-input-container {
	width: 100%;
	padding: 20rpx;
	background: #f0f0f0;
	border-radius: 15rpx;
}

.reply-btn-group {
	margin-top: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.cancel-btn {
	width: 117rpx;
	height: 50rpx;
	background: #999;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 26rpx;
	color: #ffffff;
	line-height: 1;
}

.save-btn {
	width: 117rpx;
	height: 50rpx;
	background: #60b2ff;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 26rpx;
	color: #ffffff;
	line-height: 1;
}

.loading-more {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 24rpx;
}

.empty-state {
	height: 50vh;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #999;
}
.back-to-top {
	position: fixed;
	bottom: 100rpx;
	right: 30rpx;
	background-color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}
.back-to-top image {
	width: 100%;
	height: 100%;
}
</style>
