<template>
	<view class="complaints-container">
		<u-navbar
			bgColor="transparent"
			leftIconSize="40rpx"
			leftIconColor="#fff"
			:autoBack="true"></u-navbar>
		<image
			class="bg-header"
			:src="allImgList.complaints_bg"
			mode="">
		</image>

		<view class="complaints-body">
			<view class="complaints-content">
				<view class="header-tab">
					<view class="tab-item" :class="{ active: current === 0 }" @click="changeCurrent(0)">
						<text>投诉建议</text>
						<image
							v-if="current === 0"
							class="tab-img"
							src="@/static/images/icons/bg-img.png"
							mode="">
						</image>
					</view>
					<view class="tab-item" :class="{ active: current === 1 }" @click="changeCurrent(1)">
						<text>我的投诉</text>
						<image
							v-if="current === 1"
							class="tab-img"
							src="@/static/images/icons/bg-img.png"
							mode="">
						</image>
					</view>
				</view>

				<!-- 投诉建议 -->
				<view class="current-0" v-if="current === 0">
					<u--textarea
						height="281rpx"
						v-model="cmContent"
						placeholder="请输入您的投诉与建议"></u--textarea>
					<view class="type-title">请选择投诉类型</view>
					<view class="type-list">
						<view
							v-for="(item, index) in complaintTypeList"
							:key="item.value"
							class="type-item"
							:class="{ 'type-active': cmType === item.value }"
							@click="selectType(item.value)">
							{{ item.label }}
						</view>
					</view>
					<uni-file-picker
						ref="files"
						:auto-upload="false"
						file-mediatype="image"
						limit="3"
						@select="onFileSelect"
						@delete="onFileDelete"
						title="最多选择3张图片"></uni-file-picker>
				</view>

				<!-- 我的投诉 -->
				<view class="complaint-my" v-if="current === 1">
					<view class="complaint-item" v-for="item in complaintList" :key="item.id">
						<view class="tag-top">
							{{ getDictLabel(item.cmType) }}
						</view>
						<view class="item-top">
							<view>投诉和建议内容：</view>
							<view>{{ formatDateSimple(item.cmTime) }}</view>
						</view>
						<!-- 投诉内容	 -->
						<view class="item-top-content">
							<text>{{ item.cmContent || "" }}</text>
						</view>
						<!-- 图片 -->
						<view class="item-img-box" v-if="item.cmImg">
							<view
								class="item-img-item"
								v-for="(img, index) in item.cmImg.split(',')"
								:key="index">
								<image
									:src="BASE_IMG_URL + img"
									mode="aspectFit"
									@click="previewImage(item.cmImg.split(','), index)"></image>
							</view>
						</view>

						<!-- 回复 -->
						<view v-if="item.cmReplyTime" class="item-reply-header">
							<view class="reply-title">回复：</view>
							<view>{{ formatDateSimple(item.cmReplyTime) }}</view>
						</view>
						<view class="item-reply-content">
							<text>{{ item.cmReplyContent || "" }}</text>
						</view>
					</view>
					<view class="empty-state" v-if="complaintList.length === 0">暂无数据</view>
				</view>
			</view>
			<view class="submit-btn" v-if="current === 0" @click="submit"> 确认提交 </view>
		</view>
	</view>
</template>

<script>
import { getComplaintsList, postComplaintsSave, getDictList } from "@/nxTemp/apis/common";
import { mapGetters, mapState } from "vuex";
import { BASE_URL, BASE_IMG_URL } from "@/env.js";
import tools from "@/nxTemp/utils/tools";

export default {
	data() {
		return {
			current: 0,
			cmContent: "", //投诉内容
			cmType: "", //投诉类型
			cmImg: [], // 存储文件信息
			selectedFiles: [], // 存储选择的文件信息
			complaintTypeList: [], // 投诉类型列表
			complaintList: [], // 投诉列表数据
			selectImg: [], //选择的图片
			isUploading: false, // 是否正在上传
			uploadedImages: [], // 已上传的图片URL列表
		};
	},
	onLoad() {
		this.getDictListFun();
	},
	computed: {
		...mapGetters(["token"]),
		...mapState(["allImgList"]),
		BASE_IMG_URL() {
			return BASE_IMG_URL;
		},
	},
	methods: {
		// 注册工具函数到methods中，这样可以在模板中直接使用
		formatDateSimple(dateStr) {
			return tools.formatDateSimple(dateStr);
		},
		// 获取字典label
		getDictLabel(val) {
			let text = "";
			if (val && this.complaintTypeList.length > 0) {
				text = this.complaintTypeList.find((item) => item.value == val)?.label || "";
			}
			return text;
		},
		// 获取字典列表
		async getDictListFun() {
			const res = await getDictList("complaintsType");
			const { data, code, message } = res.data;
			this.complaintTypeList = data.itemList || [];
		},
		// 切换tab
		changeCurrent(index) {
			if (this.current != index) {
				this.current = index;
				if (index === 1) {
					this.getComplaintList();
				}
			}
		},
		// 选择投诉类型
		selectType(key) {
			this.cmType = key;
		},

		// 获取我的投诉列表
		getComplaintList() {
			uni.showLoading({
				title: "加载中",
			});
			getComplaintsList({ page: 1, limit: 1000 })
				.then((res) => {
					uni.hideLoading();
					const { data, code, message } = res.data;
					if (code === 200) {
						this.complaintList = data.list || [];
					} else {
						uni.$u.toast(message);
					}
				})
				.catch((error) => {
					uni.hideLoading();
					uni.$u.toast("请求失败，请稍后再试");
					console.error("获取我的投诉列表失败", error);
				});
		},

		// 上传图片
		uploadImage(filePath) {
			return new Promise((resolve, reject) => {
				// 这里替换为您的实际上传接口
				uni.uploadFile({
					url: BASE_URL + "/api/front/upload/image",
					filePath: filePath,
					name: "multipart",
					header: {
						"Authori-zation": this.token,
					},
					formData: {
						model: "user",
						pid: 126,
					},
					success: (uploadRes) => {
						try {
							const { code, data, message } = JSON.parse(uploadRes.data);
							if (code === 200) {
								// 上传成功，返回图片URL
								resolve(data.url);
							} else {
								// 业务逻辑错误
								reject(message || "上传失败");
							}
						} catch (error) {
							// JSON解析错误
							reject("上传响应格式错误");
						}
					},
					fail: (error) => {
						// 上传失败
						reject(error.errMsg || "上传失败");
					},
				});
			});
		},

		// 文件选择事件处理
		onFileSelect(e) {
			// 存储选择的文件信息
			this.selectImg = [...this.selectImg, ...e.tempFilePaths];
		},

		// 文件删除事件处理
		onFileDelete(e) {
			this.selectImg = this.selectImg.filter((item) => item !== e.tempFilePath);
		},

		// 提交表单
		async submit() {
			// 表单验证
			if (!this.cmContent) {
				uni.$u.toast("请输入您的投诉与建议");
				return;
			}
			if (!this.cmType) {
				uni.$u.toast("请选择投诉类型");
				return;
			}

			try {
				// 显示整体处理中的提示
				uni.showLoading({
					title: "处理中...",
					mask: true,
				});

				// 上传图片
				if (this.selectImg && this.selectImg.length > 0) {
					// 更新loading提示为上传中
					uni.showLoading({
						title: "上传图片中...",
						mask: true,
					});

					// 直接遍历上传所有图片
					const uploadPromises = this.selectImg.map((filePath) => this.uploadImage(filePath));
					this.uploadedImages = await Promise.all(uploadPromises);
				}

				// 更新loading提示为提交中
				uni.showLoading({
					title: "提交数据中...",
					mask: true,
				});

				// 提交表单数据
				await this.submitFormData();

				// 提交成功
				uni.showToast({
					title: "提交成功",
					icon: "success",
          duration: 2000,
				});

				// 重置表单
				this.resetForm();

        // 添加延迟，让用户看到成功提示后再跳转
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/me/index",
          });
        }, 2000);
			} catch (error) {
				console.error("操作失败:", error);
				uni.showToast({
					title: typeof error === "string" ? error : "操作失败，请重试",
					icon: "none",
				});
			} finally {
				// 确保隐藏loading
				uni.hideLoading({
          noConflict: true
        });
			}
		},

		// 提交表单数据
		submitFormData() {
			// 构建提交的数据
			const formData = {
				cmContent: this.cmContent,
				cmType: this.cmType,
				cmImg: this.uploadedImages?.join(","),
			};

			console.log("提交表单数据", formData);

			// 返回Promise以便在submit方法中使用await
			return new Promise((resolve, reject) => {
				// 调用API提交数据
				postComplaintsSave(formData)
					.then((res) => {
						const { code, message } = res.data;
						if (code === 200) {
							// 提交成功
							resolve();
						} else {
							// 业务逻辑错误
							reject(message || "提交失败");
						}
					})
					.catch((error) => {
						// 网络错误或其他异常
						console.error("提交表单失败", error);
						reject("提交失败，请检查网络后重试");
					});
			});
		},

		// 重置表单
		resetForm() {
			this.cmContent = "";
			this.cmType = "";
			this.selectImg = [];
			this.uploadedImages = [];

			// 重置文件选择器
			if (this.$refs.files) {
				this.$refs.files.clearFiles();
			}
		},

		// 预览图片
		previewImage(urls, current) {
			const fullUrls = urls.map((url) => BASE_IMG_URL + url);
			uni.previewImage({
				urls: fullUrls,
				current: current,
				indicator: "default",
				loop: true,
			});
		},
	},
};
</script>

<style scoped>
.complaints-container {
	width: 100%;
	height: 100%;
	position: relative;
}

.bg-header {
	width: 100%;
	height: 500rpx;
}

.complaints-body {
	position: absolute;
	top: 316rpx;
	left: 0;
	padding: 0 30rpx;
	width: 100%;
}

.complaints-content {
	background: #ffffff;
	box-shadow: 0rpx 5rpx 30rpx 0rpx rgba(23, 82, 113, 0.05);
	border-radius: 20rpx;
	width: 100%;
	padding: 40rpx 35rpx;
}

.header-tab {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 42rpx;
}

.tab-item {
	position: relative;
}

.active {
	font-weight: bold;
	font-size: 32rpx;
	color: #262626;
	line-height: 32rpx;
}

.tab-img {
	position: absolute;
	bottom: -10rpx;
	left: 32rpx;
	width: 42rpx;
	height: 39rpx;
}

.current-0 {
	padding-top: 34rpx;
}

.current-1 {
	padding-top: 52rpx;
}

::v-deep .u-textarea {
	background-color: #f8f8f8 !important;
}

.type-title {
	font-weight: 400;
	font-size: 32rpx;
	color: #262626;
	line-height: 56rpx;
	padding: 30rpx 0;
}

.type-list {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 26rpx;
	text-align: center;
	margin-bottom: 30rpx;
}

.type-item {
	width: 136rpx;
	height: 60rpx;
	background: #ffffff;
	border-radius: 12rpx;
	border: 2px solid #d5d4d9;
	font-weight: 400;
	font-size: 28rpx;
	color: #8f8f9b;
	line-height: 56rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.type-active {
	width: 136rpx;
	height: 60rpx;
	background: #eefcff;
	border-radius: 12rpx;
	border: 2px solid #2da5c3;
	font-weight: 400;
	font-size: 28rpx;
	color: #2da6c4;
	line-height: 56rpx;
}

.submit-btn {
	width: 333rpx;
	height: 80rpx;
	margin: 0 auto;
	margin-top: 60rpx;
	background: #2da6c4;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

/* 我的投诉 */
.complaint-my {
	margin-top: 50rpx;
}

.complaint-item {
	background: #f3fafa;
	border-radius: 15rpx;
	position: relative;
	padding: 30rpx 25rpx;
	margin-bottom: 30rpx;
}

.tag-top {
	position: absolute;
	top: 0;
	left: 0;
	width: 99rpx;
	height: 46rpx;
	background: #f89235;
	border-radius: 15rpx 0rpx 15rpx 0rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 24rpx;
	color: #ffffff;
	line-height: 1;
}
.item-top {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 400;
	font-size: 26rpx;
	color: #adadad;
	line-height: 1;
	margin-top: 36rpx;
	margin-bottom: 21rpx;
}
.item-top-content {
	font-weight: 400;
	font-size: 28rpx;
	color: #484848;
	line-height: 44rpx;
}
.item-img-box {
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.item-img-item {
	width: 170rpx;
	height: 170rpx;
	background: #def0f0;
	border-radius: 15rpx;
}
.item-img-item image {
	width: 100%;
	height: 100%;
}

.item-reply-header {
	margin-top: 40rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 400;
	font-size: 24rpx;
	color: #2da6c4;
	line-height: 1;
}
.reply-title {
	width: 117rpx;
	height: 50rpx;
	background: #2da6c4;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 26rpx;
	color: #ffffff;
	line-height: 1;
}
.item-reply-content {
	font-weight: 400;
	font-size: 28rpx;
	color: #484848;
	line-height: 44rpx;
}
.empty-state {
	height: 50vh;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #999;
}
</style>
