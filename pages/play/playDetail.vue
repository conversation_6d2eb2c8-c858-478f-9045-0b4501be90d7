<template>
	<view class="info-detail-page">
		<view class="info-detail">
			<view class="info-detail-title">{{ infoDetail.guideTitle || "" }}</view>
			<view class="info-detail-box">
				<view class="info-detail-author">{{ infoDetail.guideAuthor || "" }}</view>
				<view class="info-detail-time">{{ infoDetail.guidePushTime || "" }}</view>
				<view class="info-detail-source">{{ infoDetail.guideDataSource || "" }}</view>
			</view>
			<view class="info-detail-content">
				<u-parse :content="infoDetail.guideContent || ''"></u-parse>
			</view>
		</view>
	</view>
</template>

<script>
import { getBusinessInfo } from "@/nxTemp/apis/common.js";
export default {
	data() {
		return {
			id: "",
			title: "",
			infoDetail: {
				guideTitle: "", // 标题
				guideAuthor: "", // 作者
				guidePushTime: "", // 时间
				guideContent: "", // 内容
				guideDataSource: "", // 来源
			}, //  攻略详情
		};
	},
	onLoad(options) {
		this.id = options?.id;
		if (options?.playKey === "0") {
			this.title = "岛上攻略";
		} else if (options?.playKey === "1") {
			this.title = "周边攻略";
		} else if (options?.playKey === "2") {
			this.title = "民风民俗";
		}
		uni.setNavigationBarTitle({
			title: this.title,
		});
	},
	watch: {
		id: {
			handler(newVal) {
				this.getInfoDetail(newVal);
			},
		},
	},
	methods: {
		// 获取详情
		getInfoDetail() {
			uni.showLoading({
				title: "加载中...",
			});
			getBusinessInfo({
				id: this.id,
			})
				.then((res) => {
					const { data, code, message } = res.data;
					if (code === 200) {
						this.infoDetail = {
							...data,
						};
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},
	},
};
</script>

<style lang="scss" scoped>
.info-detail-page {
	width: 100%;
	min-height: 100vh;
	padding: 0 30rpx;
	padding-top: 58rpx;
	padding-bottom: 30rpx;
  background-color: #E9F4F7;
}

.info-detail {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 45rpx 30rpx;
  margin-bottom: 30rpx;
}

.info-detail-title {
	font-weight: 500;
	font-size: 42rpx;
	color: #262626;
	line-height: 56rpx;
}

.info-detail-box {
	display: flex;
	align-items: center;
	padding-top: 35rpx;
	padding-bottom: 41rpx;
}

.info-detail-author {
	font-weight: 400;
	font-size: 28rpx;
	color: #2da6c4;
	line-height: 56rpx;
}

.info-detail-time {
	font-weight: 400;
	font-size: 28rpx;
	color: #adadad;
	line-height: 56rpx;
	padding-left: 30rpx;
	padding-right: 40rpx;
}

.info-detail-source {
	font-weight: 400;
	font-size: 28rpx;
	color: #adadad;
	line-height: 56rpx;
}
</style>
