<template>
	<view class="play-page">
		<scroll-view
			scroll-y
			class="main-scroll-view"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="loadMore"
			@scroll="onScroll"
			:scroll-top="scrollTop"
			:lower-threshold="100">
			<view class="header">
				<image :src="allImgList.playJS_bg" mode="scaleToFill"></image>
			</view>

			<view class="header-tab">
				<view
					class="header-tab-item"
					:class="{ active: activeTab === 0, inactive: activeTab !== 0 }"
					@click="changeMainTab(0)">
					<image
						:src="
							activeTab === 0
								? '/static/images/playIcons/L-selected.png'
								: '/static/images/playIcons/L.png'
						"></image>
					<view class="text-container">
						<text>岛上攻略</text>
						<image
							v-if="activeTab === 0"
							class="bg-img"
							src="/static/images/icons/bg-img.png"></image>
					</view>
				</view>
				<view
					class="header-tab-item"
					:class="{ active: activeTab === 1, inactive: activeTab !== 1 }"
					@click="changeMainTab(1)">
					<image
						:src="
							activeTab === 1
								? '/static/images/playIcons/M-selected.png'
								: '/static/images/playIcons/M.png'
						"></image>
					<view class="text-container">
						<text>周边攻略</text>
						<image
							v-if="activeTab === 1"
							class="bg-img"
							src="/static/images/icons/bg-img.png"></image>
					</view>
				</view>
				<view
					class="header-tab-item"
					:class="{ active: activeTab === 2, inactive: activeTab !== 2 }"
					@click="changeMainTab(2)">
					<image
						:src="
							activeTab === 2
								? '/static/images/playIcons/R-selected.png'
								: '/static/images/playIcons/R.png'
						"></image>
					<view class="text-container">
						<text>民风民俗</text>
						<image
							v-if="activeTab === 2"
							class="bg-img"
							src="/static/images/icons/bg-img.png"></image>
					</view>
				</view>
			</view>

			<view class="content">
				<view class="island-tab" v-if="activeTab === 0">
					<view
						v-for="(tab, index) in islandTabs"
						:key="index"
						:class="{ active: activeTabKey === tab.key }"
						@tap="changeIslandTab(tab.key)">
						{{ tab.title }}
					</view>
				</view>
				<view class="island-tab" v-if="activeTab === 1">
					<view
						v-for="(tab, index) in aroundTabs"
						:key="index"
						:class="{ active: activeAroundTabKey === tab.key }"
						@tap="changeAroundTab(tab.key)">
						{{ tab.title }}
					</view>
				</view>

				<!-- 数据列表 -->
				<view class="play-items-container">
					<view
						class="play-item"
						v-for="(item, index) in playData"
						:key="index"
						@tap="goDetail(item.id)">
						<image
							class="play-image"
							:src="BASE_IMG_URL + item.guideCover"
							mode="aspectFill"></image>
						<view class="play-title">{{ item.guideTitle }}</view>
					</view>

					<!-- 加载更多 -->
					<u-loadmore
						:status="loadStatus"
						:loading-text="loadingText"
						:loadmore-text="loadmoreText"
						:nomore-text="nomoreText"
						height="80rpx" />
				</view>
			</view>
		</scroll-view>
		<view class="back-to-top" v-if="showBackToTop" @click="gotoTop">
			<image src="/static/images/icons/totop.png" mode="scaleToFill"></image>
		</view>
	</view>
</template>

<script>
import { getBusinessList } from "@/nxTemp/apis/common.js";
import { BASE_IMG_URL } from "@/env.js";
import { mapState } from "vuex";
export default {
	data() {
		return {
			// 头部tab 默认选中第一项
			activeTab: 0,

			// 岛上攻略tab
			islandTabs: [
				{
					key: "eat",
					title: "吃在金梭",
				},
				{
					key: "live",
					title: "住在金梭",
				},
				{
					key: "play",
					title: "玩在金梭",
				},
			],

			// 周边攻略tab
			aroundTabs: [
				{
					key: "haidong",
					title: "玩转海东",
				},
				{
					key: "dali",
					title: "玩转大理",
				},
			],

			// 岛上攻略tab选中
			activeTabKey: "eat", // 默认选中吃在金梭
			// 周边攻略tab选中
			activeAroundTabKey: "haidong", // 默认选中玩转海东

			// 请求参数
			pageNum: 1,
			pageSize: 10,
			isLoading: false,
			hasMore: true,
			isRefreshing: false,
			loadStatus: "loadmore", // loadmore-加载前的状态，loading-加载中的状态，nomore-没有更多的状态
			loadingText: "正在加载更多...",
			loadmoreText: "上拉加载更多",
			nomoreText: "没有更多数据了",

			// 数据列表
			playData: [], // 数据数组

			// 返回顶部
			showBackToTop: false,
			scrollTop: 0,
			old: {
				scrollTop: 0,
			},
		};
	},
	computed: {
		...mapState(["allImgList"]),
		BASE_IMG_URL() {
			return BASE_IMG_URL;
		},
	},
	onShow() {
		// 从本地存储获取tab参数
		const params = uni.getStorageSync("tabParams");

		// 使用完参数后，可以清除它们
		uni.removeStorageSync("tabParams");
		if (params) {
			this.activeTab = Number(params.playKey) || 0;
			this.activeTabKey = params.islandTabKey || "eat";
			this.activeAroundTabKey = params.aroundTabKey || "haidong";
		}
		this.getList(true);
	},
	onLoad(options) {
		// 页面加载时获取数据
	},
	methods: {
		onScroll(e) {
			this.showBackToTop = e.detail.scrollTop > 500;
			this.old.scrollTop = e.detail.scrollTop;
		},
		gotoTop() {
			this.scrollTop = this.old.scrollTop;
			this.$nextTick(function () {
				this.scrollTop = 0;
			});
		},
		// 切换主标签
		changeMainTab(index) {
			if (this.activeTab === index) return;
			this.activeTab = index;
			this.pageNum = 1;
			this.playData = [];
			this.getList(true);
		},

		// 切换岛上攻略标签
		changeIslandTab(key) {
			if (this.activeTabKey === key) return;
			this.activeTabKey = key;
			this.pageNum = 1;
			this.playData = [];
			this.getList(true);
		},

		// 切换周边攻略标签
		changeAroundTab(key) {
			if (this.activeAroundTabKey === key) return;
			this.activeAroundTabKey = key;
			this.pageNum = 1;
			this.playData = [];
			this.getList(true);
		},

		// 跳转详情页
		goDetail(id) {
			// 判断岛上攻略-吃在金梭 住在金梭 跳转至商家详情页
			if (this.activeTab == 0 && (this.activeTabKey === "eat" || this.activeTabKey === "live")) {
				uni.$u.route({
					url: "pages/merchant/merchantDetail",
					params: {
						id: id,
						iswx: true,
					},
				});
			} else {
				uni.$u.route({
					url: "pages/play/playDetail",
					params: {
						id: id,
						playKey: this.activeTab,
					},
				});
			}
		},

		// 获取列表数据
		async getList(isRefresh = false) {
			if (this.isLoading) return;

			try {
				this.isLoading = true;
				this.loadStatus = "loading";

				if (isRefresh) {
					this.pageNum = 1;
				}

				// 请求参数
				const params = {
					guideType:
						this.activeTab === 0
							? this.activeTabKey
							: this.activeTab === 1
							? this.activeAroundTabKey
							: "customs",
					page: this.pageNum,
					limit: this.pageSize,
				};
				getBusinessList(params).then((res) => {
					const { data, code, message } = res.data;
					if (code === 200) {
						// 更新数据
						if (this.pageNum === 1) {
							this.playData = [...data.list];
						} else {
							this.playData = [...this.playData, ...data.list];
						}
						// 判断是否还有更多数据
						if (data.totalPage <= data.page) {
							this.loadStatus = "nomore";
						} else {
							this.loadStatus = "loadmore";
							this.pageNum++;
						}
					} else {
						uni.$u.toast(message);
					}
				});
			} catch (error) {
				uni.showToast({
					title: "获取数据失败",
					icon: "none",
				});
				this.loadStatus = "loadmore";
			} finally {
				this.isLoading = false;
				if (isRefresh) {
					this.isRefreshing = false;
				}
			}
		},

		// 加载更多
		loadMore() {
			if (this.loadStatus === "loadmore" && !this.isLoading) {
				this.getList();
			}
		},

		// 刷新数据
		refreshData() {
			this.pageNum = 1;
			this.hasMore = true;
			this.getList(true);
		},

		// 下拉刷新
		async onRefresh() {
			this.isRefreshing = true;
			await this.getList(true);
		},
	},
};
</script>

<style lang="scss" scoped>
.play-page {
	background-color: #fff;
	width: 100%;
	height: 100vh;
	position: relative;
}

.main-scroll-view {
	width: 100%;
	height: 100vh;
}

.header {
	position: relative;
	width: 100%;
	height: 450rpx;
	image {
		width: 100%;
		height: 100%;
	}
}
.header-tab {
	position: relative;
	margin-top: -120rpx;
	width: 100%;
	height: 140rpx;
	display: flex;
	background: #f4f7fa;
	border-radius: 37rpx 37rpx 0rpx 0rpx;
	align-items: center;
	.header-tab-item {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s;

		.text-container {
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;

			text {
				font-size: 30rpx;
				color: #8d9196;
				z-index: 2;
			}

			.bg-img {
				position: absolute;
				width: 42rpx;
				height: 39rpx;
				left: 30rpx;
				bottom: -18rpx;
				z-index: 1;
			}
		}

		image {
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 8rpx;
		}
	}
	.inactive {
		width: 30.4%;
	}
	.active {
		.text-container {
			text {
				font-size: 30rpx;
				color: #262626;
				font-weight: 500;
			}
		}
	}
	.active:first-child {
		background: url("/static/images/playIcons/play-left.png") no-repeat center center;
		background-size: 100% 100%;
		width: 39.2% !important;
		padding-right: 40rpx;
	}
	.active:nth-child(2) {
		background: url("/static/images/playIcons/play-mid.png") no-repeat center center;
		background-size: 100% 100%;
		width: 39.2% !important;
	}
	.active:last-child {
		background: url("/static/images/playIcons/play-right.png") no-repeat center center;
		background-size: 100% 100%;
		width: 39.2% !important;
	}
}
.content {
	width: 100%;
	min-height: calc(100vh - 450rpx - 140rpx + 120rpx);
}

.island-tab {
	width: 100%;
	padding: 0 30rpx;
	padding-top: 35rpx;
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.island-tab view {
	height: 48rpx;
	width: 130rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 24rpx;
	color: #2da6c4;
	border: 1px solid #2da6c4;
	border-radius: 8rpx;
	margin-right: 16rpx;
	position: relative;
	z-index: 2;
	transition: all 0.2s;
}

.island-tab view:last-child {
	margin-right: 0;
}

.island-tab view.active {
	background-color: #2da6c4;
	color: #fff;
}

.play-items-container {
	padding: 30rpx;
	box-sizing: border-box;
}

/* 列表样式 */
.play-item {
	margin-bottom: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.play-image {
	width: 100%;
	height: 350rpx;
	border-radius: 20rpx;
}

.play-title {
	padding: 35rpx;
	font-weight: 500;
	font-size: 36rpx;
	color: #484848;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.loading-more {
	text-align: center;
	padding: 30rpx 0;
	font-size: 26rpx;
	color: #999;
}

.empty-data {
	text-align: center;
	padding: 100rpx 0;
	font-size: 28rpx;
	color: #999;
}

.back-to-top {
	position: fixed;
	bottom: 100rpx;
	right: 30rpx;
	background-color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	image {
		width: 100%;
		height: 100%;
	}
}
</style>
