<template>
	<view class="index-page">
		<!-- 搜索栏 -->
		<!-- <view class="search-box" :style="{ top: searchBarTop + 'px', height: '70rpx' }">
			<image src="@/static/images/icons/search.png" mode="aspectFit" class="search-icon"></image>
			<input class="search-input" placeholder="请输入相关搜索文字" />
		</view> -->
		<!-- 背景图 -->
		<view class="top-image">
			<image :src="allImgList.homeBack || ''" mode="scaleToFill" class="image-bg"></image>
		</view>

		<!-- 功能栏 -->
		<view class="container-bar">
			<view class="top-bar">
				<view class="bar-item animation-slide-bottom" v-for="(item, index) in topFunctions" :key="item.name"
					:style="{ animationDelay: 0.1 + index * 0.15 + 's' }" @click="navigateTo(item)">
					<image :src="item.icon" mode="scaleToFill"></image>
					<text>{{ item.name }}</text>
				</view>
			</view>
			<view class="bottom-bar">
				<view class="bar-item animation-slide-bottom" v-for="(item, index) in bottomFunctions" :key="item.name"
					:style="{ animationDelay: 0.6 + index * 0.15 + 's' }" @click="navigateTo(item)">
					<image :src="item.icon" mode="scaleToFill"></image>
					<text>{{ item.name }}</text>
				</view>
			</view>
		</view>

		<!-- 公告 -->
		<view class="notice animation-slide-right" @click="navigateTo({}, '/pages/news/notice')" :style="{
				animationDelay: 0.5 + 's',
				backgroundImage: `url(${allImgList.homeNotice || ''})`,
			}">
			<view class="notice-text">
				<uni-notice-bar background-color="transparent" color="#333" fontSize='12'
					:scrollable="noticeText.length > 16" single :text="noticeText">
				</uni-notice-bar>
			</view>
		</view>

		<!-- 轮播 -->
		<view class="swiper-box animation-slide-bottom" :style="{ animationDelay: 0.9 + 's' }">
			<scroll-view class="scroll-view" scroll-x="true" show-scrollbar="false" :scroll-left="scrollLeft"
				:scroll-with-animation="true" @scroll="onScroll" id="scrollView">
				<view class="scroll-item" v-for="(item, index) in swiperList" :key="index" :id="'item-' + index"
					@tap="navigateTo(item)">
					<image :src="item.icon"></image>
				</view>
			</scroll-view>
		</view>

		<!-- 云游金梭 -->
		<view class="wander-jinsuo">
			<view class="wander-header" @tap="navigateTo({}, '/pages/vr/index', true)"
				:style="{ backgroundImage: `url(${allImgList.homeVR_bg || ''})` }">
				<view class="wander-text-box">
					<text>云游金梭</text>
					<image src="@/static/images/icons/rightarrow.png"> </image>
				</view>
			</view>
			<image @tap="navigateTo({}, '/pages/vr/index', true)" class="wander-image" :src="allImgList.homeVR || ''">
			</image>
		</view>

		<!-- 金梭资讯 -->
		<view class="news-jinsuo">
			<view class="jinsuo-header" @tap="navigateTo({}, '/pages/news/list')">
				<image :src="allImgList.homeNews_bg || ''" mode="scaleToFill"></image>
				<text class="jinsuo-text">金梭资讯</text>
				<image class="jinsuo-text-image" src="@/static/images/icons/rightarrow.png" mode="scaleToFill"></image>
			</view>
			<view class="news-container">
				<view class="new-box">
					<view class="news-list">
						<view class="news-item" v-for="(item, index) in newsList" :key="item.id"
							@tap="goDetail(item.id)">
							<image :src="BASE_IMG_URL + item.informCover" mode="aspectFill" class="news-image"></image>
							<text class="news-title">{{ item.informTitle }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 岛上攻略 -->
		<view class="island-guide">
			<view class="jinsuo-header"
				@tap="navigateTo({}, `/pages/play/index?playKey=0&islandTabKey=${activeTabKey}`, true)">
				<image :src="allImgList.homeIsland_bg || ''" mode="scaleToFill"></image>
				<text class="jinsuo-text">岛上攻略</text>
				<image class="jinsuo-text-image" src="@/static/images/icons/rightarrow.png" mode="scaleToFill"></image>
			</view>
			<view class="island-container">
				<view class="island-tab">
					<view v-for="(tab, index) in islandTabs" :key="index" :class="{ active: activeTabKey === tab.key }"
						@tap="changeTab(tab.key)">
						{{ tab.title }}
					</view>
				</view>
				<view class="island-content">
					<view class="tab-content">
						<!-- 左侧大图 -->
						<view class="tab-item" @tap="goDetail(islandData[0].id, activeTabKey)" v-if="islandData[0]">
							<image :src="BASE_IMG_URL + islandData[0].guideCover" mode="aspectFill" class="tab-image">
							</image>
							<view class="tab-title-overlay">
								<text class="tab-title">{{ islandData[0].guideTitle }}</text>
							</view>
						</view>
						<!-- 右侧两个小图 -->
						<view class="right-items">
							<view class="tab-item-small" @tap="goDetail(islandData[1].id, activeTabKey)"
								v-if="islandData[1]">
								<image :src="BASE_IMG_URL + islandData[1].guideCover" mode="aspectFill"
									class="tab-image"></image>
								<view class="tab-title-overlay">
									<text class="tab-title">{{ islandData[1].guideTitle }}</text>
								</view>
							</view>
							<view class="tab-item-small" @tap="goDetail(islandData[2].id, activeTabKey)"
								v-if="islandData[2]">
								<image :src="BASE_IMG_URL + islandData[2].guideCover" mode="aspectFill"
									class="tab-image"></image>
								<view class="tab-title-overlay">
									<text class="tab-title">{{ islandData[2].guideTitle }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 周边攻略 -->
		<view class="around-guide">
			<view class="jinsuo-header" @tap="
					navigateTo({}, `/pages/play/index?playKey=1&aroundTabKey=${activeAroundTabKey}`, true)
				">
				<image :src="allImgList.homeAround_bg || ''" mode="scaleToFill"></image>
				<text class="jinsuo-text">周边攻略</text>
				<image class="jinsuo-text-image" src="@/static/images/icons/rightarrow.png" mode="scaleToFill"></image>
			</view>
			<view class="island-container">
				<view class="island-tab">
					<view v-for="(tab, index) in aroundTabs" :key="index"
						:class="{ active: activeAroundTabKey === tab.key }" @tap="changeAroundTab(tab.key)">
						{{ tab.title }}
					</view>
				</view>
				<view class="around-content" @tap="goDetail(aroundData.id, 1)">
					<image v-if="aroundData.guideCover" :src="BASE_IMG_URL + aroundData.guideCover" mode="aspectFill"
						class="tab-image"></image>
					<view class="tab-title-overlay">
						<text class="around-img-title">{{ aroundData.guideTitle }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 安全提示模态框 -->
		<u-popup :show="showSafetyModal" mode="center" bgColor="transparent" :safeAreaInsetBottom="false">
			<view class="safety-tips-modal">
				<view class="safety-tips-modal-header">
					<image class="safety-tips-bg" src="@/static/images/icons/tipsbg.png" mode="scaleToFill"></image>
					<image class="safety-tips-icon" src="@/static/images/icons/warning.png" mode="scaleToFill"></image>
					<view class="safety-tips-header">
						<text class="safety-tips-title">{{ safetyTipsContent.ewTitle }}</text>
						<text class="safety-tips-time">{{ safetyTipsContent.updateTime }}</text>
					</view>
				</view>

				<view class="safety-tips-content">
					<view class="safety-tips-content-text">
						<u-parse :content="safetyTipsContent.ewContent"></u-parse>
					</view>
					<image @click="showSafetyModal = false" class="safety-tips-content-btn"
						src="@/static/images/icons/btn.png" mode="scaleToFill"></image>
				</view>

				<view class="safety-tips-cancel-btn" @click="showSafetyModal = false">
					<image class="safety-tips-cancel-btn-image" src="@/static/images/icons/cancel.png"
						mode="scaleToFill">
					</image>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		getIndexInfo,
		getBusinessList,
		getNewsList,
		getAllImgList
	} from "@/nxTemp/apis/common";
	import {
		BASE_IMG_URL
	} from "@/env.js";
	import {
		mapActions,
		mapState
	} from "vuex";
	import {
		permission
	} from "@/nxTemp/utils";
	export default {
		data() {
			return {
				showSafetyModal: false, // 控制安全提示模态框显示
				safetyTipsContent: {}, // 安全提示内容
				searchBarTop: 0, //搜索栏的外边框高度，单位px
				searchBarHeight: 0, //搜索栏的高度，单位px
				topFunctions: [], // 第一行功能列表
				bottomFunctions: [], // 第二行功能列表
				swiperList: [], //轮播的图片
				noticeText: "", // 公告内容
				scrollLeft: 0, // 当前滚动位置
				currentItemIndex: 0, // 当前显示的项目索引
				scrollTimer: null, // 自动滚动定时器
				itemWidths: [], // 每个item的宽度和位置信息
				islandTabs: [{
						key: "eat",
						title: "吃在金梭",
					},
					{
						key: "live",
						title: "住在金梭",
					},
					{
						key: "play",
						title: "玩在金梭",
					},
				],
				aroundTabs: [{
						key: "haidong",
						title: "玩转海东",
					},
					{
						key: "dali",
						title: "玩转大理",
					},
				],
				// 岛上攻略tab选中
				activeTabKey: "eat", // 默认选中第一项
				// 周边攻略tab选中
				activeAroundTabKey: "haidong", // 默认选中第一项
				newsList: [], //金梭资讯列表
				islandData: [], // 岛上攻略数据数组
				aroundData: {}, // 周边攻略数据 只需要显示一项
				BASE_IMG_URL: BASE_IMG_URL,
			};
		},

		onLoad(parms) {
			// 获取所有的图片数据
			this.getAllImgListFun();
			// 获取首页信息
			this.getIndexInfoFun();
			// 初始化岛上攻略数据
			this.getIslandData("eat");
			// 初始化周边攻略数据
			this.getAroundData("haidong");
			// 初始化金梭资讯数据
			this.getNewsData();
		},
		mounted() {
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top - 3;
			this.searchBarHeight = menuButtonInfo.height + 6;

			// 初始化自动滚动
			this.initAutoScroll();
		},
		computed: {
			...mapState(["allImgList"]),
		},
		watch: {
			allImgList: {
				handler(newVal) {
					this.getListData();
				},
			},
		},
		onUnload() {
			// 清除自动滚动定时器
			if (this.scrollTimer) {
				clearInterval(this.scrollTimer);
				this.scrollTimer = null;
			}
			this.showSafetyModal = false;
			this.safetyTipsContent = {};
		},
		methods: {
			...mapActions(["setAllImgList"]),
			// 获取所有的图片数据
			getAllImgListFun() {
				console.log(permission.hasPermission("wx:reply"),'999999');
				getAllImgList().then((res) => {
					console.log(res,'999999');
					const {
						data,
						code,
						message
					} = res.data;
					if (code == 200) {
						if (data && Object.keys(data).length > 0) {
							const newData = {};
							// 遍历原始数据，为每个值添加 BASE_IMG_URL 前缀
							Object.keys(data).forEach((key) => {
								// 确保值不为空且是字符串
								if (data[key] && typeof data[key] === "string") {
									// 处理可能包含逗号分隔的多个值的情况，只取第一个值
									let value = data[key];
									if (value.includes(",")) {
										value = value.split(",")[0];
									}
									// 如果路径已经包含http，则不再添加
									if (value.startsWith("http")) {
										newData[key] = value;
									} else {
										newData[key] = `${BASE_IMG_URL}${value}`;
									}
								} else {
									newData[key] = data[key];
								}
							});
							// 使用处理后的数据
							this.setAllImgList(newData);
						}
					} else {
						uni.$u.toast(message);
					}
				});
			},
			// 获取首页信息
			getIndexInfoFun() {
				let that = this;
				getIndexInfo()
					.then((res) => {
						const {
							data,
							code,
							message
						} = res.data;
						if (code == 200) {
							this.noticeText = data?.notice?.noticeTitle || "";
							// 检查是否有安全提示
							if (data.warning) {
								this.safetyTipsContent = data.warning || {};
								this.showSafetyModal = true;
								setTimeout(() => {
									that.showSafetyModal = false;
								}, 120000); // 120秒后关闭安全提示模态框
							}
						} else {
							uni.$u.toast(message);
						}
					})
					.catch((err) => {});
			},

			// 获取列表数据
			getListData() {
				// 第一行功能列表
				this.topFunctions = [{
						name: "演出时间",
						icon: this.allImgList.homeShowtime || "",
						url: "/pages/public/imagPage",
						imagType: "performanceTime",
					},
					{
						name: "船次时间",
						icon: this.allImgList.homeShiptime || "",
						url: "/pages/public/imagPage",
						imagType: "shipTime",
					},
					{
						name: "岛上攻略",
						icon: this.allImgList.homeIslandstrategy || "",
						url: "/pages/play/index?playKey=0",
						istab: true,
					},
					{
						name: "周边攻略",
						icon: this.allImgList.homeSurroundingstrategy || "",
						url: "/pages/play/index?playKey=1",
						istab: true,
					},
				];
				// 第二行功能列表
				this.bottomFunctions = [{
						name: "游览路线",
						icon: this.allImgList.homeTouristRoute || "",
						url: "/pages/vr/touristRoute",
					},
					{
						name: "酒店民宿",
						icon: this.allImgList.homeHotelHomestay || "",
						url: "/pages/shopping/hotel/index",
					},
					{
						name: "民风民俗",
						icon: this.allImgList.homeLocalCustoms || "",
						url: "/pages/play/index?playKey=2",
						istab: true,
					},
					{
						name: "应急救援",
						icon: this.allImgList.homeEmergencyrescue || "",
						url: "/pages/emergency/emergency",
					},
				];
				//轮播的图片
				this.swiperList = [{
						name: "投诉建议",
						icon: this.allImgList.homeComplaint || "",
						// 如果用户有投诉建议回复权限，则跳转到投诉建议回复页，否则跳转到投诉建议页
						url: "/pages/complaints/complaints",
					},
					{
						name: "用户调查",
						icon: this.allImgList.homeSurvey || "",
						url: "/pages/me/questionnaire",
					},
					// {
					// 	name: "线上购票",
					// 	icon: this.allImgList.homeOnlineticket || "",
					// 	url: "/pages/island/guide",
					// },
					{
						name: "集章规则",
						icon: this.allImgList.homeCollectionrules || "",
						url: "/pages/public/imagPage",
            imagType: "coinRules",
					},
				];
			},
			// 获取金梭资讯数据
			getNewsData(key) {
				getNewsList({
					page: 1,
					limit: 2,
					informIndexShow: 1,
				}).then((res) => {
					const {
						data,
						code,
						message
					} = res.data;
					if (code === 200) {
						this.newsList = [...data.list];
					} else {
						uni.$u.toast(message);
					}
				});
			},
			// 获取岛上攻略数据
			getIslandData(key) {
				// islandData
				getBusinessList({
					guideType: key,
					page: 1,
					limit: 3,
					guideIndexShow: 1,
				}).then((res) => {
					const {
						data,
						code,
						message
					} = res.data;
					if (code === 200) {
						this.islandData = [...data.list];
					} else {
						uni.$u.toast(message);
					}
				});
			},
			// 获取周边攻略数据
			getAroundData(key) {
				// aroundData
				getBusinessList({
					guideType: key,
					page: 1,
					limit: 1,
					guideIndexShow: 1,
				}).then((res) => {
					const {
						data,
						code,
						message
					} = res.data;
					if (code === 200) {
						this.aroundData = data?.list && data?.list?.length > 0 ? data?.list[0] : {};
					} else {
						uni.$u.toast(message);
					}
				});
			},
			// 初始化自动滚动
			initAutoScroll() {
				// 延迟获取DOM尺寸，确保页面已渲染
				setTimeout(() => {
					this.calculateItemPositions();

					// 设置定时器，滚动到下一个
					this.scrollTimer = setInterval(() => {
						this.scrollToNext();
					}, 4000);
				}, 1000);
			},

			// 计算每个item的位置信息
			calculateItemPositions() {
				const query = uni.createSelectorQuery().in(this);
				const windowWidth = uni.getWindowInfo().windowWidth;
				// rpx与px的转换比例
				const rpxToPxRatio = windowWidth / 750;
				// 将40rpx转换为px，用于计算边距
				const marginInPx = 40 * rpxToPxRatio;

				this.itemWidths = [];
				let totalWidth = 0;

				// 查询每个item的位置和尺寸
				for (let i = 0; i < this.swiperList.length; i++) {
					query
						.select("#item-" + i)
						.boundingClientRect((rect) => {
							if (rect) {
								// 记录每个item的起始位置和宽度
								this.itemWidths.push({
									start: totalWidth,
									width: rect.width,
								});
								totalWidth += rect.width + marginInPx; // 加上边距
							}
						})
						.exec();
				}
			},

			// 滚动到下一个item
			scrollToNext() {
				if (this.itemWidths.length === 0) return;

				this.currentItemIndex = (this.currentItemIndex + 1) % this.swiperList.length;
				this.scrollLeft = this.itemWidths[this.currentItemIndex].start;
			},

			// 监听滚动事件
			onScroll(e) {
				// 用户手动滚动时，重置自动滚动计时器
				if (this.scrollTimer) {
					clearInterval(this.scrollTimer);

					// 检测当前实际滚动到了哪个item附近
					const currentScrollLeft = e.detail.scrollLeft;
					this.updateCurrentIndex(currentScrollLeft);

					// 重新设置定时器，从当前位置继续自动滚动
					this.scrollTimer = setInterval(() => {
						this.scrollToNext();
					}, 3000);
				}
			},

			// 根据滚动位置更新当前索引
			updateCurrentIndex(scrollLeft) {
				// 找到最接近当前scrollLeft的项目
				let closestIndex = 0;
				let minDistance = Number.MAX_VALUE;

				for (let i = 0; i < this.itemWidths.length; i++) {
					const distance = Math.abs(this.itemWidths[i].start - scrollLeft);
					if (distance < minDistance) {
						minDistance = distance;
						closestIndex = i;
					}
				}
				this.currentItemIndex = closestIndex;
			},
			// 切换岛上攻略标签
			changeTab(key) {
				this.activeTabKey = key;
				this.getIslandData(key);
			},
			// 切换周边攻略标签
			changeAroundTab(key) {
				this.activeAroundTabKey = key;
				this.getAroundData(key);
			},
			// 页面跳转
			// 参数1：item，用于传递参数
			// 参数2：url，用于传递url
			// 参数3：istab，用于判断是否是tabbar页面
			navigateTo(item, url, istab) {
				const turl = item.url || url;
				if (istab || item.istab) {
					// 对于tabbar页面，先跳转到页面
					uni.switchTab({
						url: turl,
					});
					// 然后通过全局变量或本地存储传递参数
					if (turl.includes("?")) {
						// 解析URL中的参数
						const [path, query] = turl.split("?");
						const params = {};
						query.split("&").forEach((item) => {
							const [key, value] = item.split("=");
							params[key] = value;
						});
						// 存储参数到本地存储
						uni.setStorageSync("tabParams", params);
					}
					return;
				}
				if (item.imagType) {
					uni.$u.route({
						url: "pages/public/imagPage",
						params: {
							imagType: item.imagType,
              isDesc: item.imagType === "coinRules" ? '1' : '0'
						},
					});
					return;
				}
				uni.navigateTo({
					url: turl,
				});
			},

			// 跳转资讯或攻略详情页
			goDetail(id, type) {
				if (!id) return;

				// 攻略
				if (type) {
					// 吃在金梭 住在金梭
					if (type == "eat" || type == "live") {
						uni.$u.route({
							url: "pages/merchant/merchantDetail",
							params: {
								id: id,
								iswx: true,
							},
						});
					} else {
						uni.$u.route({
							url: "pages/play/playDetail",
							params: {
								id: id,
								playKey: 0,
							},
						});
					}
				} else {
					// 资讯
					uni.$u.route({
						url: "pages/news/infoDetail",
						params: {
							id: id,
						},
					});
				}
			},
			// 处理安全提示确认
			handleSafetyConfirm() {
				console.log("用户已确认安全提示");
				this.showSafetyModal = false;
			},
			// 处理安全提示关闭
			handleSafetyClose() {
				console.log("用户关闭了安全提示");
				this.showSafetyModal = false;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.index-page {
		background-color: #fff;
		padding-bottom: 39rpx;
	}

	.search-box {
		position: fixed;
		top: 0;
		left: 30rpx;
		width: 496rpx;
		background-color: #fff;
		z-index: 999;
		border-radius: 999rpx;
		display: flex;
		align-items: center;
		justify-content: start;
		padding: 0 30rpx;
	}

	.search-icon {
		width: 31rpx;
		height: 31rpx;
		margin-right: 10rpx;
	}

	.search-input {
		height: 100%;
		font-size: 26rpx;
		color: #a9c1c7;
	}

	.top-image {
		width: 100%;
		height: 607rpx;
		display: block;
		position: relative;
		top: 0;
		left: 0;
		z-index: 1;
	}

	.image-bg {
		width: 100%;
		height: 100%;
	}

	.container-bar {
		width: calc(100% - 60rpx);
		margin: 0 auto;
		height: 388rpx;
		margin-top: -116rpx;
		position: relative;
		z-index: 99;
		background-color: #fff;
		border-radius: 24rpx;
		box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.05);
		padding: 35rpx;
	}

	.top-bar {
		width: 100%;
		height: 50%;
		display: flex;
		margin-bottom: 30rpx;
	}

	.top-bar image {
		width: 116rpx;
		height: 116rpx;
	}

	.bar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.bar-item text {
		font-weight: bold;
		font-size: 26rpx;
		padding-top: 14rpx;
		color: #622b27;
		line-height: 1;
	}

	.bottom-bar {
		width: 100%;
		margin: 0 auto;
		height: 50%;
		display: flex;
	}

	.bottom-bar image {
		width: 88rpx;
		height: 88rpx;
	}

	.notice {
		margin: 37rpx 30rpx;
		height: 80rpx;
		background: url("") no-repeat center center;
		background-size: 100% 100%;
		display: flex;
		// align-items: center;
		position: relative;
	}

	.notice-text {
		position: absolute;
		left: 173rpx;
		top: 27rpx;
		position: relative;
		width: 450rpx;
		height: 23rpx;
	}

	::v-deep .uni-noticebar {
		display: flex;
		width: 100%;
		height: 100% !important;
		box-sizing: border-box;
		flex-direction: row;
		align-items: center;
		padding: 0 !important;
		margin-bottom: 0 !important;
	}

	.notice-text::before {
		content: "";
		position: absolute;
		left: 0;
		top: 2rpx;
		width: 1rpx;
		background-color: #27272621;
		height: 12px;
	}

	::v-deep .uni-noticebar__content-text {
		margin-left: 15rpx;
		font-size: 22rpx;
		line-height: 1;
	}

	.swiper-box {
		width: 100%;
		height: 160rpx;
		margin-bottom: 40rpx;
		overflow: hidden;
	}

	.scroll-view {
		width: 100%;
		height: 100%;
		white-space: nowrap;
	}

	.scroll-item {
		display: inline-block;
		width: 513rpx;
		height: 100%;
		margin-left: 27rpx;
		overflow: hidden;
	}

	.scroll-item image {
		width: 513rpx;
		height: 160rpx;
		border-radius: 16rpx;
	}

	/* 云游金梭样式 start */
	.wander-jinsuo {
		width: 100%;

		.wander-header {
			width: 100%;
			height: 143rpx;
			position: relative;
			z-index: 1;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			background-position: center center;

			.wander-text-box {
				position: absolute;
				bottom: 66rpx;
				left: 54rpx;
				height: 42rpx;
				width: calc(100% - 110rpx);
				color: #622b27;
				font-size: 42rpx;
				font-weight: bold;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.wander-text-box text {
				font-size: 42rpx;
				font-weight: bold;
				color: #622b27;
				line-height: 1;
			}

			.wander-text-box image {
				width: 38rpx;
				height: 19rpx;
				vertical-align: middle;
				margin-top: 2rpx;
				display: flex;
				align-self: center;
			}
		}
	}

	/* 云游金梭样式 end */

	.jinsuo-header {
		width: 100%;
		height: 143rpx;
		position: relative;
		z-index: 1;
		overflow: hidden;
	}

	.jinsuo-header image {
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.wander-image {
		width: 100%;
		padding: 0 30rpx;
		height: 880rpx;
		margin-top: -14rpx;
		margin-bottom: 48rpx;
	}

	/* 金梭资讯样式 */
	.news-jinsuo,
	.island-guide {
		width: 100%;
		margin-bottom: 52rpx;
		position: relative;
	}

	.jinsuo-header {
		width: 100%;
		height: 143rpx;
		position: relative;
		z-index: 1;
		overflow: hidden;
	}

	.jinsuo-header image {
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.jinsuo-text {
		position: absolute;
		top: 40rpx;
		left: 60rpx;
		color: #622b27;
		font-size: 42rpx;
		font-weight: bold;
		line-height: 41rpx;
		z-index: 2;
	}

	.jinsuo-text-image {
		position: absolute;
		top: 54rpx;
		right: 54rpx;
		width: 38rpx !important;
		height: 19rpx !important;
		z-index: 2;
	}

	.news-container {
		width: 100%;
		position: relative;
		z-index: 2;
		margin-top: -22rpx;
	}

	.new-box {
		width: 100%;
		padding: 0 30rpx;
		height: 231rpx;
		background-color: transparent;
		position: relative;
		z-index: 2;
	}

	.news-list {
		display: flex;
		justify-content: space-between;
		width: 100%;
		height: 100%;
		background-color: transparent;
	}

	.news-item {
		width: 337rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #edf9fb;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
		position: relative;
		z-index: 3;
	}

	.news-item:last-child {
		margin-right: 0;
	}

	.news-image {
		width: 100%;
		height: 172rpx;
		border-radius: 20rpx;
		object-fit: cover;
		position: relative;
		z-index: 3;
	}

	.news-title {
		font-size: 24rpx;
		color: #333333;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		padding: 16rpx 17rpx;
		position: relative;
		z-index: 3;
	}

	/* 岛上攻略样式 */
	.island-container {
		width: 100%;
		position: relative;
		z-index: 2;
		margin-top: -56rpx;
	}

	.island-tab {
		width: 100%;
		padding: 0 58rpx;
		height: 100rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}

	.island-tab view {
		height: 48rpx;
		width: 130rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		color: #2da6c4;
		border: 1px solid #2da6c4;
		border-radius: 8rpx;
		margin-right: 16rpx;
		position: relative;
		z-index: 2;
		transition: all 0.2s;
	}

	.island-tab view:last-child {
		margin-right: 0;
	}

	.island-tab view.active {
		background-color: #2da6c4;
		color: #fff;
	}

	.island-content {
		width: 100%;
		padding: 0 30rpx;
		height: 348rpx;
	}

	.tab-content {
		display: flex;
		justify-content: space-between;
		height: 100%;
	}

	.tab-item {
		width: 409rpx;
		background-color: #edf9fb;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
		position: relative;
	}

	.right-items {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.tab-item-small {
		width: 265rpx;
		height: 165rpx;
		background-color: #edf9fb;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
		position: relative;
	}

	.tab-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		border-bottom-left-radius: 20rpx;
		border-bottom-right-radius: 20rpx;
	}

	.tab-title-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 16rpx 10rpx;
		z-index: 2;
	}

	.tab-title {
		font-size: 24rpx;
		color: #ffffff;
		line-height: 1.3;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: block;
	}

	.around-img-title {
		font-size: 36rpx;
		color: #ffffff;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: block;
	}

	.tab-item .tab-title {
		font-size: 28rpx;
	}

	.tab-item-small .tab-title {
		font-size: 26rpx;
	}

	.around-content {
		width: calc(100% - 60rpx);
		margin: 0 auto;
		height: 350rpx;
		border-radius: 20rpx;
		position: relative;
		background-color: #f8f8ff;
	}

	/* 安全提示弹窗 start */
	.safety-tips-modal {
		width: 526rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
	}

	.safety-tips-modal-header {
		width: 100%;
		height: 186rpx;
		position: relative;
	}

	.safety-tips-bg {
		width: 100%;
		height: 186rpx;
		position: relative;
	}

	.safety-tips-icon {
		width: 42rpx;
		height: 42rpx;
		position: absolute;
		top: 35rpx;
		left: 39rpx;
	}

	.safety-tips-header {
		position: absolute;
		top: 35rpx;
		left: 91rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
	}

	.safety-tips-title {
		font-weight: bold;
		font-size: 42rpx;
		color: #ffffff;
		line-height: 1;
	}

	.safety-tips-time {
		font-weight: 400;
		font-size: 20rpx;
		color: #ffffff;
		line-height: 1;
		margin-top: 15rpx;
	}

	.safety-tips-content {
		width: 100%;
		background: #ffffff;
		border-radius: 55rpx;
		padding: 44rpx;
		position: relative;
		margin-top: -53rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
	}

	.safety-tips-content-text {
		font-weight: 400;
		font-size: 30rpx;
		color: #f89235;
		line-height: 48rpx;
		margin-bottom: 40rpx;
	}

	.safety-tips-content-btn {
		width: 348rpx;
		height: 100rpx;
	}

	.safety-tips-cancel-btn {
		width: 72rpx;
		height: 72rpx;
		margin-top: 60rpx;
	}

	.safety-tips-cancel-btn-image {
		width: 100%;
		height: 100%;
	}

	/* 安全提示弹窗 end */
</style>
