<template>
	<view class="img-page">
		<!-- 标签导航栏 -->
		<view class="tab-header">
			<u-tabs
				:list="tabList"
				:current="currentCategoryIndex"
				@change="tabChange"
				:is-scroll="true"
				:line-height="0"
				:activeStyle="{
					color: '#2da6c4',
					border: '2rpx solid #2da6c4',
					borderRadius: '12rpx',
					backgroundColor: '#EEFCFF',
					padding: '14rpx 25rpx',
				}"
				:inactiveStyle="{
					color: '#8F8F9B',
					border: '2rpx solid #D5D4D9',
					borderRadius: '12rpx',
					padding: '14rpx 25rpx',
				}"></u-tabs>
		</view>

		<!-- 图片内容区域 -->
		<scroll-view
			class="content-scroll"
			scroll-y="true"
			:scroll-top="scrollTop"
			:style="{ height: scrollViewHeight + 'px' }"
			@scroll="handleScroll">
			<view
				v-for="(category, index) in categories"
				:key="index"
				:id="'category-' + index"
				class="category-section">
				<!-- 分类标题 -->
				<view class="category-title" v-if="category.name != '全部'">
					{{ category.name }}({{ category.images.length }})
				</view>

				<!-- 图片网格 -->
				<view class="image-grid" v-if="category.name != '全部'">
					<view
						v-for="(image, imgIndex) in category.images"
						:key="imgIndex"
						class="image-item"
						@tap="previewImage(category.images, imgIndex)">
						<image
							:src="getImageUrl(image.url)"
							mode="aspectFill"
							:lazy-load="true"
							@error="(e) => handleImgError(e, image.url)">
						</image>
					</view>
				</view>
			</view>

			<!-- 无图片提示 -->
			<view v-if="categories.length === 0 || (categories.length === 1 && categories[0].name === '全部' && categories[0].images.length === 0)" class="no-data">
				<text>暂无图片</text>
			</view>
		</scroll-view>

		<!-- 加载提示 -->
		<view class="loading-overlay" v-if="isLoading">
			<view class="loading-content">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import { mapState } from "vuex";
import { BASE_IMG_URL } from "@/env.js";

export default {
	data() {
		return {
			categories: [],
			tabList: [],
			currentCategoryIndex: 0,
			scrollTop: 0,
			scrollViewHeight: 500, // 默认高度，会在onReady中更新
			categoryPositions: [], // 存储每个分类的位置
			BASE_IMG_URL: "",
			visibleCategoryIndexes: [], // 记录可见分类的索引
			isLoading: true, // 加载状态
			imgErrors: {}, // 记录图片加载错误
		};
	},
	computed: {
		...mapState(["merchantDetail"]),
		// 判断当前是否为H5环境
		isH5() {
			// #ifdef H5
			return true;
			// #endif
			
			// #ifndef H5
			return false;
			// #endif
		}
	},
	onLoad() {
		this.BASE_IMG_URL = BASE_IMG_URL;
		this.isLoading = true;
		this.initImgData();
	},
	onShow() {
		this.initImgData();
	},
	onReady() {
		// 计算内容区域高度
		this.calculateScrollViewHeight();

		// 延迟获取分类位置信息
		setTimeout(() => {
			this.getCategoryPositions();
			this.isLoading = false;
		}, 500);
	},
	methods: {
		// 获取图片URL，处理编码问题
		getImageUrl(url) {
			if (!url) return '';
			
			try {
				// 完整URL处理
				let fullUrl = this.BASE_IMG_URL + url;
				
				// #ifdef H5
				// H5环境下特殊处理URL编码
				fullUrl = this.safeEncodeUrl(fullUrl);
				// #endif
				
				return fullUrl;
			} catch (error) {
				return this.BASE_IMG_URL + url; // 返回原始拼接，作为备选
			}
		},
		
		// 安全编码URL，处理中文和特殊字符
		safeEncodeUrl(url) {
			if (!url) return '';
			
			try {
				// 如果URL已经是完整的HTTP/HTTPS URL
				if (url.startsWith("http")) {
					// 分解URL，只对路径部分进行编码
					const urlObj = new URL(url);
					const path = urlObj.pathname;
					
					// 检查路径是否包含非ASCII字符（可能是中文）
					if (/[^\x00-\x7F]/.test(path)) {
						// 对路径部分进行编码
						const encodedPath = path.split('/').map(segment => 
							segment ? encodeURIComponent(segment) : ''
						).join('/');
						
						urlObj.pathname = encodedPath;
						return urlObj.toString();
					}
					return url;
				} else {
					// 对非完整URL进行编码
					return encodeURI(url);
				}
			} catch (e) {
				console.error("URL编码失败:", e, url);
				return url; // 出错时返回原始URL
			}
		},

		// 计算滚动视图高度
		calculateScrollViewHeight() {
			const systemInfo = uni.getSystemInfoSync();
			const tabHeight = 100; // 标签导航栏高度(rpx)，转换为实际像素时会自动处理

			// 计算内容区域高度 = 屏幕高度 - 标签导航栏高度
			// 注意：这里使用px是因为scrollViewHeight最终会用于style绑定，需要像素单位
			this.scrollViewHeight = systemInfo.windowHeight - uni.upx2px(tabHeight);
		},

		// 获取所有分类的位置信息
		getCategoryPositions() {
			const query = uni.createSelectorQuery().in(this);

			// 创建一个映射数组，记录非"全部"分类的索引
			this.visibleCategoryIndexes = [];
			
			// 遍历所有分类，只获取非"全部"分类的位置信息
			this.categories.forEach((category, index) => {
				if (category.name !== '全部') {
					this.visibleCategoryIndexes.push(index);
					const id = `#category-${index}`;
					query.select(id).boundingClientRect();
				}
			});

			query.exec((res) => {
				if (res && res.length > 0) {
					// 过滤掉无效的位置信息
					this.categoryPositions = res
						.filter((item) => item)
						.map((item) => ({
							top: item.top,
							height: item.height,
							bottom: item.bottom,
						}));
				}
			});
		},

		// 初始化图片数据
		initImgData() {
			try {
				// 尝试从merchantDetail获取图片列表
				if (this.merchantDetail && this.merchantDetail.imgList) {
					// 检查imgList是否为空对象
					const imgList = this.merchantDetail.imgList;
					const hasData = Object.keys(imgList).length > 0;
					
					if (hasData) {
						this.processImgData(imgList);
					} else {
						this.categories = [];
						this.tabList = [];
					}
				} else {
					// 如果没有数据，显示空数据
					this.categories = [];
					this.tabList = [];
				}

				// 延迟获取分类位置信息
				setTimeout(() => {
					this.getCategoryPositions();
				}, 500);
			} catch (error) {
				console.error("初始化图片数据出错:", error);
				this.categories = [];
				this.tabList = [];
			}
		},

		// 处理图片数据
		processImgData(imgList) {
			try {
				// 创建"全部"分类
				let allImages = [];
				const processedCategories = [];

				// 处理每个分类
				Object.keys(imgList).forEach((category) => {
					// 获取并过滤图片列表
					const rawImageUrls = this.getImages(imgList[category]);
					
					// 过滤掉空URL
					const validImageUrls = rawImageUrls.filter(url => url && url.trim() !== "");
					
					if (validImageUrls.length > 0) {
						// 为每张图片添加唯一标识
						const imagesWithId = validImageUrls.map(url => {
							return {
								url: url,
								id: uni.$u.guid(20) // 添加唯一标识
							};
						});
						
						processedCategories.push({
							name: category,
							images: imagesWithId,
						});

						// 将该分类的图片添加到"全部"分类
						allImages = allImages.concat(imagesWithId);
					}
				});

				// 如果有图片，添加"全部"分类
				if (allImages.length > 0) {
					processedCategories.unshift({
						name: "全部",
						images: allImages,
					});
				}

				this.categories = processedCategories;

				// 生成tabList用于u-tabs组件
				this.tabList = this.categories.map((category) => ({
					name: `${category.name}(${category.images.length})`,
				}));
			} catch (error) {
				console.error("处理图片数据出错:", error);
				this.categories = [];
				this.tabList = [];
				
				// 显示错误提示
				uni.showToast({
					title: '图片数据处理失败',
					icon: 'none'
				});
			}
		},

		// 处理图片数据字符串
		getImages(data) {
			if (!data) return [];
			try {
				// 如果data是字符串，尝试按逗号分割
				if (typeof data === 'string') {
					return data.split(",").filter((url) => url && url.trim() !== "");
				}
				// 如果data是数组，直接返回
				else if (Array.isArray(data)) {
					return data.filter((url) => url && url.trim() !== "");
				}
				// 其他情况返回空数组
				return [];
			} catch (error) {
				console.error("处理图片数据出错:", error, data);
				return [];
			}
		},

		// 预览图片
		previewImage(images, index) {
			// 如果没有图片，不执行操作
			if (!images || images.length === 0) return;
			
			try {
				// 获取所有非"全部"分类的所有图片，扁平化为一个数组
				let allImages = [];
				
				// 只把非"全部"分类的图片收集到 allImages 数组中
				this.categories.forEach(category => {
					if (category.name !== '全部' && category.images && category.images.length > 0) {
						allImages = [...allImages, ...category.images];
					}
				});
				
				// 如果没有图片，不执行操作
				if (allImages.length === 0) return;
				
				// 获取当前点击图片的唯一标识
				const currentImageId = images[index].id;
				
				// 使用唯一标识在所有图片中找到对应的索引
				let currentIndex = allImages.findIndex(img => img.id === currentImageId);
				if (currentIndex === -1) currentIndex = 0;
				
				// 添加 BASE_IMG_URL 前缀到所有图片的 URL，并处理编码
				const urlsWithPrefix = allImages.map(img => this.getImageUrl(img.url));
				
				// #ifdef H5
				// H5环境下特殊处理
				if (this.isH5) {
					// 如果只有一张图片，可以使用window.open在新标签页打开
					if (urlsWithPrefix.length === 1) {
						window.open(urlsWithPrefix[0], '_blank');
						return;
					}
				}
				// #endif
				
				// 打开图片预览
				uni.previewImage({
					urls: urlsWithPrefix,
					current: currentIndex,
					longPressActions: {
						itemList: ["保存图片", "收藏"],
						success: function (data) {
							// 不需要记录操作结果
						},
						fail: function (err) {
							console.error(err.errMsg);
						},
					},
					fail: (err) => {
						console.error("预览图片失败:", err);
						// 显示错误提示
						uni.showToast({
							title: '图片预览失败',
							icon: 'none'
						});
					}
				});
			} catch (error) {
				console.error("预览图片出错:", error);
				// 显示错误提示
				uni.showToast({
					title: '图片预览失败',
					icon: 'none'
				});
			}
		},

		// 处理图片错误
		handleImgError(e, url) {
			// 记录错误的URL
			this.imgErrors[url] = true;
			console.error("图片加载错误:", e, url);
			
			// #ifdef H5
			// H5环境下特殊处理
			if (this.isH5) {
				// H5环境下处理图片加载错误
			}
			// #endif
		},

		// u-tabs切换事件
		tabChange(e) {
			const index = e.index;
			this.currentCategoryIndex = index;
			
			// 如果是"全部"分类，滚动到顶部
			if (index === 0 && this.categories[0] && this.categories[0].name === "全部") {
				// 设置滚动位置为0（顶部）
				this.scrollTop = 0;
				// 使用uni.pageScrollTo作为备份方案
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300,
				});
				return;
			}
			
			// 计算实际的滚动目标索引
			// 如果当前选中的是"全部"分类之后的索引，需要在visibleCategoryIndexes中找到对应的索引
			const targetIndex = index > 0 ? index - 1 : 0; // 减1是因为"全部"分类在tab中占了一个位置
			this.scrollToCategory(targetIndex);
		},

		// 直接滚动到指定分类
		scrollToCategory(visibleIndex) {
			// 确保visibleIndex在有效范围内
			if (visibleIndex < 0 || visibleIndex >= this.categoryPositions.length) {
				console.error("无效的可见分类索引:", visibleIndex);
				return;
			}
			
			// 使用可见分类的位置信息
			const position = this.categoryPositions[visibleIndex];
			if (position) {
				// 计算需要滚动的距离
				const tabHeight = uni.upx2px(100); // 标签导航栏高度(rpx)转换为px

				// 设置滚动位置
				const scrollPosition = position.top - tabHeight;

				// 使用scroll-top属性滚动
				this.scrollTop = scrollPosition;

				// 同时使用uni.pageScrollTo作为备份方案
				uni.pageScrollTo({
					scrollTop: scrollPosition,
					duration: 300,
				});
			} else {
				console.error("未找到分类位置信息:", visibleIndex);

				// 尝试重新获取分类位置信息
				this.getCategoryPositions();

				// 使用延迟再次尝试滚动
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					// 使用可见分类的实际索引
					const actualIndex = this.visibleCategoryIndexes[visibleIndex];
					if (actualIndex === undefined) {
						console.error("无法找到对应的实际分类索引");
						return;
					}
					
					query.select(`#category-${actualIndex}`).boundingClientRect();
					query.selectViewport().scrollOffset();
					query.exec((res) => {
						if (res && res[0] && res[1]) {
							const element = res[0];
							const viewport = res[1];

							// 计算需要滚动的距离
							const tabHeight = uni.upx2px(100); // 标签导航栏高度(rpx)转换为px
							const scrollPosition = element.top + viewport.scrollTop - tabHeight;

							// 设置滚动位置
							this.scrollTop = scrollPosition;

							// 使用uni.pageScrollTo作为备份方案
							uni.pageScrollTo({
								scrollTop: scrollPosition,
								duration: 300,
							});
						}
					});
				}, 500);
			}
		},

		// 处理滚动事件
		handleScroll(e) {
			// 获取当前滚动位置
			const scrollTop = e.detail.scrollTop;

			// 如果有分类位置信息，可以根据滚动位置更新当前选中的标签
			if (this.categoryPositions && this.categoryPositions.length > 0) {
				// 查找当前滚动位置对应的分类
				const tabHeight = uni.upx2px(100); // 标签导航栏高度(rpx)转换为px
				const currentPosition = scrollTop + tabHeight;

				// 找到第一个底部位置大于当前滚动位置的分类
				for (let i = 0; i < this.categoryPositions.length; i++) {
					const position = this.categoryPositions[i];
					if (position && position.bottom > currentPosition) {
						// 计算实际的tab索引，考虑到"全部"分类的存在
						// 如果第一个分类是"全部"，那么可见分类的索引需要+1才是对应的tab索引
						let tabIndex = i;
						if (this.categories[0] && this.categories[0].name === "全部") {
							tabIndex = i + 1; // +1是因为"全部"分类在tab中占了一个位置
						}
						
						if (this.currentCategoryIndex !== tabIndex) {
							this.currentCategoryIndex = tabIndex;
						}
						break;
					}
				}
			}
		},
	},
};
</script>

<style>
.img-page {
	width: 100%;
	min-height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 50rpx;
}

.tab-header {
	width: 100%;
	height: 100rpx;
	background-color: #fff;
	border-bottom: 2rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 10;
	padding: 14rpx 0;
}

/* 自定义u-tabs样式 */
/deep/ .u-tabs__wrapper__scroll-view {
	padding: 0 20rpx;
}

/* 移除原有的样式，因为我们已经在activeStyle和inactiveStyle中设置了 */
/deep/ .u-tabs__wrapper__nav__item {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	overflow: visible;
	transition: all 0.3s;
}

/* 移除原有的底部线条样式 */
/deep/ .u-tabs__wrapper__nav__line {
	display: none !important;
}

.content-scroll {
	flex: 1;
	width: 100%;
}

.category-section {
	padding: 22rpx; /* 调整为与图片间距一致 */
}

.category-title {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 22rpx; /* 调整为与图片间距一致 */
	padding-left: 8rpx; /* 添加一点左边距，使标题与图片对齐更美观 */
}

.image-grid {
	display: grid;
	grid-template-columns: repeat(2, 333rpx); /* 两列，每列333rpx */
	column-gap: 22rpx; /* 列间距 */
	row-gap: 22rpx; /* 行间距 */
	justify-content: center; /* 居中显示 */
}

.image-item {
	width: 333rpx;
	height: 222rpx;
	overflow: hidden;
	border-radius: 16rpx;
	position: relative;
}

.image-item image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 无数据提示 */
.no-data {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300rpx;
	color: #999;
	font-size: 28rpx;
}

/* 加载中遮罩 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.7);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.loading-content {
	background-color: rgba(0, 0, 0, 0.6);
	padding: 20rpx 40rpx;
	border-radius: 10rpx;
	color: white;
	font-size: 28rpx;
}
</style>
