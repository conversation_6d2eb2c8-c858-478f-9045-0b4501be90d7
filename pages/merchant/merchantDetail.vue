<template>
	<view :class="iswx ? 'merchant-detail' : 'merchant-detail isvr'">
		<u-navbar v-if="iswx" title="岛上攻略" :autoBack="true"></u-navbar>
		<view class="merchant-swiper">
			<swiper
				class="swiper"
				:current="current"
				:indicator-dots="false"
				:autoplay="false"
				:duration="300"
				:circular="false"
				:acceleration="true"
				:cache-extent="1"
				@change="handleChange"
				@animationfinish="handleAnimationFinish">
				<swiper-item v-for="(item, index) in tabList" :key="index">
					<template v-if="item.type === 'cover'">
						<image
							style="width: 100%; height: 100%; border-radius: 20rpx"
							mode="aspectFill"
							:src="guideCover"
							lazy-load />
					</template>
					<template v-else-if="item.type === 'video'">
						<video
							v-if="showVideo && current === index"
							ref="videoPlayer"
							:key="'video-' + videoKey"
							style="width: 100%; height: 100%; border-radius: 20rpx"
							:src="video"
							controls
							object-fit="cover"
							:initial-time="0"
							:enable-progress-gesture="false"
							@play="onVideoPlay"
							@pause="onVideoPause"
							@ended="onVideoEnded"></video>
					</template>
					<template v-else-if="item.type === 'img'">
						<image
							style="width: 100%; height: 100%; border-radius: 20rpx"
							mode="aspectFill"
							:src="img" />
					</template>
				</swiper-item>
			</swiper>
			<!-- 封面，视频，相册 -->
			<view class="merchant-tab" ref="tabContainer" v-if="tabList.length > 1">
				<view
					class="merchant-tab__item"
					v-for="(item, index) in tabList"
					:key="index"
					:class="{ 'merchant-tab__item--active': current === index }"
					ref="tabItems"
					@click="tabClick(index)">
					{{ item.name }}
				</view>
				<!-- 动画滑块（不影响原有样式） -->
				<view class="tab-slider" :style="sliderStyle" v-if="enableAnimation"></view>
			</view>

			<!-- 指示器 -->
			<view class="indicator">
				<view
					class="indicator__dot"
					v-for="(item, index) in tabList"
					:key="index"
					:class="[index === current && 'indicator__dot--active']">
				</view>
			</view>
		</view>

		<!-- 商户名称 -->
		<view class="merchant-name">
			<text>{{ merchantName || "" }}</text>
		</view>

		<!-- 商户地址 -->
		<view class="merchant-address" v-if="merchantAddress">
			<view class="merchant-address-text">
				<text>{{ merchantAddress || "" }}</text>
			</view>
			<view class="merchant-address-nav" @tap="clickMap">
				<image
					class="location-icon"
					src="@/static/images/playIcons/location.png"
					mode="scaleToFill" />
				<view class="location-text">地图导航</view>
			</view>
		</view>

		<!-- 商户详情 -->
		<view class="merchant-detail-content">
			<u-parse :content="content"></u-parse>
		</view>

		<!-- 商户电话 -->
		<view class="merchant-phone" v-if="merchantPhone">
			<view class="merchant-phone-text">
				<text>{{ merchantPhone || "" }}</text>
			</view>
			<view class="merchant-phone-call" @click="clickMerchantPhone">
				<image
					class="merchant-phone-call-icon"
					src="@/static/images/playIcons/phonecall.png"
					mode="scaleToFill" />
				<text class="merchant-phone-call-text">拨打商家电话</text>
			</view>
		</view>

		<!-- 商户多个电话 -->
		<u-popup :show="showphone" :round="10" mode="bottom" @close="closepopup">
			<view class="phone-popup">
				<view class="phone-popup-header">
					<view class="phone-popup-header-text">联系商家</view>
					<image
						@click="closepopup"
						class="phone-popup-header-close"
						src="@/static/images/playIcons/closeicon.png"
						mode="scaleToFill" />
				</view>
				<view
					@click="callPhone(item.phone)"
					class="phone-popup-item"
					v-for="(item, index) in phoneList"
					:key="index">
					<view class="phone-popup-item-text">{{ item.phone }}</view>
					<image
						class="phone-popup-item-call"
						src="@/static/images/playIcons/call.png"
						mode="scaleToFill" />
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { getBusinessInfo } from "@/nxTemp/apis/common.js";
import { BASE_IMG_URL } from "@/env.js";

export default {
	data() {
		return {
			iswx: false,
			current: 0,
			tabList: [], //封面，视频，相册
			prevCurrent: 0, // 记录上一个选中项
			enableAnimation: false, // 控制动画滑块显示
			sliderPosition: {
				width: 0,
				left: 0,
			},
			content: "", // 商户详情
			merchantName: "", // 商户名称
			merchantAddress: "", // 商户地址
			guideCover: "", //封面
			video: "", //视频
			img: "", //相册
			merchantPhone: "", // 商户电话
			showphone: false, // 显示商户多个电话
			phoneList: [], //多个商户电话
			isNavigating: false, // 防止重复跳转
			videoKey: 0, // 用于强制刷新视频组件
			showVideo: true, // 控制视频显示
			videoContext: null, // 视频上下文对象
		};
	},
	computed: {
		sliderStyle() {
			return {
				width: this.sliderPosition.width + "px",
				left: this.sliderPosition.left + "px",
			};
		},
	},
	watch: {
		current(newVal, oldVal) {
			this.prevCurrent = oldVal;
			this.$nextTick(() => {
				this.updateSliderPosition();
				
				// 如果当前页是视频页，创建视频上下文
				if (this.tabList[newVal] && this.tabList[newVal].type === 'video') {
					this.createVideoContext();
				}
			});
		},
	},
	mounted() {
		// 初始化后启用动画
		setTimeout(() => {
			this.enableAnimation = true;
			this.updateSliderPosition();
		}, 300);
		
		// 初始化视频控制
		this.$nextTick(() => {
			// 尝试创建视频上下文
			this.createVideoContext();
		});
	},
	onLoad(options) {
		// 确保options存在
		// 如果URL参数中明确指定了iswx，则优先使用URL参数值
		this.iswx = options.iswx === "true" || options.iswx === true;
		// 如果有id参数，获取商户详情
		if (options.id) {
			this.getMerchantDetails(options.id);
		}
	},
	onShow() {},
	methods: {
		...mapActions(["setMerchantDetails"]),
		// 点击地图导航
		clickMap() {
			// 获取商户经纬度信息
			const latitude = this.$store.state.merchantDetail.latitude;
			const longitude = this.$store.state.merchantDetail.longitude;

			// 如果有经纬度信息
			if (latitude && longitude) {
				// 准备导航参数
				const name = encodeURIComponent(this.merchantName);
				const address = encodeURIComponent(this.merchantAddress);
				const lat = parseFloat(latitude);
				const lng = parseFloat(longitude);

				// #ifdef H5
				// H5环境下跳转到地图页面
				uni.$u.route({
					url: "pages/merchant/map",
					params: {
						latitude: lat,
						longitude: lng,
						name: this.merchantName,
						address: this.merchantAddress,
					},
				});
				return;
				// #endif

				// #ifdef MP-WEIXIN
				// 微信小程序环境下，使用微信内置地图
				uni.openLocation({
					latitude: lat,
					longitude: lng,
					name: this.merchantName,
					address: this.merchantAddress,
					success: () => {
						console.log("打开微信地图成功");
					},
					fail: (err) => {
						console.error("打开微信地图失败", err);
						uni.showToast({
							title: "打开地图失败",
							icon: "none",
						});
					},
				});
				return;
				// #endif
			} else {
				uni.$u.toast("暂无经纬度信息");
			}
		},
		// 获取商户详情数据
		getMerchantDetails(id) {
			uni.showLoading({
				title: "加载中...",
			});
			getBusinessInfo({
				id: id,
			})
				.then((res) => {
					const { data, code, message } = res.data;
					if (code === 200) {
						let videoList = (data.video && data.video.split(",")) || [];
						this.merchantAddress = data.address || "";
						this.merchantName = data.guideTitle || "";
						this.phoneList =
							(data.telephone &&
								data.telephone.split(",").map((phone) => ({
									phone,
								}))) ||
							[];
						this.merchantPhone = this.phoneList.length > 0 ? this.phoneList[0].phone : "";
						this.guideCover = this.safeUrl(BASE_IMG_URL + data.guideCover);
						this.video = videoList[0] ? this.safeUrl(BASE_IMG_URL + videoList[0].trim()) : "";
						this.img = this.getFirstUrl(data.imgMap);
						this.content = data.guideContent;

						// 一次性设置所有数据到Vuex中
						this.setMerchantDetails({
							// 视频数据
							videoList: data.video,
							// 相册数据
							imgList: data.imgMap,
							latitude: data.latitude,
							longitude: data.longitude,
							guideTitle: data.guideTitle,
							address: data.address,
						});
						// 根据detailsData更新tabList
						this.updateTabList();
						// 重置视频状态
						this.resetVideo();
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},

		// 提取第一个URL的函数
		// 提取第一个value的首个URL
		getFirstUrl(obj) {
			if (!obj) return "";
			// 将对象转换为键值对数组
			const entries = Object.entries(obj);
			if (entries.length === 0) return ""; // 空对象处理

			// 取第一个键值对的value
			const [, value] = entries[0];
			if (!value) return ""; // value为空处理

			// 分割逗号并取第一个URL，去除空格
			return this.safeUrl(BASE_IMG_URL + value.split(",")[0].trim() || "");
		},

		// 安全处理URL，确保中文字符被正确编码
		safeUrl(url) {
			if (!url) return "";

			// 如果URL已经是完整的HTTP/HTTPS URL
			if (url.startsWith("http")) {
				// 分解URL，只对路径部分进行编码
				try {
					const urlObj = new URL(url);
					const path = urlObj.pathname;
					// 检查路径是否包含非ASCII字符（可能是中文）
					if (/[^\x00-\x7F]/.test(path)) {
						// 对路径部分进行编码
						urlObj.pathname = encodeURI(path);
						return urlObj.toString();
					}
					return url;
				} catch (e) {
					// URL解析失败，直接对整个URL进行编码
					return encodeURI(url);
				}
			} else {
				// 对非完整URL进行编码（这些通常会与baseimgUrl拼接）
				return encodeURI(url);
			}
		},

		// 根据detailsData更新tabList
		updateTabList() {
			// 重置tabList
			let newTabList = [];

			// 如果有封面数据，添加封面选项
			if (this.guideCover) {
				newTabList.push({
					name: "封面",
					type: "cover",
				});
			}

			// 如果有视频数据，添加视频选项
			if (this.video) {
				newTabList.push({
					name: "视频",
					type: "video",
				});
			}

			// 如果有相册数据，添加相册选项
			if (this.img) {
				newTabList.push({
					name: "相册",
					type: "img",
				});
			}

			// 更新tabList
			this.tabList = newTabList;

			// 如果当前选中的标签超出了tabList的范围，重置为0
			if (this.current >= this.tabList.length) {
				this.current = 0;
			}

			// 更新滑块位置
			this.$nextTick(() => {
				this.updateSliderPosition();
			});
		},

		// 点击拨打商家电话 判断有多个还是一个  不同操作
		clickMerchantPhone() {
			if (this.phoneList && this.phoneList.length >= 2) {
				this.showphone = true;
			} else {
				this.callPhone(this.merchantPhone);
			}
		},

		// 拨打商家电话
		callPhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone,
			});
		},
		// 关闭商户多个电话
		closepopup() {
			this.showphone = false;
		},
		// 重置视频状态
		resetVideo() {
			console.log('重置视频状态');
			// 先隐藏视频组件
			this.showVideo = false;
			// 更新videoKey强制刷新视频组件
			this.videoKey = Date.now();
			
			// 在下一个事件循环中重新显示视频组件
			setTimeout(() => {
				this.showVideo = true;
				
				// 在视频组件重新渲染后，获取视频上下文
				this.$nextTick(() => {
					if (this.getVideoIndex() !== -1) {
						this.createVideoContext();
					}
				});
			}, 50);
		},
		// 点击tab项
		tabClick(index) {
			if (this.current === index) return;
			
			// 如果当前在视频选项卡，先停止视频播放
			this.stopVideoIfPlaying();
			
			// 重置视频
			this.resetVideo();
			this.switchTab(index);
		},
		// 切换商户tab (swiper滑动触发)
		handleChange(e) {
			try {
				const newCurrent = e.detail.current;
				
				// 如果当前在视频选项卡，先停止视频播放
				this.stopVideoIfPlaying();
				
				this.current = newCurrent;
				// 重置视频
				this.resetVideo();

				// 获取当前tab项
				const currentTab = this.tabList[newCurrent];
				
				// 只有类型是video或img时才执行跳转（cover类型不跳转）
				if (currentTab && (currentTab.type === 'video' || currentTab.type === 'img')) {
					this.switchTab(newCurrent);
				}
			} catch (err) {
				console.error("handleChange error:", err);
			}
		},
		// 统一处理tab切换逻辑
		switchTab(index) {
			try {
				// 防止重复跳转
				if (this.isNavigating) return;
				this.current = index;

				// 获取当前tab项的类型
				const currentTab = this.tabList[index];
				if (!currentTab) return;

				// 根据类型执行相应的跳转逻辑
				if (currentTab.type === 'video') {
					// 标记正在跳转中
					this.isNavigating = true;
					// 切换到视频标签，直接跳转到视频页面（数据已在getMerchantDetails中设置）
					uni.$u.route({
						url: "pages/merchant/videoPage",
						animationType: "slide-in-right",
					});
					// 2秒后重置状态，避免长时间锁定
					setTimeout(() => {
						this.isNavigating = false;
					}, 2000);
				} else if (currentTab.type === 'img') {
					// 标记正在跳转中
					this.isNavigating = true;
					// 切换到相册标签，直接跳转到相册页面（数据已在getMerchantDetails中设置）
					uni.$u.route({
						url: "pages/merchant/imgPage",
						animationType: "slide-in-right",
					});
					// 2秒后重置状态，避免长时间锁定
					setTimeout(() => {
						this.isNavigating = false;
					}, 2000);
				}
			} catch (err) {
				this.isNavigating = false;
				uni.showToast({
					title: "页面跳转失败",
					icon: "none",
				});
			}
		},
		// 更新滑块位置
		updateSliderPosition() {
			try {
				// 获取当前选中项的DOM
				if (!this.$refs.tabItems) return;

				// 确保当前索引在有效范围内
				if (this.current < 0 || this.current >= this.tabList.length) return;

				const activeItem = this.$refs.tabItems[this.current];
				if (activeItem) {
					// 获取元素位置和尺寸
					const rect = activeItem.getBoundingClientRect();
					// 获取容器位置
					const containerRect = this.$refs.tabContainer.getBoundingClientRect();

					// 计算相对位置
					this.sliderPosition = {
						width: rect.width,
						left: rect.left - containerRect.left,
					};
				}
			} catch (err) {
				console.error("updateSliderPosition error:", err);
			}
		},
		// 处理滑动动画完成
		handleAnimationFinish() {
			this.$nextTick(() => {
				this.updateSliderPosition();
			});
		},
		// 视频事件处理方法
		onVideoPlay() {
			console.log('视频开始播放');
		},
		
		onVideoPause() {
			console.log('视频已暂停');
		},
		
		onVideoEnded() {
			console.log('视频播放完毕');
			// 视频播放完毕后，重置状态
			this.resetVideo();
		},
		
		// 获取当前视频索引
		getVideoIndex() {
			return this.tabList.findIndex(item => item.type === 'video' && this.tabList.indexOf(item) === this.current);
		},
		
		// 创建视频上下文
		createVideoContext() {
			// 在某些平台上，需要延迟获取视频上下文
			setTimeout(() => {
				// #ifdef APP-PLUS || MP-WEIXIN || H5
				this.videoContext = uni.createVideoContext('videoPlayer', this);
				// #endif
			}, 100);
		},
		// 如果视频正在播放，停止它
		stopVideoIfPlaying() {
			// 查找当前是否有视频正在显示
			const videoIndex = this.tabList.findIndex(item => item.type === 'video' && this.tabList.indexOf(item) === this.current);
			
			if (videoIndex !== -1 && this.videoContext) {
				console.log('停止视频播放');
				// 尝试停止视频播放
				try {
					this.videoContext.stop();
				} catch (e) {
					console.error('停止视频失败:', e);
				}
			}
		},
	},
};
</script>

<style lang="scss">
.merchant-detail {
	width: 100%;
	background-color: #fff;
	padding: 40rpx 30rpx;
	// #ifdef H5
	margin-top: 90rpx;
	// #endif
	// #ifdef MP-WEIXIN
	margin-top: 150rpx;
	// #endif
}

.isvr {
	margin-top: 0;
	min-height: 100vh;
}

.merchant-swiper {
	width: 100%;
	height: 400rpx;
	border-radius: 20rpx;
	position: relative;
	overflow: hidden;
}

.swiper {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
	:deep(.uni-swiper-wrapper) {
		display: flex;
		transition-timing-function: ease-out;
	}

	:deep(.uni-swiper-item) {
		flex-shrink: 0;
		width: 100%;
		height: 100%;
		position: relative;
		transition-property: transform;
	}
}

.tab-slider {
	position: absolute;
	height: 100%;
	background-color: #ffffff;
	border-radius: 30rpx;
	top: 0;
	transition: all 0.3s ease;
	z-index: 1;
	pointer-events: none;
	opacity: 0.8;
}

.merchant-tab {
	position: absolute;
	bottom: 34rpx;
	left: 30rpx;
	width: 310rpx;
	height: 44rpx;
	display: flex;
	justify-content: center;
	z-index: 2;
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 30rpx;

	&__item {
		flex: 1;
		font-weight: 400;
		font-size: 28rpx;
		color: #fff;
		line-height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&--active {
			border-radius: 30rpx;
			color: #000000;
			background-color: #ffffff;
		}
	}
}

.indicator {
	position: absolute;
	bottom: 34rpx;
	right: 30rpx;
	display: flex;
	justify-content: center;
	z-index: 100;

	&__dot {
		height: 8rpx;
		width: 8rpx;
		border-radius: 4rpx;
		background-color: rgba(255, 255, 255, 0.35);
		margin: 0 5px;
		transition: background-color 0.3s;

		&--active {
			background-color: #ffffff;
			width: 40rpx;
		}
	}
}

.merchant-name {
	font-weight: 500;
	font-size: 48rpx;
	color: #262626;
	padding: 30rpx 0;
}

.merchant-address {
	width: 100%;
	height: 130rpx;
	background-image: url("@/static/images/playIcons/mapbg.png");
	background-size: 100% 100%;
	background-repeat: no-repeat;
	display: flex;
	align-items: center;
	padding: 0 22rpx;
	margin-bottom: 50rpx;
}

.merchant-address-text {
	font-weight: 400;
	font-size: 30rpx;
	color: #535961;
	flex: 1;
}

.merchant-address-nav {
	width: 88rpx;
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
}

.location-icon {
	width: 52rpx;
	height: 52rpx;
	margin-bottom: 10rpx;
}

.location-text {
	font-weight: 400;
	font-size: 22rpx;
	color: #888f97;
}

.merchant-phone {
	position: fixed;
	bottom: 30rpx;
	left: 30rpx;
	width: calc(100% - 60rpx);
	height: 130rpx;
	background: #ffffff;
	box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.12);
	border-radius: 20rpx;
	padding: 0 25rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.merchant-phone-text {
	font-weight: bold;
	font-size: 40rpx;
	color: #2da6c4;
	line-height: 32rpx;
}

.merchant-phone-call {
	width: 281rpx;
	height: 80rpx;
	background: #2da6c4;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.merchant-phone-call-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 10rpx;
}

.merchant-phone-call-text {
	font-weight: 400;
	font-size: 32rpx;
	color: #ffffff;
}

.phone-popup {
	width: 100%;
	min-height: 400rpx;
	padding: 40rpx 50rpx;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	background-color: #f8f8f8;
}

.phone-popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.phone-popup-header-text {
	font-weight: 500;
	font-size: 32rpx;
	color: #333333;
	line-height: 32rpx;
}

.phone-popup-header-close {
	width: 42rpx;
	height: 42rpx;
}

.phone-popup-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 140rpx;
	width: 100%;
	background: #ffffff;
	border-radius: 15rpx;
	margin-bottom: 30rpx;
	padding: 0 30rpx;
}

.phone-popup-item-text {
	font-weight: 400;
	font-size: 34rpx;
	color: #1a1a1a;
	line-height: 32rpx;
}

.phone-popup-item-call {
	width: 76rpx;
	height: 76rpx;
}

.merchant-detail-content {
	font-weight: 400;
	font-size: 28rpx;
	color: #4d5257;
	padding-bottom: 180rpx;
}

:deep(video) {
	width: 100%;
	height: 100%;
	object-fit: cover;
	background-color: #000;
}
</style>
