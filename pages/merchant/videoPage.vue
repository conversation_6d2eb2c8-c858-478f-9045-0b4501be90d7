<template>
	<view class="video-page">
		<!-- 导航栏 -->
		<u-navbar title="视频" :autoBack="true" bgColor="#000" leftIconColor="#fff" leftIconSize="25">
		</u-navbar>

		<!-- 视频轮播 -->
		<swiper
			class="swiper"
			:current="current"
			:indicator-dots="true"
			:indicator-color="'rgba(255, 255, 255, 0.5)'"
			:indicator-active-color="'#ffffff'"
			:autoplay="false"
			:vertical="true"
			@change="handleChange"
			@animationfinish="handleAnimationFinish">
			<swiper-item v-for="(item, index) in videoList" :key="index">
				<view class="video-container">
					<video
						:id="'video-' + index"
						:src="safeUrl(BASE_IMG_URL + item)"
						class="video-player"
						controls
						show-center-play-btn
						object-fit="contain"
						:initial-time="0"
						@error="handleVideoError"
						@play="onVideoPlay(index)"
						@pause="onVideoPause(index)"></video>
					<!-- 自定义播放按钮 -->
					<view class="custom-play-btn" v-if="!isPlaying[index]" @tap.stop="playVideo(index)">
						<u-icon name="play-right-fill" color="#ffffff" size="60"></u-icon>
					</view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 视频计数 -->
		<view class="video-counter" v-if="videoList.length > 1">
			{{ current + 1 }}/{{ videoList.length }}
		</view>
	</view>
</template>

<script>
import { mapState } from "vuex";
import { BASE_IMG_URL } from "@/env.js";

export default {
	data() {
		return {
			current: 0,
			videoList: [],
			videoContexts: [],
			isPlaying: {},
			BASE_IMG_URL: "",
		};
	},
	computed: {
		...mapState(["merchantDetail"]),
	},
	onLoad() {
		// 初始化视频列表
		this.initVideoList();
		this.BASE_IMG_URL = BASE_IMG_URL;
	},
	onShow() {
		// 页面显示时确保数据正确
		this.initVideoList();
	},
	onReady() {
		// 页面渲染完成后初始化视频上下文
		this.initVideoContexts();
	},
	onHide() {
		// 页面隐藏时暂停所有视频
		this.pauseAllVideos();
	},
	onUnload() {
		// 页面卸载时暂停所有视频
		this.pauseAllVideos();
	},
	methods: {
		// 初始化视频列表
		initVideoList() {
			try {
				// 尝试从merchantDetail获取视频列表
				if (this.merchantDetail && this.merchantDetail.videoList) {
					this.videoList = this.getVideo(this.merchantDetail.videoList);
				} else {
					// 如果vuex中没有，尝试从本地存储获取
					const cachedDetail = uni.getStorageSync("merchantDetail");
					if (cachedDetail && cachedDetail.videoList) {
						this.videoList = this.getVideo(cachedDetail.videoList);
					}
				}

				// 初始化播放状态
				this.videoList.forEach((_, index) => {
					this.$set(this.isPlaying, index, false);
				});
			} catch (error) {
				console.error("初始化视频列表出错:", error);
			}
		},

		// 初始化视频上下文
		initVideoContexts() {
			this.videoContexts = [];
			this.videoList.forEach((_, index) => {
				const videoContext = uni.createVideoContext(`video-${index}`, this);
				this.videoContexts.push(videoContext);
			});

			// 设置当前索引为0，确保从第一个视频开始
			this.current = 0;

			// 自动播放第一个视频（延迟执行以确保组件已渲染）
			setTimeout(() => {
				if (this.videoContexts.length > 0) {
					this.playVideo(0);
				}
			}, 500);
		},

		// 处理视频数据
		getVideo(data) {
			if (!data) return [];
			try {
				return data.split(",").filter((url) => url && url.trim() !== "");
			} catch (error) {
				console.error("处理视频数据出错:", error);
				return [];
			}
		},

		// 安全处理URL，确保中文字符被正确编码
		safeUrl(url) {
			if (!url) return '';
			
			// 如果URL已经是完整的HTTP/HTTPS URL
			if (url.startsWith('http')) {
				// 分解URL，只对路径部分进行编码
				try {
					const urlObj = new URL(url);
					const path = urlObj.pathname;
					// 检查路径是否包含非ASCII字符（可能是中文）
					if (/[^\x00-\x7F]/.test(path)) {
						// 对路径部分进行编码
						urlObj.pathname = encodeURI(path);
						return urlObj.toString();
					}
					return url;
				} catch (e) {
					// URL解析失败，直接对整个URL进行编码
					return encodeURI(url);
				}
			} else {
				// 对非完整URL进行编码（这些通常会与baseimgUrl拼接）
				return encodeURI(url);
			}
		},

		// 处理轮播切换
		handleChange(e) {
			const oldCurrent = this.current;
			this.current = e.detail.current;

			// 暂停上一个视频
			if (this.videoContexts[oldCurrent]) {
				this.videoContexts[oldCurrent].pause();
				this.$set(this.isPlaying, oldCurrent, false);
			}
		},

		// 处理动画结束
		handleAnimationFinish() {
			// 动画结束后播放当前视频
			this.playVideo(this.current);
		},

		// 播放指定索引的视频
		playVideo(index) {
			if (this.videoContexts[index]) {
				this.pauseAllVideos();
				this.videoContexts[index].play();
				this.$set(this.isPlaying, index, true);
			}
		},

		// 暂停指定索引的视频
		pauseVideo(index) {
			if (this.videoContexts[index]) {
				this.videoContexts[index].pause();
				this.$set(this.isPlaying, index, false);
			}
		},

		// 暂停所有视频
		pauseAllVideos() {
			this.videoContexts.forEach((context, index) => {
				if (context) {
					context.pause();
					this.$set(this.isPlaying, index, false);
				}
			});
		},

		// 视频播放事件处理
		onVideoPlay(index) {
			this.$set(this.isPlaying, index, true);
		},

		// 视频暂停事件处理
		onVideoPause(index) {
			this.$set(this.isPlaying, index, false);
		},

		// 处理视频错误
		handleVideoError(e) {
			console.error("视频加载错误:", e);
			uni.showToast({
				title: "视频加载失败",
				icon: "none",
				duration: 2000,
			});
		},
	},
};
</script>

<style>
.video-page {
	width: 100%;
	min-height: 100vh;
	background: #000;
	display: flex;
	flex-direction: column;
	position: relative;
}

.swiper {
	width: 100%;
	flex: 1;
	height: calc(100vh - 44px);
}

.video-container {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.video-player {
	width: 100%;
	height: 100%;
	max-height: 100vh;
}

.custom-play-btn {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80px;
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	z-index: 5;
}

.video-counter {
	position: absolute;
	top: 60px;
	right: 20px;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	padding: 4px 12px;
	border-radius: 20px;
	font-size: 14px;
	z-index: 10;
}
</style>
