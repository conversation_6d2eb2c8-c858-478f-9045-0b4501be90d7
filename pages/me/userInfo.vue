<template>
	<view class="user-info-page">
		<view class="user-info-content">
			<view class="user-info-item">
				<view class="item-label">用户头像</view>
				<view class="item-value" @tap="chooseAvatar">
					<image class="avatar-image" :src="tempAvatar || userAvatar || allImgList.defaultAvatar"></image>
				</view>
			</view>
			<view class="user-info-item">
				<view class="item-label">昵称</view>
				<view class="item-value">
					<u-input v-model="tempUserInfo.nickname" placeholder="请输入" border="none"></u-input>
				</view>
			</view>
			<view class="user-info-item">
				<view class="item-label">姓名</view>
				<view class="item-value">
					<u-input v-model="tempUserInfo.realName" placeholder="请输入" border="none"></u-input>
				</view>
			</view>
			<view class="user-info-item">
				<view class="item-label">性别</view>
				<view class="item-value">
					<u-radio-group v-model="tempUserInfo.sex" placement="row">
						<u-radio
							:customStyle="{
                marginRight: '30px',
                padding: '8rpx 0'
							}"
							v-for="(item, index) in sexList"
							:key="item.value"
							:label="item.name"
							:name="item.name"
              labelSize="30rpx"
              iconSize="36rpx">
						</u-radio>
					</u-radio-group>
				</view>
			</view>
			<view class="user-info-item">
				<view class="item-label">生日</view>
				<view class="item-value">
					<uni-datetime-picker type="date" :clear-icon="false" v-model="tempUserInfo.birthday" />
				</view>
			</view>
			<view class="user-info-item">
				<view class="item-label">电话号码</view>
				<view class="item-value">
					<u-input v-model="tempUserInfo.phone" placeholder="" disabled border="none"> </u-input>
				</view>
			</view>
		</view>
		<view class="change-password" @click="navigateTo('/pages/login/password')">
			<text class="password-text">修改密码</text>
			<image class="arrow-icon" src="@/static/images/icons/arrow-gray.png" mode="widthFix"></image>
		</view>
		<view class="save-info" @tap="saveUserInfo"> 保存资料 </view>
	</view>
</template>

<script>
import { mapState, mapActions, mapGetters } from "vuex";
import { getUserInfo, editUserInfo } from "@/nxTemp/apis/login.js";
import { BASE_URL, BASE_IMG_URL } from "@/env.js";
export default {
	data() {
		return {
			sexList: [
				{
					name: "男",
					value: 1,
				},
				{
					name: "女",
					value: 2,
				},
			],
			tempAvatar: "", // 临时存储上传的头像
			tempAvatarUrl: "", // 临时存储上传的头像地址
			tempUserInfo: {},
		};
	},
	computed: {
		...mapState(["userInfo", "allImgList"]),
		...mapGetters(["token"]),
		userAvatar() {
      if (this.userInfo.avatar) {
        return BASE_IMG_URL + this.userInfo.avatar;
      } else {
        return '';
      }
		},
	},
	mounted() {
		this.getUserInfoFun();
	},
	created() {
		this.tempUserInfo = this.userInfo;
		this.tempAvatarUrl = this.userInfo.avatar;
	},
	methods: {
		...mapActions(["setUserInfo"]),

		// 获取用户信息
		getUserInfoFun() {
			uni.showLoading({
				title: "加载中...",
			});
			getUserInfo()
				.then((res) => {
					const { code, data, message } = res.data;
					if (code == 200) {
						this.tempUserInfo = {
							...data,
							sex: this.sexList.find((item) => item.value == data.sex)?.name,
						};
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},
		// 选择头像
		chooseAvatar() {
			uni.showActionSheet({
				itemList: ["拍照", "从相册选择"],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 拍照
						this.takePhoto();
					} else if (res.tapIndex === 1) {
						// 从相册选择
						this.chooseFromAlbum();
					}
				},
			});
		},

		// 页面跳转
		navigateTo(url) {
			uni.navigateTo({
				url: url,
			});
		},

		// 拍照
		takePhoto() {
			uni.chooseImage({
				count: 1,
				sourceType: ["camera"],
				success: (res) => {
					this.uploadImage(res.tempFilePaths[0]);
				},
			});
		},

		// 从相册选择
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sourceType: ["album"],
				success: (res) => {
					this.uploadImage(res.tempFilePaths[0]);
				},
			});
		},

		// 上传图片
		uploadImage(filePath) {
			uni.showLoading({
				title: "上传中...",
			});

			// 这里替换为您的实际上传接口
			uni.uploadFile({
				url: BASE_URL + "/api/front/upload/image",
				filePath: filePath,
				name: "multipart",
				header: {
					"Authori-zation": this.token,
				},
				formData: {
					model: "user",
					pid: 125,
				},
				success: (uploadRes) => {
					const { code, data, message } = JSON.parse(uploadRes.data);
					// 上传成功后只更新临时头像
					this.tempAvatar = BASE_IMG_URL + data.url;
					this.tempAvatarUrl = data.url;
					uni.showToast({
						title: "上传成功",
						icon: "success",
					});
				},
				fail: () => {
					uni.showToast({
						title: "上传失败",
						icon: "none",
					});
				},
				complete: () => {
					uni.hideLoading();
				},
			});
		},

		// 保存用户信息
		async saveUserInfo() {
			uni.showLoading({
				title: "保存中...",
			});
			// 准备要保存的数据
			const saveData = {
				...this.tempUserInfo,
				avatar: this.tempAvatarUrl,
				sex: this.sexList.find((item) => this.tempUserInfo.sex == item.name)?.value,
			};
			editUserInfo(saveData)
				.then((res) => {
					const { code, data, message } = res.data;
					if (code == 200) {
						this.setUserInfo(saveData);
						uni.showToast({
							title: "保存成功",
							icon: "success",
              duration: 2000,
						});
            // // 添加延迟，让用户看到成功提示后再跳转
            setTimeout(() => {
              uni.switchTab({
                url: "/pages/me/index",
              });
            }, 2000);
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
            uni.hideLoading({
              noConflict: true
            });
				});
		},
	},
};
</script>

<style lang="scss" scoped>
.user-info-page {
	width: 100%;
	height: 100%;
	padding: 30rpx;
}

.user-info-content {
	background: #ffffff;
	box-shadow: 0rpx 5rpx 30rpx 0rpx rgba(23, 82, 113, 0.05);
	border-radius: 20rpx;
	padding: 0 34rpx;
}

.user-info-item {
	height: 117rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	border-bottom: 1rpx solid #e0e0e0;

	.item-label {
		font-weight: 500;
		font-size: 32rpx;
		color: #262626;
		line-height: 32rpx;
		padding-right: 33rpx;
	}

	.item-value {
		flex: 1;
		display: flex;
		align-items: center;
	}

	::v-deep .u-input__content {
		height: 90rpx;
		padding: 0 15rpx;
	}
}

.user-info-item:last-child {
	border-bottom: none;
}

.avatar-image {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
}

.change-password {
	width: 100%;
	height: 118rpx;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 0 34rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 30rpx;
}

.password-text {
	font-weight: 500;
	font-size: 32rpx;
	color: #262626;
	line-height: 32rpx;
}

.arrow-icon {
	width: 11rpx;
	height: 11rpx;
}

.save-info {
	margin: 0 auto;
	margin-top: 60rpx;
	width: 333rpx;
	height: 80rpx;
	background: #2da6c4;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 32rpx;
	color: #ffffff;
}
</style>
