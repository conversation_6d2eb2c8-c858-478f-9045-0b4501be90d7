<template>
	<view class="content">
		<view class="container">
			<!-- 修改密码 -->
			<u--form
				labelPosition="left"
				:model="regInfo"
				:errorType="['message']"
				:rules="rules"
				ref="uForm">
				<u-form-item prop="password">
					<u-input
						v-model="regInfo.password"
						placeholder="请输入8-20位数字字母组合密码"
						prefixIcon="lock"
						clearable
						:password="!showPassword"
						prefixIconStyle="font-size: 23;color: #ADADAD;"
						border="none">
						<template slot="suffix">
							<view
								@click="showPassword = !showPassword"
								style="height: 100%; display: flex; align-items: center">
								<u-icon
									:name="showPassword ? 'eye-off' : 'eye-fill'"
									size="38rpx"
									color="#ADADAD"></u-icon>
							</view>
						</template>
					</u-input>
				</u-form-item>
				<u-form-item prop="password2">
					<u-input
						v-model="regInfo.password2"
						placeholder="再次输入密码"
						prefixIcon="lock"
						:password="!showPassword2"
						clearable
						prefixIconStyle="font-size: 23;color: #ADADAD;"
						border="none">
						<template slot="suffix">
							<view
								@click="showPassword2 = !showPassword2"
								style="height: 100%; display: flex; align-items: center">
								<u-icon
									:name="showPassword2 ? 'eye-off' : 'eye-fill'"
									size="38rpx"
									color="#ADADAD"></u-icon>
							</view>
						</template>
					</u-input>
				</u-form-item>
			</u--form>
			<view class="save-info" @tap="save"> 保存 </view>
		</view>
	</view>
</template>

<script>
import { changePwd } from "@/nxTemp/apis/login.js";
export default {
	data() {
		// 自定义验证规则：验证两次密码是否一致
		const validatePassword = (rule, value, callback) => {
			if (value !== this.regInfo.password) {
				return callback(new Error("两次输入的密码不一致"));
			}
			return callback();
		};

		// 自定义验证规则：验证密码复杂度
		const validatePasswordStrength = (rule, value, callback) => {
			if (value.length < 8 || value.length > 20) {
				return callback(new Error("密码长度应为8-20位"));
			}
			if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
				return callback(new Error("密码应包含字母和数字"));
			}
			return callback();
		};
		return {
			regInfo: {
				password: "",
				password2: "",
			},
			rules: {
				password: [
					{
						required: true,
						message: "请输入密码",
					},
					{
						validator: validatePasswordStrength,
						message: "密码应为8-20位数字字母组合",
					},
				],
				password2: [
					{
						required: true,
						message: "请再次输入密码",
					},
					{
						validator: validatePassword,
						message: "两次输入的密码不一致",
					},
				],
			},
			showPassword: false, // 是否显示密码
			showPassword2: false, // 是否显示密码2
		};
	},
	onShow() {
		// 页面显示时重置密码字段
		this.regInfo.password = "";
		this.regInfo.password2 = "";
	},
	onReady() {
		this.$refs.uForm?.setRules(this.rules);
	},
	methods: {
		save() {
			this.$refs.uForm?.validate().then((res) => {
				if (res) {
					uni.showLoading({
						title: "正在修改密码...",
					});
					changePwd({
						password: this.regInfo.password2,
					})
						.then((res) => {
							uni.hideLoading();
							const { code } = res.data;
							if (code == 200) {
                uni.showToast({
                  title: "保存成功",
                  icon: "success",
                  duration: 2000
                });
                // 添加延迟，让用户看到成功提示后再跳转
                setTimeout(() => {
                  uni.switchTab({
                    url: "/pages/me/index",
                  });
                }, 2000);
							} else if (res && res.message) {
								uni.$u.toast(res.message);
							}
						})
						.finally(() => {
							uni.hideLoading({
                noConflict: true
              });
						});
				}
			});
		},
	},
};
</script>

<style>
::v-deep .u-form-item {
	height: 100rpx;
	background-color: #f6f6f6;
	margin-bottom: 45rpx;
	justify-content: center;
	padding: 0 30rpx;
	border-radius: 12rpx;
	position: relative;
}

::v-deep .u-form-item__body__right__message {
	margin-left: 0 !important;
	position: absolute;
	bottom: -25rpx;
	left: 30rpx;
	z-index: 2;
}

.content {
	padding: 35rpx;
}

.container {
	margin-top: 35rpx;
	padding: 30rpx;
	background-color: #fff;
	border-radius: 20rpx;
}

.save-info {
	margin: 0 auto;
	margin-top: 35rpx;
	width: 333rpx;
	height: 80rpx;
	background: #2da6c4;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 32rpx;
	color: #ffffff;
}
</style>
