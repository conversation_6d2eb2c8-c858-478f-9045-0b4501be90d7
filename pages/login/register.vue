<template>
	<view class="content">
		<u-navbar bgColor="transparent" leftIconSize="40rpx" leftIconColor="#fff" :autoBack="true">
		</u-navbar>
		<image class="login-bg" :src="allImgList.login_bg" mode="scaleToFill"></image>

		<view class="login-text">
			<view>Hi~欢迎您注册</view>
			<view>文旅金梭数字沙盘</view>
		</view>

		<view class="login-content">
			<view class="login-logo">
				<image :src="allImgList.login_logo" mode="scaleToFill"> </image>
			</view>

			<!-- 注册 -->
			<u--form
				labelPosition="left"
				:model="regInfo"
				:errorType="['message']"
				:rules="rules"
				ref="uForm">
				<u-form-item prop="phone">
					<u-input
						v-model="regInfo.phone"
						placeholder="请输入手机号"
						prefixIcon="phone"
						type="number"
						clearable
						prefixIconStyle="font-size: 23;color: #ADADAD;"
						border="none"></u-input>
				</u-form-item>
				<u-form-item prop="password">
					<u-input
						v-model="regInfo.password"
						placeholder="请输入8-20位数字字母组合密码"
						prefixIcon="lock"
						clearable
						:password="!showPassword"
						prefixIconStyle="font-size: 23;color: #ADADAD;"
						border="none">
						<template slot="suffix">
							<view
								@click="showPassword = !showPassword"
								style="height: 100%; display: flex; align-items: center">
								<u-icon
									:name="showPassword ? 'eye-off' : 'eye-fill'"
									size="38rpx"
									color="#ADADAD"></u-icon>
							</view>
						</template>
					</u-input>
				</u-form-item>
				<u-form-item prop="password2">
					<u-input
						v-model="regInfo.password2"
						placeholder="再次输入密码"
						prefixIcon="lock"
						:password="!showPassword2"
						clearable
						prefixIconStyle="font-size: 23;color: #ADADAD;"
						border="none">
						<template slot="suffix">
							<view
								@click="showPassword2 = !showPassword2"
								style="height: 100%; display: flex; align-items: center">
								<u-icon
									:name="showPassword2 ? 'eye-off' : 'eye-fill'"
									size="38rpx"
									color="#ADADAD"></u-icon>
							</view>
						</template>
					</u-input>
				</u-form-item>
				<view class="login-codebox">
					<u-form-item prop="captcha">
						<u-input
							v-model="regInfo.captcha"
							placeholder="请输入验证码"
							prefixIcon="checkmark-circle"
							prefixIconStyle="font-size: 18;color: #ADADAD;"
							clearable
							border="none">
						</u-input>
					</u-form-item>
					<view class="custom-style" @tap="getCode">
						{{ tips }}
					</view>
				</view>
			</u--form>

			<!-- 注册按钮 -->
			<view class="login-button" @click="clickRegister"> 立即注册 </view>
		</view>

		<!-- 验证码组件-->
		<u-code seconds="60" ref="uCode" @change="codeChange" @start="start" @end="end"></u-code>
	</view>
</template>

<script>
import { postRegister, postSendCode } from "@/nxTemp/apis/login.js";
export default {
	data() {
		// 自定义验证规则：验证手机号格式
		const validatePhone = (rule, value, callback) => {
			if (!/^1[3456789]\d{9}$/.test(value)) {
				return callback(new Error("请输入正确的手机号格式"));
			}
			return callback();
		};

		// 自定义验证规则：验证两次密码是否一致
		const validatePassword = (rule, value, callback) => {
			if (value !== this.regInfo.password) {
				return callback(new Error("两次输入的密码不一致"));
			}
			return callback();
		};

		// 自定义验证规则：验证密码复杂度
		const validatePasswordStrength = (rule, value, callback) => {
			if (value.length < 8 || value.length > 20) {
				return callback(new Error("密码长度应为8-20位"));
			}
			if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
				return callback(new Error("密码应包含字母和数字"));
			}
			return callback();
		};

		return {
			// 注册信息
			regInfo: {
				phone: "",
				password: "",
				password2: "",
				captcha: "",
			},
			rules: {
				phone: [
					{
						required: true,
						message: "请输入手机号",
					},
					{
						validator: validatePhone,
						message: "请输入正确的手机号格式",
					},
				],
				password: [
					{
						required: true,
						message: "请输入密码",
					},
					{
						validator: validatePasswordStrength,
						message: "密码应为8-20位数字字母组合",
					},
				],
				password2: [
					{
						required: true,
						message: "请再次输入密码",
					},
					{
						validator: validatePassword,
						message: "两次输入的密码不一致",
					},
				],
				captcha: [
					{
						required: true,
						message: "请输入验证码",
					},
				],
			},
			// 验证码提示
			tips: "",
			showPassword: false, // 是否显示密码
			showPassword2: false, // 是否显示密码2
		};
	},
	onLoad() {
		// 确保表单初始化时是空的
		this.regInfo = {
			phone: "",
			password: "",
			password2: "",
			captcha: "",
		};
	},
	onReady() {
		this.$refs.uForm?.setRules(this.rules);
	},
	onShow() {
		// 页面显示时重置密码字段
		this.regInfo.password = "";
		this.regInfo.password2 = "";
	},
	methods: {
		// 点击注册
		clickRegister() {
			this.$refs.uForm.validate().then((res) => {
				if (res) {
					this.register();
				}
			});
		},

		// 实际注册操作
		register() {
			uni.showLoading({
				title: "注册中...",
			});
			const datas = {
				phone: this.regInfo.phone,
				password: this.regInfo.password2,
				captcha: this.regInfo.captcha,
			};

			postRegister(datas)
				.then((res) => {
					const { code, message } = res.data;
					if (code == 200) {
						uni.$u.toast("注册成功");
						this.regInfo = {
							phone: "",
							password: "",
							password2: "",
							captcha: "",
						};
						uni.navigateBack();
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},

		// 验证码改变
		codeChange(text) {
			this.tips = text;
		},

		// 获取验证码
		getCode() {
			let phone = this.regInfo.phone;
			// 先校验手机号
			this.$refs.uForm.validateField("phone");
			if (!phone) {
				uni.$u.toast("请输入手机号");
				return;
			}
			console.log(phone, 666);

			uni.showLoading({
				title: "正在获取验证码",
			});
			postSendCode({
				phone,
			})
				.then((res) => {
					const { code } = res.data;
					if (code == 200) {
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast("验证码已发送");
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					} else if (res && res.message) {
						uni.$u.toast(res.message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},

		// 验证码开始倒计时
		start() {
			this.tips = "";
		},

		// 验证码倒计时结束
		end() {
			this.tips = "重新获取";
		},
	},
	watch: {
		// 监听密码变化，当密码变化时如果确认密码已有值，则重新校验确认密码
		"regInfo.password": function (val) {
			if (this.regInfo.password2) {
				this.$refs.uForm.validateField("password2");
			}
		},
	},
};
</script>

<style>
.login-bg {
	width: 100%;
	height: 545rpx;
	position: relative;
}

.login-text {
	position: absolute;
	height: 120rpx;
	top: 216rpx;
	left: 51rpx;
	color: #fff;
	font-size: 44rpx;
	font-weight: bold;
	display: flex;
	line-height: 1;
	flex-direction: column;
	justify-content: space-between;
}

.login-content {
	position: absolute;
	top: 405rpx;
	left: 0;
	width: 100%;
	min-height: calc(100% - 405rpx);
	background-color: #fff;
	border-top-left-radius: 40rpx;
	border-top-right-radius: 40rpx;
	padding: 0 45rpx;
}

.login-logo {
	width: 100%;
	height: 74rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 79rpx;
	margin-top: 68rpx;
}

.login-logo image {
	width: 312rpx;
	height: 74rpx;
}

.content-login {
	padding: 0 45rpx;
}

::v-deep .u-form-item {
	height: 100rpx;
	background-color: #f6f6f6;
	margin-bottom: 45rpx;
	justify-content: center;
	padding: 0 30rpx;
	border-radius: 12rpx;
	position: relative;
}

::v-deep .u-form-item__body__right__message {
	margin-left: 0 !important;
	position: absolute;
	bottom: -25rpx;
	left: 30rpx;
	z-index: 2;
}

.login-button {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(to bottom, #52cfee, #2da6c4);
	color: #fff;
	font-size: 34rpx;
	border-radius: 10rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.login-register {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-top: 30rpx;
}

.login-register text {
	font-size: 26rpx;
	color: #adadad;
}

.login-register text:last-child {
	color: #262626;
}

.register-text {
	font-weight: 400;
	font-size: 34rpx;
	color: #2da6c4;
	text-align: center;
	width: 100%;
	margin: 47rpx auto;
}

.login-button-first {
	background: #ebf6f9;
	color: #2da6c4;
	font-size: 34rpx;
	border: 1rpx solid #2da6c4;
	margin-top: 45rpx;
	margin-bottom: 30rpx;
}

.login-button-second {
	background: #ebf6f9;
	color: #2da6c4;
	font-size: 34rpx;
	border: 1rpx solid #2da6c4;
}

.login-codebox {
	display: flex;
	justify-content: space-between;
}

.login-codebox .u-form-item {
	flex: 1;
	margin-right: 20rpx;
}

.custom-style {
	width: 226rpx;
	height: 100rpx;
	background-color: #fdbb3c;
	border-radius: 12rpx;
	color: #fff;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
