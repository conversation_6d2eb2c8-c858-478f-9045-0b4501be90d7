<template>
	<view class="content">
		<u-navbar bgColor="transparent" leftIconSize="40rpx" leftIconColor="#fff" :autoBack="true">
		</u-navbar>
		<image class="login-bg" :src="allImgList.login_bg" mode="scaleToFill"></image>

		<view class="login-text">
			<view>Hi~欢迎登录</view>
			<view>文旅金梭数字沙盘</view>
		</view>

		<view class="login-content">
			<view class="login-logo">
				<image :src="allImgList.login_logo" mode="scaleToFill"> </image>
			</view>

			<!-- 登录 -->
			<u--form v-if="loginType == 2 || loginType == 3" labelPosition="left" :model="loginInfo" :rules="rules"
				:errorType="['message']" validateTrigger="change" ref="uForm">
				<u-form-item prop="phone">
					<u-input v-model="loginInfo.phone" placeholder="请输入手机号" prefixIcon="phone" type="number" clearable
						prefixIconStyle="font-size: 23;color: #ADADAD;" border="none"></u-input>
				</u-form-item>
				<u-form-item prop="password" v-if="loginType == 2">
					<u-input v-model="loginInfo.password" placeholder="请输入密码" prefixIcon="lock"
						:password="!showPassword" prefixIconStyle="font-size: 23;color: #ADADAD;" border="none">
						<template slot="suffix">
							<view @click="showPassword = !showPassword"
								style="height: 100%; display: flex; align-items: center">
								<u-icon :name="showPassword ? 'eye-off' : 'eye-fill'" size="38rpx"
									color="#ADADAD"></u-icon>
							</view>
						</template>
					</u-input>
				</u-form-item>

				<view class="login-codebox" v-if="loginType == 3">
					<u-form-item prop="captcha">
						<u-input v-model="loginInfo.captcha" placeholder="请输入验证码" prefixIcon="checkmark-circle"
							prefixIconStyle="font-size: 18;color: #ADADAD;" clearable border="none">
						</u-input>
					</u-form-item>
					<view class="custom-style" @tap="getCode">
						{{ tips }}
					</view>
				</view>
			</u--form>

			<!-- 登录按钮 -->
			<view class="login-button" v-if="loginType == 1">
				<button v-if="checkboxValue.length" class="login-btn" open-type="getPhoneNumber"
					@getphonenumber="getPhoneNumber">
					本机号码一键登录
				</button>
				<button v-else class="login-btn" @click="isAgreement">本机号码一键登录</button>
			</view>

			<view class="login-button" v-if="loginType != 1" @click="clickLogin"> 登录 </view>

			<!-- 《用户服务协议》 -->
			<view class="login-register">
				<u-checkbox-group v-model="checkboxValue" @click="handleCheckbox">
					<u-checkbox ref="checkbox" size="14" name="checkbox1" inactiveColor="#ADADAD" activeColor="#52CFEE"
						shape="circle"
            labelSize="30rpx"
            iconSize="36rpx"
          >
					</u-checkbox>
				</u-checkbox-group>
				<text @click="handleCheckbox">登录即代表您已阅读并同意</text>
				<text @click="navigateTo('/pages/login/agreement')">《用户服务协议》</text>
			</view>

			<!-- 注册账号  -->
			<view class="register-text" @click="navigateTo('/pages/login/register')">注册账号</view>

			<u-divider text="OR"></u-divider>

			<view class="login-button login-button-first" @click="changeLoginType">
				{{ loginType == 1 ? "手机号密码登录" : "本机号码登录" }}
			</view>
			<view class="login-button login-button-second" @click="switchToCodeLogin">
				手机号验证码登录
			</view>
		</view>

		<!-- 验证码组件-->
		<u-code seconds="60" ref="uCode" @change="codeChange" @start="start" @end="end"></u-code>
	</view>
</template>

<script>
	// 引入Vuex的mapActions
	import {
		mapActions,
		mapState
	} from "vuex";
	import {
		postPasswordLogin,
		postSendCode,
		postCaptchaLogin
	} from "@/nxTemp/apis/login.js";
	import {
		getWechatLogin,
		getWechatRegisterBindingPhone
	} from "@/nxTemp/apis/common.js";
	export default {
		data() {
			return {
				// 登录类型 1:本机号码登录 2:手机号密码登录 3:手机号验证码登录
				loginType: 1,
				// 登录信息
				loginInfo: {
					phone: "",
					password: "",
					captcha: "",
				},
				rules: {
					phone: [{
							required: true,
							message: "请输入手机号",
							trigger: ["blur", "change"],
						},
						{
							// 使用uView内置的手机号验证规则
							pattern: /^1[3456789]\d{9}$/,
							message: "请输入正确的手机号格式",
							trigger: ["blur", "change"],
						},
					],
					password: [{
						required: true,
						message: "请输入密码",
						trigger: ["blur", "change"],
					}, ],
					captcha: [{
						required: true,
						message: "请输入验证码",
						trigger: ["blur", "change"],
					}, ],
				},

				checkboxValue: [],
				// 验证码提示
				tips: "",
				showPassword: false, // 是否显示密码
				loginLoading: false, // 登录加载状态标志
			};
		},
		computed: {
			...mapState(["allImgList"]),
		},
		onReady() {
			this.$refs.uForm?.setRules(this.rules);
		},

		methods: {
			// 映射Vuex的actions
			...mapActions(["setToken", "setUserInfo"]),

			// 没有选中用户服务协议提示
			isAgreement() {
				uni.$u.toast("请勾选确认用户服务协议");
			},

			// 点击登录
			clickLogin() {
				// 判断是否选中用户服务协议
				if (!this.checkboxValue.includes("checkbox1")) {
					uni.$u.toast("请勾选确认用户服务协议");
					return;
				}
				this.$refs.uForm?.validate().then((res) => {
					if (res) {
						if (this.loginType == 2) {
							this.passwordLogin();
						} else {
							this.codeLogin();
						}
					}
				});
			},

			// 封装wx.login为Promise
			wxLoginPromise() {
				return new Promise((resolve, reject) => {
					wx.login({
						success: (res) => resolve(res),
						fail: (err) => reject(err)
					});
				});
			},

			// 获取手机号
			async getPhoneNumber(e) {
				if (e.detail.errMsg !== "getPhoneNumber:ok") {
					console.log("用户取消授权或获取手机号失败:", e.detail.errMsg);
					// 用户取消授权不显示错误提示
					if (e.detail.errMsg !== "getPhoneNumber:fail user deny") {
						uni.$u.toast('获取手机号失败');
					}
					return;
				}

				// 防止重复点击
				if (this.loginLoading) return;
				this.loginLoading = true;

				// 显示加载提示
				uni.showLoading({
					title: "登录中...",
					mask: true // 添加遮罩防止用户点击
				});

				// 设置超时保护，确保loading不会一直显示
				const loadingTimeout = setTimeout(() => {
					if (this.loginLoading) {
						this.loginLoading = false;
						uni.hideLoading();
						uni.$u.toast('登录超时，请重试');
						console.log('登录操作超时自动关闭');
					}
				}, 15000); // 15秒超时保护

				try {
					// 第一次微信登录获取code
					const res1 = await this.wxLoginPromise();

					try {
						// 调用微信登录接口
						const res2 = await getWechatLogin({
							code: res1.code,
							type: "routine",
						});

						const {
							code: res2Code,
							data: res2Data,
							message
						} = res2.data;

						if (res2Code == 200) {
							// 登录成功
							if (res2Data.type == "login") {
								// 清除超时保护
								clearTimeout(loadingTimeout);
								this.loginLoading = false;
								this.saveAndRedirect(res2Data);
							} else {
								// 去注册，需要第二次微信登录
								try {
									const res3 = await this.wxLoginPromise();

									try {
										// 调用注册绑定手机号接口
										const res4 = await getWechatRegisterBindingPhone({
											type: "routine",
											key: res2Data.key,
											code: res3.code,
											encryptedData: e.detail.encryptedData,
											iv: e.detail.iv,
										});

										const {
											code: res4Code,
											data: res4Data,
											message: res4Message
										} = res4.data;

										// 清除超时保护
										clearTimeout(loadingTimeout);
										this.loginLoading = false;

										if (res4Code == 200) {
											this.saveAndRedirect(res4Data);
										} else {
											uni.hideLoading();
											uni.$u.toast(res4Message || '注册绑定失败');
										}
									} catch (err) {
										// 清除超时保护
										clearTimeout(loadingTimeout);
										this.loginLoading = false;

										console.error("注册绑定失败:", err);
										uni.hideLoading();
										uni.$u.toast('注册绑定失败，请稍后重试');
									}
								} catch (err) {
									// 清除超时保护
									clearTimeout(loadingTimeout);
									this.loginLoading = false;

									console.error("第二次微信登录失败:", err);
									uni.hideLoading();
									uni.$u.toast('微信登录失败，请稍后重试');
								}
							}
						} else {
							// 清除超时保护
							clearTimeout(loadingTimeout);
							this.loginLoading = false;

							uni.hideLoading();
							uni.$u.toast(message || '微信登录失败');
						}
					} catch (err) {
						// 清除超时保护
						clearTimeout(loadingTimeout);
						this.loginLoading = false;

						console.error("微信登录请求失败:", err);
						uni.hideLoading();
						uni.$u.toast('登录失败，请稍后重试');
					}
				} catch (err) {
					// 清除超时保护
					clearTimeout(loadingTimeout);
					this.loginLoading = false;

					console.error("微信登录失败:", err);
					uni.hideLoading();
					uni.$u.toast('微信登录失败，请稍后重试');
				}
			},

			// 保存到Vuex并跳转
			saveAndRedirect(resData) {
				// 确保loading已关闭
				uni.hideLoading();

				// 保存到Vuex
				this.setToken(resData.token);
				this.setUserInfo(resData);

				// 获取redirect参数，如果存在则跳转到对应页面，否则跳转到首页
				const redirect = this.getRedirectPath();

				// 延迟跳转，确保Toast和loading状态都正确处理
				setTimeout(() => {

					if (redirect) {
						if (redirect == '/pages/vr/index') {
							uni.switchTab({
								url: redirect,
							})

						} else {
							uni.redirectTo({
								url: redirect,
							});
						}

					} else {
						uni.switchTab({
							url: '/pages/me/index',
						})
					}
				}, 300);
			},

			// 一键登录接口
			loginFun(loginCode, phoneCode) {
				console.log("loginCode", loginCode);
				console.log("phoneCode", phoneCode);
			},

			// 账号密码登录
			passwordLogin() {
				uni.showLoading({
					title: "登录中...",
				});

				postPasswordLogin({
						phone: this.loginInfo.phone,
						password: this.loginInfo.password,
					})
					.then((res) => {
						const {
							code,
							data,
							message
						} = res.data;
						if (code == 200) {
							this.saveAndRedirect(data);
						} else {
							uni.$u.toast(message);
						}
					})
					.catch((error) => {
						// 添加错误处理
						console.error("登录失败:", error);
						uni.$u.toast("登录失败，请稍后重试");
					})
					.finally(() => {
						// 确保在finally中隐藏loading
						uni.hideLoading();
					});
			},

			// 手机号验证码登录
			codeLogin() {
				uni.showLoading({
					title: "登录中...",
				});

				postCaptchaLogin({
						phone: this.loginInfo.phone,
						captcha: this.loginInfo.captcha,
					})
					.then((res) => {
						const {
							code,
							data,
							message
						} = res.data;
						if (code == 200) {
							this.saveAndRedirect(data);
						} else {
							uni.$u.toast(message);
						}
					})
					.catch((error) => {
						// 添加错误处理
						console.error("验证码登录失败:", error);
						uni.$u.toast("登录失败，请稍后重试");
					})
					.finally(() => {
						// 确保在finally中隐藏loading
						uni.hideLoading();
					});
			},

			// 获取重定向路径 TODO
			getRedirectPath() {
				const query =
					uni.getAppBaseInfo().platform === "h5" ? this.$route.query : this.getQueryParams();
				const encodedRedirect = query.redirect || "";
				// 如果有redirect参数且包含%，说明是编码后的URL，需要解码
				return encodedRedirect ? decodeURIComponent(encodedRedirect) : "";
			},

			// 获取当前页面的query参数（非H5平台）
			getQueryParams() {
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				return currentPage.options || {};
			},

			// 验证码改变
			codeChange(text) {
				this.tips = text;
			},

			// 改变登录类型
			changeLoginType() {
				this.loginType = this.loginType == 1 ? 2 : 1;
				// 切换登录方式时清空表单
				this.loginInfo = {
					phone: "",
					password: "",
					code: "",
				};
				// 延迟执行，确保DOM更新后再重置表单
				this.$nextTick(() => {
					if (this.loginType != 1) {
						this.$refs.uForm?.resetFields();
					}
				});
			},
			//改变checkbox状态
			handleCheckbox() {
				if (this.checkboxValue.includes("checkbox1")) {
					this.checkboxValue = [];
				} else {
					this.checkboxValue.push("checkbox1");
				}
			},
			// 获取验证码
			getCode() {
				let that = this;
				// 直接校验手机号
				if (!this.loginInfo.phone) {
					uni.$u.toast("请输入手机号");
					return;
				}

				// 手动验证手机号格式
				if (!/^1[3456789]\d{9}$/.test(this.loginInfo.phone)) {
					uni.$u.toast("请输入正确的手机号格式");
					return;
				}

				if (this.$refs.uCode.canGetCode) {
					uni.showLoading({
						title: "正在获取验证码",
					});
					postSendCode({
							phone: this.loginInfo.phone,
						})
						.then((res) => {
							uni.hideLoading();
							const {
								code,
								message
							} = res.data;
							if (code == 200) {
								// 这里此提示会被this.start()方法中的提示覆盖
								uni.$u.toast("验证码已发送");
								// 通知验证码组件内部开始倒计时
								that.$refs.uCode.start();
							} else {
								uni.$u.toast(message);
							}
						})
						.catch((err) => {
							console.log("err", err);
							uni.hideLoading();
							uni.$u.toast("获取验证码失败，请稍后重试");
						});
				}
			},
			// 跳转
			navigateTo(url) {
				uni.navigateTo({
					url: url,
				});
			},
			// 验证码开始倒计时
			start() {
				this.tips = "";
			},

			// 验证码倒计时结束
			end() {
				this.tips = "重新获取";
			},

			// 切换到验证码登录
			switchToCodeLogin() {
				this.loginType = 3;
				// 切换登录方式时清空表单
				this.loginInfo = {
					phone: "",
					password: "",
					code: "",
				};
				// 延迟执行，确保DOM更新后再重置表单
				this.$nextTick(() => {
					this.$refs.uForm?.resetFields();
				});
			},
		},
	};
</script>

<style>
	.login-bg {
		width: 100%;
		height: 545rpx;
		position: relative;
	}

	.login-text {
		position: absolute;
		height: 120rpx;
		top: 216rpx;
		left: 51rpx;
		color: #fff;
		font-size: 44rpx;
		font-weight: bold;
		display: flex;
		line-height: 1;
		flex-direction: column;
		justify-content: space-between;
	}

	.login-content {
		position: absolute;
		top: 405rpx;
		left: 0;
		width: 100%;
		min-height: calc(100% - 405rpx);
		background-color: #fff;
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		padding: 0 45rpx;
	}

	.login-logo {
		width: 100%;
		height: 74rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 79rpx;
		margin-top: 68rpx;
	}

	.login-logo image {
		width: 312rpx;
		height: 74rpx;
	}

	.content-login {
		padding: 0 45rpx;
	}

	::v-deep .u-form-item {
		height: 100rpx;
		background-color: #f6f6f6;
		margin-bottom: 45rpx;
		justify-content: center;
		padding: 0 30rpx;
		border-radius: 12rpx;
		position: relative;
	}

	::v-deep .u-form-item__body__right__message {
		margin-left: 0 !important;
		position: absolute;
		bottom: -25rpx;
		left: 30rpx;
		z-index: 2;
	}

	.login-button {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(to bottom, #52cfee, #2da6c4);
		color: #fff;
		font-size: 34rpx;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.login-btn {
		width: 100%;
		height: 100%;
		background-color: transparent;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 34rpx;
	}

	.login-register {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-top: 30rpx;
	}

	.login-register text {
		font-size: 26rpx;
		color: #adadad;
	}

	.login-register text:last-child {
		color: #262626;
	}

	.register-text {
		font-weight: 400;
		font-size: 34rpx;
		color: #2da6c4;
		text-align: center;
		width: 100%;
		margin: 47rpx auto;
	}

	.login-button-first {
		background: #ebf6f9;
		color: #2da6c4;
		font-size: 34rpx;
		border: 1rpx solid #2da6c4;
		margin-top: 45rpx;
		margin-bottom: 30rpx;
	}

	.login-button-second {
		background: #ebf6f9;
		color: #2da6c4;
		font-size: 34rpx;
		border: 1rpx solid #2da6c4;
	}

	.login-codebox {
		display: flex;
		justify-content: space-between;
	}

	.login-codebox .u-form-item {
		flex: 1;
		margin-right: 20rpx;
	}

	.custom-style {
		width: 226rpx;
		height: 100rpx;
		background-color: #fdbb3c;
		border-radius: 12rpx;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>