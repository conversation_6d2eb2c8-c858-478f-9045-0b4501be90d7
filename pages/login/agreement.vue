<template>
	<view class="agreement-container">
		<u-parse v-if="agreementContent" :content="agreementContent"></u-parse>
		<view v-else>暂无数据</view>
	</view>
</template>

<script>
import { getAgreement } from "@/nxTemp/apis/common";
export default {
	data() {
		return {
			agreementContent: "", // 富文本内容
		};
	},
	onLoad(options) {
		this.getAgreementData();
	},
	methods: {
		// 获取协议内容
		getAgreementData() {
			uni.showLoading({
				title: "加载中",
			});
			getAgreement()
				.then((res) => {
					const { data, code, message } = res.data;
					if (code === 200) {
						this.agreementContent = JSON.parse(data)?.agreement || "";
					} else {
						uni.$u.toast(message);
					}
				})
				.catch((error) => {
					console.error("获取协议内容失败", error);
					uni.$u.toast("获取协议内容失败");
				})
				.finally(() => {
					uni.hideLoading();
				});
		},
	},
};
</script>

<style>
.agreement-container {
	padding: 30rpx;
	min-height: 100vh;
}
</style>
