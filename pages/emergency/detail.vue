<template>
	<view class="emergency-detail-page">
		<u-parse :content="emergencyDesc"></u-parse>
	</view>
</template>

<script>
import { mapState } from "vuex";
export default {
	data() {
		return {
		};
	},
	computed: {
		...mapState(["emergencyDesc"]),
	},
	methods: {},
};
</script>

<style>
.emergency-detail-page {
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	padding: 30rpx;
}
</style>
