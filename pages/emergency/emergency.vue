<template>
	<view class="emergency-page">
		<u-navbar bgColor="transparent" leftIconSize="40rpx" leftIconColor="#fff" :autoBack="true">
		</u-navbar>
		<image :src="allImgList.emergency_bg" mode="scaleToFill" class="emergency-image"></image>
		<view class="emergency-header">
			<view class="header-title">金梭岛龙宫景区应急救援电话</view>
			<view class="header-phone">{{ phoneNumber }}</view>
			<image
				@click="callPhone"
				:src="allImgList.emergencyCall"
				mode="scaleToFill"
				class="emergency-header-image">
			</image>
			<view class="emergency-container">
				<!-- 应急预警 -->
				<view class="emergency-item" v-for="(item, index) in emergencyList" :key="$u.guid()">
					<view class="emergency-item-box">
						<view class="emergency-item-title">{{ item.ewTitle || "" }}</view>
						<view class="emergency-item-time">{{
							formatDateToChinese(item.updateTime) || ""
						}}</view>
					</view>
					<view class="emergency-item-content">
						<u-parse :content="item.ewContent"></u-parse>
					</view>
				</view>

				<!-- 应急疏散路线 -->
				<view class="evacuate-title">
					<text>应急疏散路线</text>
					<image src="@/static/images/icons/bg-img.png" mode="scaleToFill" alt="" />
				</view>
				<image @tap="goMap" :src="emergencyImageUrl" mode="scaleToFill" class="evacuate-image">
				</image>

				<!-- 安全注意事项 -->
				<view class="evacuate-title">
					<text>安全注意事项</text>
					<image src="@/static/images/icons/bg-img.png" mode="scaleToFill" alt="" />
				</view>
				<view class="safety-item" v-for="(item, index) in safetyList" :key="$u.guid()">
					<view class="safety-item-title">
						<text>{{ item.name }}</text>
						<image src="@/static/images/icons/line-b.png" mode="scaleToFill" alt="" />
					</view>
					<view class="safety-item-content">
						<u-parse :content="item.content"></u-parse>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getEmergency, getWarningList } from "@/nxTemp/apis/common";
import tools from "@/nxTemp/utils/tools";
import { BASE_IMG_URL } from "@/env.js";
import { mapActions, mapState } from "vuex";
export default {
	data() {
		return {
			// 应急预警
			emergencyList: [],
			// 安全注意事项
			safetyList: [],
			// 应急救援详情
			emergencyDetails: {},
			// 救援电话
			phoneNumber: "",
		};
	},
	onLoad() {
		this.getEmergencyDetails();
		this.getWarningListFun();
	},
	computed: {
		...mapState(["allImgList"]),
		emergencyImageUrl() {
			return this.emergencyDetails && this.emergencyDetails.erLineImg
				? BASE_IMG_URL + this.emergencyDetails.erLineImg
				: "";
		},
	},
	methods: {
		...mapActions(["setEmergencyDesc"]),
		// 注册工具函数到methods中，这样可以在模板中直接使用
		formatDateToChinese(dateStr) {
			return tools.formatDateToChinese(dateStr);
		},
		//获取应急救援详情
		getEmergencyDetails() {
			uni.showLoading({
				title: "加载中",
			});
			getEmergency()
				.then((res) => {
					const { data, code, message } = res.data;
					if (code == 200) {
						this.emergencyDetails = data;
						this.safetyList = JSON.parse(data.erSecurityItem);
						this.phoneNumber = data.erPhone;
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},
		//获取预警管理列表
		getWarningListFun() {
			uni.showLoading({
				title: "加载中",
			});
			getWarningList()
				.then((res) => {
					const { data, code, message } = res.data;
					if (code == 200) {
						this.emergencyList = data && data.list ? data.list : [];
					} else {
						uni.$u.toast(message);
					}
				})
				.finally(() => {
					uni.hideLoading();
				});
		},

		// 拨打电话
		callPhone() {
			uni.makePhoneCall({
				phoneNumber: this.phoneNumber,
			});
		},
		// 跳转地图
		goMap() {
			this.setEmergencyDesc(this.emergencyDetails.erLineDesc);
			uni.navigateTo({
				url: "/pages/emergency/detail",
			});
		},
	},
};
</script>

<style>
.emergency-page {
	width: 100%;
	height: 100%;
	position: relative;
}

.emergency-image {
	width: 100%;
	height: 574rpx;
}

.emergency-header {
	position: absolute;
	top: 174rpx;
	left: 0;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.emergency-header-image {
	width: 384rpx;
	height: 95rpx;
	margin-bottom: 30rpx;
}

.header-title {
	font-weight: 500;
	font-size: 36rpx;
	color: #ffffff;
}

.header-phone {
	font-weight: 800;
	font-size: 53rpx;
	color: #ffffff;
	margin-top: 23rpx;
	margin-bottom: 31rpx;
}
.emergency-container {
	width: 100%;
	background-color: #ffffff;
	border-radius: 37rpx 37rpx 0rpx 0rpx;
	padding: 40rpx 30rpx;
}
.emergency-item {
	background-color: #fff6ee;
	padding: 20rpx;
	margin-bottom: 26rpx;
	border-radius: 15rpx;
}
.emergency-item-box {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 26rpx;
}
.emergency-item-title {
	font-size: 26rpx;
	color: #fff;
	padding: 13rpx 18rpx;
	background-color: #f89235;
	border-radius: 999rpx;
	margin-right: 20rpx;
}
.emergency-item-time {
	font-weight: 400;
	font-size: 22rpx;
	color: #f89235;
	line-height: 42rpx;
}
.emergency-item-content {
	font-weight: 400;
	font-size: 26rpx;
	color: #f89235;
	line-height: 42rpx;
}
.evacuate-title {
	font-weight: bold;
	font-size: 36rpx;
	color: #262626;
	line-height: 32rpx;
	margin-bottom: 27rpx;
	margin-top: 46rpx;
	position: relative;
}
.evacuate-title image {
	position: absolute;
	top: 6rpx;
	left: 31rpx;
	width: 42rpx;
	height: 39rpx;
}
.evacuate-image {
	width: 100%;
	height: 920rpx;
	background: #dfdfdf;
	border-radius: 20rpx;
}
.safety-item {
	background: #eff9fc;
	border-radius: 15rpx;
	padding: 27rpx;
	margin-bottom: 30rpx;
}
.safety-item-title {
	margin-bottom: 27rpx;
	position: relative;
}
.safety-item-title text {
	font-weight: bold;
	font-size: 32rpx;
	color: #262626;
	line-height: 32rpx;
	z-index: 2;
	display: inline-block;
	position: relative;
}
.safety-item-title image {
	position: absolute;
	top: 22rpx;
	left: 0;
	width: 152rpx;
	height: 15rpx;
	z-index: 1;
}
.safety-item-content {
	font-weight: 400;
	font-size: 30rpx;
	color: #484848;
	line-height: 42rpx;
}
</style>
