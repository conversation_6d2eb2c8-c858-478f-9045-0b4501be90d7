<template>
	<!-- z-paging默认铺满全屏，此时页面所有view都应放在z-paging标签内，否则会被盖住 -->
	<view class="shopping-container">
		<image class="online-service" :src="allImgList.onlineService || ''" mode="aspectFill"> </image>
		<!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
		<image class="shopping-bgimg" :src="allImgList.shoppingBg || ''" mode="aspectFill"> </image>
		<view class="shopping-box">
			<!-- 搜索框 -->
			<view class="search-box">
				<u-input placeholder-style="color: #797979; font-size: 26rpx;"
					:customStyle="{backgroundColor: '#fff', marginLeft: '30rpx', padding: '4px', height: '70rpx'}"
					shape="circle" v-model="name" placeholder="输入商品关键词" border="surround" clearable>
					<template #prefix>
						<image class="shopping-search-bgimg" :src="allImgList.shoppingSearch || ''"
							mode="aspectFill"> </image>
					</template>
					<template #suffix>
						<button class="search-btn" type="warn">搜索</button>
					</template>
				</u-input>
				<image class="shopping-cat-bgimg" :src="allImgList.shoppingCart || ''" mode="aspectFill"> </image>
			</view>
			<!-- 轮播图 -->
			<view class="swiper">
				<u-swiper :list="list" @change="e => current = e.current" :autoplay="true" :current="current"
					:circular="true" height="100" radius="10">
					<template #indicator>
						<view class="indicator">
							<view class="indicator__dot" v-for="(item, index) in list" :key="index"
								:class="[index === current && 'indicator__dot--active']"
								@click="clickIndicator(index)">
							</view>
						</view>
					</template>
				</u-swiper>
			</view>
			<!-- 导航菜单 -->
			<view class="nav-menu">
				<scroll-view scroll-x class="nav-scroll" @scroll="onScroll" show-scrollbar="false"
					:scroll-into-view="scrollIntoView">
					<view class="nav-content">
						<view @click="headelList(item)" v-for="(item, index) in navList" :key="index" class="nav-item"
							:id="'nav-item-' + index">
							<image src="@/static/images/shopping/dd.png" mode="aspectFit"></image>
							<text>{{ item.name }}</text>
							<text v-if="item.badge" class="badge"
								:class="{ 'square-badge': index === 2 }">{{ item.badge }}</text>
						</view>
					</view>
				</scroll-view>
				<view class="nav-indicator" :style="{ '--indicator-left': indicatorLeft + 'px' }"></view>
			</view>
			<!-- 新人专享礼 -->
			<view class="new-user-gift">
				<image class="gift-banner" :src="allImgList.shoppingCapsule || ''" mode="aspectFill"> </image>
			</view>
			<!-- 限时抢购和品质商家 -->
			<view class="section-container">
				<!-- 限时抢购 -->
				<view class="flash-sale">
					<view class="flash-sale-bg">
						<view class="section-title">
							<image class="flash-sale-image" :src="allImgList.shoppingTime || ''" mode="aspectFill">
							</image>
							<view class="countdown">
								<text class="end-text">距结束</text>
								<text class="time">12:12:12</text>
								<image class="end-bg" :src="allImgList.shoppingEndBg || ''" mode="aspectFill">
								</image>
							</view>
						</view>
						<view class="product-list">
							<view class="product-item" v-for="(item, index) in flashSaleList" :key="index">
								<image src="@/static/images/shopping/goods.png" mode="aspectFill"
									class="product-image"></image>
								<view class="price">¥{{item.price}}</view>
							</view>
						</view>
					</view>
				</view>
			
				<!-- 品质商家 -->
				<view class="quality-merchant">
					<view class="quality-merchant-bg">
						<view class="section-title">
							<text>品质商家</text>
						</view>
						<view class="merchant-list">
							<view @click="headelMerchant(item)" class="merchant-item" v-for="(item, index) in merchantList" :key="index">
								<image src="@/static/images/shopping/house-number.png" mode="aspectFill"
									class="merchant-image"></image>
								<image class="avatar-image" src="@/static/images/shopping/profile-picture.png"
									mode="aspectFill"></image>
								<view class="merchant-info">
									<text class="name">{{item.name}}</text>
									<text class="coupon">{{item.coupon}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 商品列表 -->
			<u-sticky offset-top="0" customNavHeight="0" bgColor="#f5f5f5">
				<u-tabs :list="list4"
					lineWidth="20"
					lineHeight="7"
					:customNavHeight="0"
					:lineColor="`url(${lineBg}) 100% 100%`"
					:activeStyle="{
						color: '#303133',
						fontWeight: 'bold',
						transform: 'scale(1.05)',
						fontSize: '32rpx'
					}"
					:inactiveStyle="{
						color: '#606266',
						transform: 'scale(1)',
						fontSize: '28rpx'
					}"
					style="margin: 0 10rpx;">
				</u-tabs>
			</u-sticky>
			<z-paging ref="paging" v-model="dataList" @query="queryList" :use-page-scroll="true" :scrollable="false" :refresher-enabled="false" :auto-show-back-to-top="true">
				<!-- 自定义 loading 模板 -->
				<template slot="loading">
					<view class="custom-loading">
						<u-loading-icon :vertical="true" mode="circle" text="正在加载资讯..." size="24" textSize="14" color="#409EFF"></u-loading-icon>
					</view>
				</template>
				<view class="paging-box">
					<view v-for="(item,index) in dataList" :key="index" class="paging-item">
						<image class="paging-image" src="@/static/images/shopping/sy.png" mode="aspectFill"></image>
						<view class="title">普洱三道茶普洱三道茶</view>
						<view class="price-box">
							<text class="unit">￥</text>
							<text class="price">150.90</text>
							<text class="sold">已售 19</text>
						</view>
						<view class="discount-information">
							<view class="deduction deduction-item">
								积分最高抵扣￥15
							</view>
							<view class="no-reason deduction-item">
								7天无理由
							</view>
						</view>
					</view>
				</view>
			</z-paging>
		</view>
	</view>
</template>

<script>
import { mapState } from "vuex";
export default {
	data() {
		return {
				name: '',
				current: 0,
				scrollLeft: 0,
				indicatorLeft: 0,
				scrollIntoView: '',
				navList: [{
						name: '我的订单',
						icon: '/static/images/icons/order.png',
						badge: '2'
					},
					{
						name: '我的收藏',
						icon: '/static/images/icons/favorite.png'
					},
					{
						name: '优惠券',
						icon: '/static/images/icons/coupon.png',
						badge: '3张券'
					},
					{
						name: '积分购物',
						icon: '/static/images/icons/points.png'
					},
					{
						name: '专属服务',
						icon: '/static/images/icons/service.png'
					},
					{
						name: '更多服务',
						icon: '/static/images/icons/more.png'
					}
				],
				list: [
					"https://uviewui.com/swiper/swiper1.png",
					"https://uviewui.com/swiper/swiper2.png",
					"https://uviewui.com/swiper/swiper3.png",
				],
				flashSaleList: [{
						image: '/static/images/product1.jpg',
						price: '9.9'
					},
					{
						image: '/static/images/product2.jpg',
						price: '9.9'
					}
				],
				merchantList: [{
						image: '/static/images/merchant1.jpg',
						name: '阿凤婆小吃',
						coupon: '领券满30减5'
					},
					{
						image: '/static/images/merchant2.jpg',
						name: '阿凤婆小吃',
						coupon: '领券满30减5'
					}
				],
				list4: [{
					name: '全部'
				}, {
					name: '创意文创',
				}, {
					name: '门票船票'
				}, {
					name: '餐饮美食'
				}, {
					name: '酒店民宿'
				}],
				lineBg: 'data:image/png;base64,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',
				// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
				dataList: []
			}
		},
	computed: {
		...mapState(["userInfo", "allImgList"]),
			maxScroll() {
				const totalWidth = this.navList.length * 140; // 每个项目140rpx
				const visibleWidth = 700; // 可见区域宽度
				return totalWidth - visibleWidth;
			}
	},
	methods: {
		onSearch(e) {
			console.log(e)
			},
			onNavClick(item) {
				console.log('点击了导航项：', allImgList)
			},
			clickIndicator(index) {
				this.current = index;
			},
			onScroll(e) {
				const {
					scrollLeft
				} = e.detail;
				// 计算指示器位置
				// 30rpx是指示器总宽度，10rpx是指示器滑块宽度
				const maxIndicatorMove = 30; // 指示器可移动的最大距离（rpx）
				const progress = scrollLeft / this.maxScroll; // 滚动进度 (0-1)
				this.indicatorLeft = maxIndicatorMove * progress;
			},
			// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用this.$refs.paging.reload()即可
			queryList(pageNo, pageSize) {
				let res = [{
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}]
				// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				this.$refs.paging.complete(res);
				// 此处请求仅为演示，请替换为自己项目中的请求
				//   this.$request.queryList({ pageNo,pageSize }).then(res => {
				// // 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				//   	this.$refs.paging.complete(res.data.list);
				//   }).catch(res => {
				//   	// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
				//   	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
				//   	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
				//   	this.$refs.paging.complete(false);
				//   })
			},
			// 导航菜单
			headelList(item) {
				if (item.name === '我的收藏') {
					uni.navigateTo({
					  url: 'pages/shopping/collect/index'
					})
				} else if (item.name === '优惠券') {
					uni.navigateTo({
					  url: 'pages/shopping/coupon/index'
					})
				} else if (item.name === '积分购物') {
					uni.navigateTo({
					  url: 'pages/shopping/points/index'
					})
				}
		},
		headelMerchant(item) {
			uni.navigateTo({
			  url: 'pages/shopping/qualityMerchant/index'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.shopping-container {
	width: 100%;
    min-height: 100vh;
    background-color: #f5f5f5;
	position: relative;
}

.online-service {
    position: fixed;
    top: 50%;
    right: -36rpx;
    transform: translate(-50%);
    z-index: 9999;
    width: 70rpx;
    height: 254rpx;
}

.shopping-bgimg {
	width: 100%;
    position: absolute;
    top: 0;
    left: 0;
	z-index: 0;
}

.shopping-box {
    position: relative;
    padding-top: 190rpx;
}

.main-box {
    position: relative;
	width: 100%;
    padding-top: 200rpx;
    z-index: 1;
}

.search-box {
	display: flex;
	align-items: center;
    justify-content: center;
}

.shopping-search-bgimg {
    width: 31rpx;
    height: 31rpx;
    margin: 13rpx;
}

.shopping-cat-bgimg {
    width: 78rpx;
    height: 78rpx;
    margin: 0 30rpx 0 18rpx;
}

.search-btn {
	border-radius: 50rpx;
	margin: 0;
    height: 60rpx;
    line-height: 60rpx;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 24rpx;
    color: #FFFFFF;
}

.swiper {
    padding: 30rpx;
}

.swiper :deep(.u-swiper) {
    border-radius: 16rpx;
    overflow: hidden;
}

.swiper :deep(.u-swiper__image) {
    height: 300rpx;
    border-radius: 16rpx;
}

.swiper .indicator {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.swiper .indicator__dot {
    width: 8rpx;
    height: 8rpx;
    border-radius: 4rpx;
    background: red;
    margin: 0 6rpx;
    transition: all 0.3s;
    cursor: pointer;
}

.swiper .indicator__dot--active {
    width: 40rpx;
    background: blue;
}

.nav-menu {
    margin: 0 30rpx 30rpx 30rpx;
    border-radius: 16rpx;
    // padding: 30rpx 0 20rpx 0;
    overflow: visible;
}

.nav-menu .nav-scroll {
    width: 100%;
    margin: 0 auto;
    overflow: visible;
}

.nav-menu .nav-content {
    display: flex;
    width: 100%;
    overflow: visible;
    padding: 0;
}

.nav-menu .nav-item {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
    position: relative;
    flex-shrink: 0;
    overflow: visible;
}

.nav-menu .nav-item image {
    width: 62rpx;
    height: 62rpx;
}

.nav-menu .nav-item text {
    font-size: 24rpx;
    color: #333;
    width: 100%;
    text-align: center;
    padding: 0 10rpx;
    box-sizing: border-box;
}

.nav-menu .nav-item .badge {
    position: absolute;
    top: 0rpx;
    right: 30rpx;
    background: #fff;
    color: #D93D25;
    font-size: 20rpx;
    border-radius: 50%;
    z-index: 1;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 2rpx solid #D93D25;
    min-width: 32rpx;
    width: max-content;
}

.nav-menu .nav-item .badge.square-badge {
    border-radius: 9rpx;
	right: -18rpx;
}

.nav-menu .nav-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
    position: relative;
    width: 60rpx;
    height: 6rpx;
    background: #fff;
    border-radius: 3rpx;
    margin: 20rpx auto 0;
    overflow: hidden;
}

.nav-menu .nav-indicator::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 30rpx;
    height: 100%;
    background: #D93D25;
    border-radius: 3rpx;
    transition: transform 0.3s;
    transform: translateX(var(--indicator-left));
}

.new-user-gift {
    margin: 30rpx;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
}

.new-user-gift .gift-banner {
    width: 100%;
    height: 165rpx !important;
}

.section-container {
    margin: 20rpx 30rpx;
    display: flex;
    gap: 20rpx;
}

.section-container .flash-sale,
.section-container .quality-merchant {
    flex: 1;
    border-radius: 16rpx;
    padding: 2rpx;
    box-sizing: border-box;
}

.section-container .flash-sale .section-title,
.section-container .quality-merchant .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 17rpx;
}

.section-container .flash-sale .section-title .flash-sale-image,
.section-container .quality-merchant .section-title .flash-sale-image {
    width: 117rpx;
    height: 29rpx;
}

.section-container .flash-sale .section-title .countdown,
.section-container .quality-merchant .section-title .countdown {
    display: flex;
    align-items: center;
    border-radius: 20rpx;
    padding: 0 13rpx;
    box-sizing: border-box;
    position: relative;
    width: 165rpx;
    height: 30rpx;
    line-height: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
}

.section-container .flash-sale .section-title .countdown .end-text,
.section-container .quality-merchant .section-title .countdown .end-text {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16rpx;
    color: #FFFFFF;
    position: relative;
    z-index: 1;
}

.section-container .flash-sale .section-title .countdown .time,
.section-container .quality-merchant .section-title .countdown .time {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 18rpx;
    color: #D93D25;
    position: relative;
    z-index: 2;
}

.section-container .flash-sale .section-title .countdown .end-bg,
.section-container .quality-merchant .section-title .countdown .end-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 80rpx;
    height: 30rpx;
    z-index: 0;
}

.section-container .flash-sale {
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.section-container .flash-sale .product-list {
    display: flex;
    gap: 20rpx;
    flex: 1;
}

.section-container .flash-sale .product-list .product-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.section-container .flash-sale .product-list .product-item .product-image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
}

.section-container .flash-sale .product-list .product-item .price {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    width: 69rpx;
    height: 38rpx;
    background: #FFECEA;
    border-radius: 19rpx;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 20rpx;
    color: #D93D25;
    line-height: 34rpx;
}

.section-container .flash-sale-bg {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #FCE3E1, #FFFFFF);
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding: 16rpx 20rpx;
    box-sizing: border-box;
}

.section-container .quality-merchant {
    display: flex;
    flex-direction: column;
    padding: 2rpx;
    box-sizing: border-box;
    background-color: #fff;
}

.section-container .quality-merchant .merchant-list {
    display: flex;
    gap: 20rpx;
    flex: 1;
    border-radius: 9rpx 9rpx 0 0;
}

.section-container .quality-merchant .merchant-list .merchant-item {
    flex: 1;
    height: 146rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    background: #F7F7F7;
    border-radius: 9rpx;
    position: relative;
    height: 146rpx;
}

.section-container .quality-merchant .merchant-list .merchant-item .merchant-image {
    width: 100%;
    height: 73rpx;
    background-color: #e5e5e5;
}

.section-container .quality-merchant .merchant-list .merchant-item .avatar-image {
    position: absolute;
    top: 50rpx;
    left: 50%;
    width: 46rpx;
    height: 46rpx;
    border-radius: 50%;
    background-color: yellow;
    transform: translate(-50%);
}

.section-container .quality-merchant .merchant-list .merchant-item .merchant-info {
    display: flex;
    flex-direction: column;
}

.section-container .quality-merchant .merchant-list .merchant-item .merchant-info .name {
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 16rpx;
    color: #2F2F2F;
    text-align: center;
}

.section-container .quality-merchant .merchant-list .merchant-item .merchant-info .coupon {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16rpx;
    color: #D93D25;
    text-align: center;
}

.section-container .quality-merchant-bg {
    background: linear-gradient(180deg, #FEECD1, #FFFFFF);
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding: 16rpx 20rpx;
    box-sizing: border-box;
}

.paging-box {
    display: flex;
    flex-wrap: wrap;
    padding: 4rpx 21rpx 21rpx 21rpx;
}

.paging-box .paging-item {
    width: calc(100% / 2);
    padding: 16rpx;
    box-sizing: border-box;
}

.paging-box .paging-image {
    height: 340rpx;
    border-radius: 20rpx;
}

.paging-box .title {
	padding-top: 20rpx;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 28rpx;
    color: #1E1412;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
}

.paging-box .price-box {
    display: flex;
    align-items: center;
	padding: 10rpx 0;
	box-sizing: border-box;
}

.paging-box .price-box .unit {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 22rpx;
    color: #D93D25;
}

.paging-box .price-box .price {
   font-family: Source Han Sans CN;
   font-weight: bold;
   font-size: 30rpx;
   color: #D93D25;
}

.paging-box .price-box .sold {
	padding-left: 14rpx;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 22rpx;
    color: #999999;
}

.paging-box .discount-information {
    display: flex;
    align-items: center;
    font-size: 22rpx;
    color: #E8603E;
	padding-bottom: 20rpx;
}

.paging-box .discount-information .deduction-item {
	background: #FFFFFF;
	border-radius: 6rpx;
	border: 1px solid #E85D3A;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 20rpx;
    color: #E85D3A;
	padding: 0 10rpx;
}

.paging-box .discount-information .no-reason {
    margin-left: 10rpx;
}

:deep(.u-tabs) {
    margin: 0 15rpx;
}
</style>