<template>
	<z-paging ref="paging" v-model="dataList" @query="queryList">
		<template slot="top">
			<view class="search-bar-container" :style="{ marginTop: searchBarTop + 'px', height: searchBarHeight + 'px', marginRight: searchBarRight + 'px' }">
				<view hover-class="menu-item-hover" class="nav-back" @click="rightClick">
					<u-icon name="arrow-left" color="#7A7A7A" size="20"></u-icon>
				</view>
				<view class="search-input-wrap">
					<u-input 
						v-model="name" 
						prefixIcon="search" 
						prefixIconStyle="font-size: 22px; color: #999999;" 
						placeholder-style="color: #999999; font-size: 28rpx;"
						class="search-input" 
						confirm-type="search"
						@confirm="onSearch" 
						shape="circle" 
						placeholder="请输入商家名称" />
				</view>
			</view> 
		</template>
		<view class="quality-merchant-container">
			<view v-for="(item,index) in dataList" :key="index" @click="headelDetail(item)" class="quality-merchant-item">
				<view class="box-container">
					<image class="image" src="@/static/images/shopping/dp.png" mode="aspectFill"></image>
					<view class="quality-merchant-left">
						<view class="title">大理10年老店阿凤嫂土鸡风味米线</view>
						<view class="score">
							<view class="score-left">
								<u-rate active-color="#FF6C05" inactive-color="#E5E5E5" gutter="4" :allowHalf="true"  :value="scoreValue" ></u-rate>
								<text>{{ scoreValue }}</text>
							</view>
							<view class="discount">
								28减3 
								<u-line color="#E85D3A" direction="column" :hairline="false" length="5" margin="0 4rpx"></u-line>
								68减10
							</view>
						</view>
						<view class="quality-item-box">
							<view v-for="(item, index) in item.list" class="quality-item">
								<image class="quality-image" src="@/static/images/shopping/cp.png" mode="aspectFill"></image>
								<view class="quality-title">
									{{ item.title }}
								</view>
								<view class="quality-num">
									<text>¥</text>
									{{ item.num }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="positioning">
					<view class="positioning-left">
						<u-icon name="map" color="#6F7896" size="14"></u-icon>
						<view class="title">云南省大理市金梭岛主街道980号</view>
					</view>
					<u-icon name="arrow-right" color="#6F7896" size="14"></u-icon>
				</view>
			</view>
		</view>
	</z-paging>
</template>

<script>
	import { mapState } from "vuex";
	import { onLoad } from '@dcloudio/uni-app'
	export default { 
		data() {
			return {
				searchBarTop: 0,
				searchBarHeight: 0,
				searchBarRight: 0,
				name: '',
				dataList: [],
				scoreValue: 3.7,
			}
		},
		computed: {
			...mapState(["userInfo", "allImgList"]),
		},
		onLoad: function() {
			// #ifdef MP
			// 仅在小程序端执行
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.searchBarRight = menuButtonInfo.width;
			// #endif
		
			// #ifndef MP
			const systemInfo = uni.getSystemInfoSync();
			this.searchBarHeight = systemInfo.navigationBarHeight || 40; // 使用系统导航栏高度
			this.searchBarTop = systemInfo.statusBarHeight || 15; // 直接使用状态栏高度
			this.searchBarRight = 0
			// #endif
		},
		
		methods: {
			// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用this.$refs.paging.reload()即可
			queryList(pageNo, pageSize) {
				let res = [{
					title: 85558,
					list: [{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					}]
				}, {
					title: 85558,
					list: [{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					}]
				}, {
					title: 85558,
					list: [{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					},{
						title: '秘制土鸡米线',
						num: '2.90'
					}]
				}]
				// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				this.$refs.paging.complete(res);
				// 此处请求仅为演示，请替换为自己项目中的请求
				//   this.$request.queryList({ pageNo,pageSize }).then(res => {
				// // 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				//   	this.$refs.paging.complete(res.data.list);
				//   }).catch(res => {
				//   	// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
				//   	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
				//   	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
				//   	this.$refs.paging.complete(false);
				//   })
			},
			rightClick() {
				uni.navigateBack()
			},
			headelDetail() {
				uni.navigateTo({
				  url: 'pages/shopping/qualityMerchant/details'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.search-bar-container {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		width: 100%;
	}
	
	.search-input {
		background-color: #EEEEEE;
		border: 0;
	}
	
	.search-input-wrap {
		margin-left: 40rpx;
		flex: 1
	}
	
	.quality-merchant-container {
		padding: 30rpx;
		box-sizing: border-box;
	}
	
	.quality-merchant-item {
		padding: 25rpx;
		box-sizing: border-box;
		background: #FFFFFF;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}
	
	
	.quality-merchant-item:first-child {
		margin-top: 0;
	}
	
	.box-container {
		display: flex;
	}
	
	.quality-merchant-left {
		flex: 1;
		overflow: hidden;
		margin-left: 30rpx;
	}
	
	.title {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 28rpx;
		color: #1E1412;
		overflow:hidden;
		text-overflow:ellipsis;
		white-space:nowrap;
	}
	
	.image {
		width: 190rpx;
		height: 190rpx;
		border-radius: 8rpx;
	}
	
	.score {
		display: flex;
		align-items: center;
		color: #FF6C05;
		margin-left: 5rpx;
		padding: 17rpx 0;
	}
	
	.score-left {
		display: flex;
		align-items: center;
	}
	
	.score text {
		padding-left: 5rpx;
	}
	
	.discount {
		background: #FFFFFF;
		border-radius: 6rpx;
		border: 1px solid #E85D3A;
		margin-left: 15rpx;
		padding: 0 8rpx;
		box-sizing: border-box;
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 20rpx;
		color: #E85D3A;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.quality-item-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.quality-image {
		width: 126rpx;
		height: 95rpx;
		border-radius: 8rpx;
	}
	
	.quality-title {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 20rpx;
		color: #1A1A1A;
	}
	
	.quality-num text {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 22rpx;
		color: #F03030;
		padding-right: 4rpx;
	}
	
	.quality-num {
		font-family: Source Han Sans CN;
		font-weight: bold;
		font-size: 24rpx;
		color: #F03030;
	}
	
	.positioning {
		background: #F0F2F8;
		border-radius: 8rpx;
		padding: 17rpx;
		box-sizing: border-box;
		margin-top: 17rpx;
		
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 22rpx;
		color: #6F7896;
		
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.positioning-left {
		display: flex;
		align-items: center;
		flex: 1;
		overflow: hidden;
	}
	
	.positioning-left .title {
		flex: 1;
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 22rpx;
		color: #6F7896;
		padding: 0 5rpx;
		overflow:hidden;
		text-overflow:ellipsis;
		white-space:nowrap;
	}
	
	.quality-positioning {
		width: 22rpx;
		height: 26rpx;
		margin-right: 16rpx;
	}
</style>