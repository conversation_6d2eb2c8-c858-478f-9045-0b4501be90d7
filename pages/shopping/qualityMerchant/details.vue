<template>
	<view class="detail-container">
		<view class="headel-container">
			<u-navbar title="" @rightClick="rightClick" :autoBack="true" bgColor="transparent" :customStyle="{zIndex: 100}">
				<view class="u-nav-slot" slot="left">
					<image class="return-left" :src="allImgList.returnLeft || ''" mode="aspectFill"> </image>
				</view>
			</u-navbar>
			<image class="details-bg" src="@/static/images/shopping/dp.png" mode="aspectFill"></image>
			<image class="online-service" :src="allImgList.onlineService || ''" mode="aspectFill"> </image>
		</view>
		<view class="main-box">
			<view class="shop-headel">
				<image class="headel-bg" src="@/static/images/shopping/dp.png" mode="aspectFill"></image>
				<view class="headel-left">
					<view class="headel-title">大理10年老店阿凤嫂土鸡风味米线</view>
					<view class="score">
						<u-rate active-color="#FF6C05" inactive-color="#E5E5E5" gutter="4" :allowHalf="true"  :value="scoreValue" ></u-rate>
						<text>{{ scoreValue }}</text>
					</view>
					<view class="discount">
						<view class="discount-item">￥3满28可用</view>
						<view class="discount-item">￥5满50可用</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 商户地址 -->
		<view class="merchant-address" v-if="merchantAddress">
			<view class="merchant-address-text">
				<text>{{ merchantAddress || "" }}</text>
			</view>
			<view class="merchant-address-nav" @tap="clickMap">
				<image class="location-icon" src="@/static/images/playIcons/location.png" mode="scaleToFill" />
				<view class="location-text">地图导航</view>
			</view>
		</view>
		<u-sticky offset-top="0" customNavHeight="0" bgColor="#fff">
			<u-tabs @change="tabChange"
				:list="list"
				lineWidth="20"
				lineHeight="7"
				:customNavHeight="0"
				:lineColor="`url(${lineBg}) 100% 100%`"
				:activeStyle="{ color: '#303133', fontWeight: 'bold', transform: 'scale(1.05)', fontSize: '32rpx' }"
				:inactiveStyle="{ color: '#606266', transform: 'scale(1)', fontSize: '28rpx' }"
				style="margin: 0 10rpx;">
			</u-tabs>
		</u-sticky>
		<z-paging v-if="currentCategoryIndex === 0" 
			:use-page-scroll="true"
			ref="paging" 
			v-model="dataList" 
			@query="queryList"
			:scrollable="false"
			:refresher-enabled="false"
			:auto-show-back-to-top="true">
			<!-- 商品 -->
			<view class="paging-box" >
				<view v-for="(item,index) in dataList" :key="index" class="paging-item">
					<image class="paging-image" src="@/static/images/shopping/sy.png" mode="aspectFill"></image>
					<view class="title">普洱三道茶普洱三道茶</view>
					<view class="price-box">
						<text class="unit">￥</text>
						<text class="price">150.90</text>
						<text class="sold">已售 19</text>
					</view>
					<view class="discount-information">
						<view class="deduction deduction-item">
							积分最高抵扣￥15
						</view>
						<view class="no-reason deduction-item">
							7天无理由
						</view>
					</view>
				</view>
			</view>
		</z-paging>
		<!-- 相册 -->
		<view v-if="currentCategoryIndex === 1">
			<album :height="200" @scrollToCategory="handleScrollToCategory"></album>
		</view>
		<!-- 简介 -->
		<view class="brief-introduction" v-if="currentCategoryIndex === 2">
			欢迎光临“味遇记”——一家融合传统与创新，致力于为您带来味蕾盛宴的餐饮店铺。在这里，每一道菜都是对美食艺术的精心雕琢，每一口都是对生活热爱的深情表达。
			欢迎光临“味遇记”——一家融合传统与创新，致力于为您带来味蕾盛宴的餐饮店铺。在这里，每一道菜都是对美食艺术的精心雕琢，每一口都是对生活热爱的深情表达。
			欢迎光临“味遇记”——一家融合传统与创新，致力于为您带来味蕾盛宴的餐饮店铺。在这里，每一道菜都是对美食艺术的精心雕琢，每一口都是对生活热爱的深情表达。
			欢迎光临“味遇记”——一家融合传统与创新，致力于为您带来味蕾盛宴的餐饮店铺。在这里，每一道菜都是对美食艺术的精心雕琢，每一口都是对生活热爱的深情表达。
		</view>
	</view>
</template>

<script>
	import { mapState } from "vuex";
	import album from '@/pages/shopping/qualityMerchant/components/album.vue'
	export default {
		data() {
			return {
				// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
				dataList: [],
				scoreValue: 3.7,
				merchantAddress: "金梭岛风景区位于大理市海东乡西南", // 商户地址
				scrollTop: 0,
				
				list: [{
					name: '商品'
				}, {
					name: '相册',
				}, {
					name: '简介'
				}],
				lineBg: 'data:image/png;base64,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',
				currentCategoryIndex: 0,
			}
		},
		components: {
			album
		},
		computed: {
			...mapState(["userInfo", "allImgList"]),
		},
		methods: {
			// 点击地图导航
			clickMap() {
				// 获取商户经纬度信息
				const latitude = this.$store.state.merchantDetail.latitude;
				const longitude = this.$store.state.merchantDetail.longitude;
			
				// 如果有经纬度信息
				if (latitude && longitude) {
					// 准备导航参数
					const name = encodeURIComponent(this.merchantName);
					const address = encodeURIComponent(this.merchantAddress);
					const lat = parseFloat(latitude);
					const lng = parseFloat(longitude);
			
					// #ifdef H5
					// H5环境下跳转到地图页面
					uni.$u.route({
						url: "pages/merchant/map",
						params: {
							latitude: lat,
							longitude: lng,
							name: this.merchantName,
							address: this.merchantAddress,
						},
					});
					return;
					// #endif
			
					// #ifdef MP-WEIXIN
					// 微信小程序环境下，使用微信内置地图
					uni.openLocation({
						latitude: lat,
						longitude: lng,
						name: this.merchantName,
						address: this.merchantAddress,
						success: () => {
							console.log("打开微信地图成功");
						},
						fail: (err) => {
							console.error("打开微信地图失败", err);
							uni.showToast({
								title: "打开地图失败",
								icon: "none",
							});
						},
					});
					return;
					// #endif
				} else {
					uni.$u.toast("暂无经纬度信息");
				}
			},
			// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用this.$refs.paging.reload()即可
			queryList(pageNo, pageSize) {
				let res = [{
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}]
				// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				this.$refs.paging.complete(res);
				// 此处请求仅为演示，请替换为自己项目中的请求
				//   this.$request.queryList({ pageNo,pageSize }).then(res => {
				// // 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				//   	this.$refs.paging.complete(res.data.list);
				//   }).catch(res => {
				//   	// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
				//   	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
				//   	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
				//   	this.$refs.paging.complete(false);
				//   })
			},
			
			// 处理相册组件的滚动事件
			handleScrollToCategory(scrollTop) {
				this.scrollTop = scrollTop;
			},
			
			// u-tabs切换事件
			tabChange(e) {
				const index = e.index;
				this.currentCategoryIndex = index;
			},
		}
	}
</script>

<style scoped lang="scss">
	.detail-container {
		width: 100%;
		height: 100vh;
		background-color: #fff;
	}
	
	.online-service {
	    position: fixed;
	    top: 50%;
	    right: -36rpx;
	    transform: translate(-50%);
	    z-index: 99;
	    width: 70rpx;
	    height: 254rpx;
	}
	
	.details-bg {
		width: 100%;
		height: 360rpx;
		position: relative;
		z-index: 1;
		top: 0;
		left: 0;
	}
	
	.return-left {
		width: 66rpx;
		height: 66rpx;
	}
	
	.u-nav-slot {
		position: relative;
		z-index: 101;
	}
	
	.main-box {
		position: relative;
		margin-top: -20rpx;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		z-index: 2;
		padding: 30rpx;
		box-sizing: border-box;
	}
	
	.shop-headel {
		display: flex;
	}
	
	.headel-left {
		flex: 1;
		overflow: hidden;
	}
	
	.headel-bg {
		width: 140rpx;
		height: 140rpx;
		background: #D93D25;
		border-radius: 10rpx;
		margin-right: 26rpx;
	}
	
	.headel-title {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 32rpx;
		color: #1E1412;
		
		overflow:hidden;
		text-overflow:ellipsis;
		white-space:nowrap;
	}
	
	.score {
		display: flex;
		align-items: center;
		padding: 4rpx 0 14rpx 0;
		box-sizing: border-box;
	}
	
	.score text {
		font-family: Source Han Sans CN;
		font-weight: bold;
		font-size: 26rpx;
		color: #FF6C05;
		padding-left: 10rpx;
	}
	
	.discount {
		display: flex;
	}
	
	.discount-item {
		background: #D93D25;
		border-radius: 6rpx;
		height: 30rpx;
		line-height: 30rpx;
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 20rpx;
		color: #FFFFFF;
		margin-left: 16rpx;
		padding: 0 10rpx;
		box-sizing: border-box;
	}
	
	.discount-item:first-child {
		margin-left: 0;
	}
	
	.merchant-address {
		width: 100%;
		height: 130rpx;
		background-image: url("@/static/images/playIcons/mapbg.png");
		background-size: 100% 100%;
		background-repeat: no-repeat;
		display: flex;
		align-items: center;
		padding: 0 22rpx;
	}
	
	.merchant-address-text {
		font-weight: 400;
		font-size: 30rpx;
		color: #535961;
		flex: 1;
	}
	
	.merchant-address-nav {
		width: 88rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
	}
	
	.location-icon {
		width: 52rpx;
		height: 52rpx;
		margin-bottom: 10rpx;
	}
	
	.location-text {
		font-weight: 400;
		font-size: 22rpx;
		color: #888f97;
	}
	
	.paging-box {
	    display: flex;
	    flex-wrap: wrap;
	    padding: 4rpx 21rpx 21rpx 21rpx;
	}
	
	.paging-box .paging-item {
	    width: calc(100% / 2);
	    padding: 16rpx;
	    box-sizing: border-box;
	}
	
	.paging-box .paging-image {
	    height: 340rpx;
	    border-radius: 20rpx;
	}
	
	.paging-box .title {
		padding-top: 20rpx;
	    font-family: Source Han Sans CN;
	    font-weight: 500;
	    font-size: 28rpx;
	    color: #1E1412;
		overflow:hidden;
		text-overflow:ellipsis;
		white-space:nowrap;
	}
	
	.paging-box .price-box {
	    display: flex;
	    align-items: center;
		padding: 10rpx 0;
		box-sizing: border-box;
	}
	
	.paging-box .price-box .unit {
	    font-family: Source Han Sans CN;
	    font-weight: 500;
	    font-size: 22rpx;
	    color: #D93D25;
	}
	
	.paging-box .price-box .price {
	   font-family: Source Han Sans CN;
	   font-weight: bold;
	   font-size: 30rpx;
	   color: #D93D25;
	}
	
	.paging-box .price-box .sold {
		padding-left: 14rpx;
	    font-family: Source Han Sans CN;
	    font-weight: 400;
	    font-size: 22rpx;
	    color: #999999;
	}
	
	.paging-box .discount-information {
	    display: flex;
	    align-items: center;
	    font-size: 22rpx;
	    color: #E8603E;
		padding-bottom: 20rpx;
	}
	
	.paging-box .discount-information .deduction-item {
		background: #FFFFFF;
		border-radius: 6rpx;
		border: 1px solid #E85D3A;
	    font-family: Source Han Sans CN;
	    font-weight: 400;
	    font-size: 20rpx;
	    color: #E85D3A;
		padding: 0 10rpx;
	}
	
	.paging-box .discount-information .no-reason {
	    margin-left: 10rpx;
	}
	.album-scroll {
		height: calc(100vh - 500rpx);
		background-color: #fff;
	}
	
	.brief-introduction {
		padding: 0 30rpx;
		box-sizing: border-box;
		font-family: Source Han Sans CN;
		font-weight: 400;
		font-size: 28rpx;
		color: #4D5257;
		line-height: 40rpx;
		letter-spacing: 2rpx;
		text-indent: 2em;
	}
</style>