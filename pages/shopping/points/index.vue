<template>
	<view class="points-container">
		<view class="headel-container">
			<image class="online-service" :src="allImgList.totalRedemption || ''" mode="aspectFill"> </image>
			<view class="search-bar-container" :style="{ top: searchBarTop + 'px', height: searchBarHeight + 'px', marginRight: searchBarRight + 'px' }">
				<view hover-class="menu-item-hover" class="nav-back" @click="rightClick">
					<u-icon name="arrow-left" color="#FFFFFF" size="20"></u-icon>
				</view>
				<view class="search-input-wrap">
					<u-input 
						v-model="name" 
						prefixIcon="search" 
						prefixIconStyle="font-size: 22px; color: #D93D25;" 
						placeholder-style="color: #797979; font-size: 28rpx;"
						class="search-input" 
						confirm-type="search"
						@confirm="onSearch" 
						shape="circle" 
						placeholder="请输入商品名称" />
				</view>
			</view>
			<view class="info-container">
				<view class="avatar-box" @click="myPoints">
					 <image class="avatar" src="@/static/images/shopping/profile-picture.png" mode="aspectFill"> </image>
					 <view class="user-info">
						<text>游客李三</text>
						<view class="user-info-right">
							<image class="gold-coin" src="@/static/images/shopping/gold-coin.png" mode="aspectFill"> </image>
							<text>可用积分 999</text>
						</view>
					 </view>
				</view>
				<u-button text="赚积分" color="#fff" @click="confirmSelect" class="confirm-btn" :plain="true"></u-button>
			</view>
		</view>
		<view class="main-box">
			<z-paging
				:use-page-scroll="true"
				ref="paging" 
				v-model="dataList" 
				@query="queryList"
				:scrollable="false"
				:refresher-enabled="false"
				:auto-show-back-to-top="true">
				<!-- 商品 -->
				<view class="paging-box" >
					<view v-for="(item,index) in dataList" :key="index" class="paging-item">
						<image class="paging-image" src="@/static/images/shopping/sy.png" mode="aspectFill"></image>
						<view class="title">普洱三道茶普洱三道茶</view>
						<view class="price-box">
							<text class="unit">￥</text>
							<text class="price">150.90</text>
							<text class="plusgood">+</text>
							<text class="points-num">90</text>
							<text class="points-unit">积分</text>
							<text class="sold">已售 19</text>
						</view>
						<view class="discount-information">
							<view class="deduction deduction-item">
								积分最高抵扣￥15
							</view>
							<view class="no-reason deduction-item">
								7天无理由
							</view>
						</view>
					</view>
				</view>
			</z-paging>
		</view>
	</view>
</template>

<script>
	import { mapState } from "vuex";
	import { onLoad } from '@dcloudio/uni-app'
	export default {
		data() {
			return {
				searchBarTop: 0,
				searchBarHeight: 0,
				searchBarRight: 0,
				name: '',
				// v-model绑定的这个变量不要在分页请求结束中自己赋值，直接使用即可
				dataList: [],
			}
		},
		computed: {
			...mapState(["userInfo", "allImgList"]),
		},
		onLoad: function() {
			// #ifdef MP
			// 仅在小程序端执行
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.searchBarRight = menuButtonInfo.width;
			// #endif
		
			// #ifndef MP
			const systemInfo = uni.getSystemInfoSync();
			this.searchBarHeight = systemInfo.navigationBarHeight || 40; // 使用系统导航栏高度
			this.searchBarTop = systemInfo.statusBarHeight || 15; // 直接使用状态栏高度
			this.searchBarRight = 0
			// #endif
		},
		methods: {
			
			rightClick() {
				uni.navigateBack()
			},

			onSearch(e) {
				console.log('onSearch', e)
			},
			
			// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用this.$refs.paging.reload()即可
			queryList(pageNo, pageSize) {
				let res = [{
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}, {
					title: 85558
				}]
				// 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				this.$refs.paging.complete(res);
				// 此处请求仅为演示，请替换为自己项目中的请求
				//   this.$request.queryList({ pageNo,pageSize }).then(res => {
				// // 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
				//   	this.$refs.paging.complete(res.data.list);
				//   }).catch(res => {
				//   	// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
				//   	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
				//   	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
				//   	this.$refs.paging.complete(false);
				//   })
			},
			
			// 我的积分
			myPoints() {
				uni.navigateTo({
				  url: 'pages/shopping/points/info'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.points-container {
		width: 100%;
		height: 100vh;
		background-color: #fff;
	}
	
	.headel-container {
		position: relative;
		top: 0;
		left: 0;
		width: 100%;
		height: 360rpx;
	}
	
	.online-service {
	   width: 100%;
	   height: 360rpx;
	   position: absolute;
	   top: 0;
	   left: 0;
	   z-index: 0;
	}
	
	.search-bar-container {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		width: 100%;
		z-index: 1;
	}
	
	.search-input {
		background-color: #FFFFFF;
		border: 0;
	}
	
	.search-input-wrap {
		margin-left: 40rpx;
		flex: 1
	}
	
	.confirm-btn {
		width: 180rpx;
		height: 70rpx;
		border-radius: 35rpx;
		margin: 0;
		background-color: #FF6C05;
	}
	
	.info-container {
		width: 100%;
		position: absolute;
		padding: 0 30rpx;
		box-sizing: border-box;
		left: 0;
		bottom: 94rpx;
		z-index: 99;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.avatar-box {
		display: flex;
	}
	
	.avatar {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
	}
	
	.user-info {
		margin-left: 16rpx;
	}
	
	.user-info-right {
		display: flex;
		align-items: center;
		height: 38rpx;
		line-height: 38rpx;
		background: #FFDDB1;
		border-radius: 19rpx;
		
		padding: 0 10rpx;
		box-sizing: border-box;
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 22rpx;
		color: #C66A44;
		margin-top: 5rpx;
	}
	
	.user-info-right text {
		display: inline-block;
	}
	
	.gold-coin {
		width: 26rpx;
		height: 26rpx;
		margin-right: 10rpx;
	}
	
	.main-box {
		position: relative;
		margin-top: -40px;
		background-color: #fff;
		border-radius: 10px 10px 0 0;
		z-index: 2;
		box-sizing: border-box;
	}
	
	.paging-box {
	    display: flex;
	    flex-wrap: wrap;
		gap: 20rpx;
	    padding: 30rpx;
	}
	
	.paging-box .paging-item {
	    box-sizing: border-box;
		width: calc(50% - 10rpx);
	}
	
	.paging-box .paging-image {
	    height: 340rpx;
	    border-radius: 20rpx;
	}
	
	.paging-box .title {
		padding-top: 20rpx;
	    font-family: Source Han Sans CN;
	    font-weight: 500;
	    font-size: 28rpx;
	    color: #1E1412;
		overflow:hidden;
		text-overflow:ellipsis;
		white-space:nowrap;
	}
	
	.paging-box .price-box {
	    display: flex;
	    align-items: center;
		padding: 10rpx 0;
		box-sizing: border-box;
	}
	
	.paging-box .price-box .unit {
	    font-family: Source Han Sans CN;
	    font-weight: 500;
	    font-size: 22rpx;
	    color: #D93D25;
	}
	
	.paging-box .price-box .price {
	   font-family: Source Han Sans CN;
	   font-weight: bold;
	   font-size: 30rpx;
	   color: #D93D25;
	}
	
	.paging-box .price-box .sold {
		padding-left: 14rpx;
	    font-family: Source Han Sans CN;
	    font-weight: 400;
	    font-size: 22rpx;
	    color: #999999;
	}
	
	.plusgood {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 22rpx;
		color: #D93D25;
		padding-left: 10rpx;
	}
	
	.points-num {
		font-family: Source Han Sans CN;
		font-weight: bold;
		font-size: 30rpx;
		color: #D93D25;
		padding-left: 10rpx;
	}
	
	.points-unit {
		font-family: Source Han Sans CN;
		font-weight: 500;
		font-size: 22rpx;
		color: #D93D25;
		padding-left: 10rpx;
	}
	
	.paging-box .discount-information {
	    display: flex;
	    align-items: center;
	    font-size: 22rpx;
	    color: #E8603E;
		padding-bottom: 20rpx;
	}
	
	.paging-box .discount-information .deduction-item {
		background: #FFFFFF;
		border-radius: 6rpx;
		border: 1px solid #E85D3A;
	    font-family: Source Han Sans CN;
	    font-weight: 400;
	    font-size: 20rpx;
	    color: #E85D3A;
		padding: 0 10rpx;
	}
	
	.paging-box .discount-information .no-reason {
	    margin-left: 10rpx;
	}
</style>