<template>
	<view class="hotel-rooms-page">
		<!-- 日期信息栏 -->
		<view class="date-info">
			<view class="date-item">
				<text class="date-label">入住</text>
				<text class="date-value">{{ checkInDate }}</text>
			</view>
			<view class="nights-info">{{ nightsCount }}晚</view>
			<view class="date-item">
				<text class="date-label">离店</text>
				<text class="date-value">{{ checkOutDate }}</text>
			</view>
		</view>

		<!-- 房型列表 -->
		<view class="room-list" v-if="roomList.length > 0">
			<view class="room-item" v-for="room in roomList" :key="room.roomName">
				<!-- 房型图片 -->
				<view class="room-image">
					<u-image
						:src="room.roomImages[0]"
						width="160rpx"
						height="120rpx"
						border-radius="8rpx"
						:fade="true"
					></u-image>
				</view>

				<!-- 房型信息 -->
				<view class="room-info">
					<view class="room-name">{{ room.roomName }}</view>
					<view class="room-details">
						<text class="room-area">{{ room.roomArea }}㎡</text>
						<text class="room-bed">{{ room.bedType }}</text>
						<text class="room-guests">最多{{ room.maxGuests }}人</text>
					</view>
					<view class="room-facilities">
						<text class="facility-tag" v-for="facility in room.facilities.slice(0, 3)" :key="facility">
							{{ facility }}
						</text>
						<text class="more-facilities" v-if="room.facilities.length > 3">
							+{{ room.facilities.length - 3 }}
						</text>
					</view>

					<!-- 价格明细 -->
					<view class="price-details" v-if="showPriceDetails[room.roomName] && room.priceInfo && room.priceInfo.priceDetails">
						<view class="price-detail-item" v-for="detail in room.priceInfo.priceDetails" :key="detail.date">
							<text class="detail-date">{{ detail.date || '' }}</text>
							<text class="detail-type">{{ detail.priceTypeName || '' }}</text>
							<text class="detail-price">¥{{ detail.price || '0' }}</text>
						</view>
					</view>
				</view>

				<!-- 价格和预订 -->
				<view class="room-booking">
					<view class="price-section">
						<view class="total-price">
							<text class="price-symbol">¥</text>
							<text class="price-number">{{ (room.priceInfo && room.priceInfo.totalPrice) || '0' }}</text>
						</view>
						<view class="avg-price">平均¥{{ (room.priceInfo && room.priceInfo.avgPrice) || '0' }}/晚</view>
						<view class="price-toggle" @click="togglePriceDetails(room.roomName)">
							<text>价格明细</text>
							<u-icon
								:name="showPriceDetails[room.roomName] ? 'arrow-up' : 'arrow-down'"
								size="24"
							></u-icon>
						</view>
					</view>

					<view class="availability-section">
						<view class="remaining-rooms" v-if="room.availability && room.availability.available">
							仅剩{{ room.availability.remainingRooms || 0 }}间
						</view>
						<view class="unavailable-reason" v-else-if="room.availability">
							{{ room.availability.unavailableReason || '暂不可预订' }}
						</view>
						<view class="unavailable-reason" v-else>
							暂不可预订
						</view>

						<u-button
							:type="(room.availability && room.availability.available) ? 'primary' : 'info'"
							:disabled="!(room.availability && room.availability.available)"
							@click="bookRoom(room)"
							size="small"
							class="book-btn"
						>
							{{ (room.availability && room.availability.available) ? '立即预订' : '已售罄' }}
						</u-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 无房型提示 -->
		<view class="no-rooms" v-else-if="!loading">
			<u-empty text="该日期暂无可预订房型" mode="data"></u-empty>
		</view>

		<!-- 加载中 -->
		<view class="loading" v-if="loading">
			<u-loading-icon></u-loading-icon>
			<text>加载中...</text>
		</view>
	</view>
</template>

<script>
import { getRoomList } from '@/nxTemp/apis/hotel.js'

export default {
	name: 'HotelRooms',
	data() {
		return {
			hotelId: null,
			checkInDate: '',
			checkOutDate: '',
			roomList: [],
			loading: false,
			showPriceDetails: {} // 控制价格明细显示状态
		}
	},

	computed: {
		// 入住晚数
		nightsCount() {
			if (this.checkInDate && this.checkOutDate) {
				const checkIn = new Date(this.checkInDate)
				const checkOut = new Date(this.checkOutDate)
				return Math.ceil((checkOut - checkIn) / (24 * 60 * 60 * 1000))
			}
			return 0
		}
	},

	onLoad(options) {
		if (options.hotelId && options.checkInDate && options.checkOutDate) {
			this.hotelId = options.hotelId
			this.checkInDate = options.checkInDate
			this.checkOutDate = options.checkOutDate
			this.loadRoomList()
		} else {
			this.$u.toast('参数错误')
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	},

	methods: {
		// 加载房型列表
		async loadRoomList() {
			try {
				this.loading = true

				// 确保日期格式正确
				const formatDate = (dateStr) => {
					if (!dateStr) return null
					// 如果已经是 YYYY-MM-DD 格式，直接返回
					if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
						return dateStr
					}
					// 否则尝试转换
					const date = new Date(dateStr)
					if (isNaN(date.getTime())) {
						console.error('无效的日期格式:', dateStr)
						return null
					}
					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					return `${year}-${month}-${day}`
				}

				const params = {
					hotelId: parseInt(this.hotelId),
					checkInDate: formatDate(this.checkInDate),
					checkOutDate: formatDate(this.checkOutDate)
				}

				console.log('请求参数:', params)

				const response = await getRoomList(params)

				console.log('房型列表API响应:', response)
				console.log('响应数据结构:', response.data)

				if (response.data.code === 200) {
					this.roomList = response.data.data || []

					console.log('解析后的房型列表:', this.roomList)

					// 检查每个房型的数据结构并进行数据修复
					this.roomList.forEach((room, index) => {
						console.log(`房型${index}数据:`, room)
						console.log(`房型${index}价格信息:`, room.priceInfo)

						// 确保priceInfo存在
						if (!room.priceInfo) {
							room.priceInfo = {
								totalPrice: 0,
								avgPrice: 0,
								nights: this.nightsCount,
								priceDetails: []
							}
							console.warn(`房型${index}缺少价格信息，已设置默认值`)
						}

						// 确保availability存在
						if (!room.availability) {
							room.availability = {
								available: false,
								remainingRooms: 0,
								unavailableReason: '暂不可预订'
							}
							console.warn(`房型${index}缺少可用性信息，已设置默认值`)
						}

						this.$set(this.showPriceDetails, room.roomName, false)
					})

					if (this.roomList.length === 0) {
						this.$u.toast('该日期范围内暂无可预订房型')
					}
				} else {
					console.error('API错误:', response.data)
					this.$u.toast(response.data.message || '获取房型列表失败')
				}
			} catch (error) {
				console.error('获取房型列表失败:', error)
				this.$u.toast('网络错误，请重试')
			} finally {
				this.loading = false
			}
		},

		// 切换价格明细显示
		togglePriceDetails(roomName) {
			this.$set(this.showPriceDetails, roomName, !this.showPriceDetails[roomName])
		},

		// 预订房间
		bookRoom(room) {
			console.log('预订房间数据:', room)

			if (!room.availability || !room.availability.available) {
				this.$u.toast('该房型暂不可预订')
				return
			}

			// 构建预订参数
			const bookingData = {
				hotelId: this.hotelId,
				roomName: room.roomName || '',
				checkInDate: this.checkInDate,
				checkOutDate: this.checkOutDate,
				nights: this.nightsCount,
				totalPrice: (room.priceInfo && room.priceInfo.totalPrice) || 0,
				avgPrice: (room.priceInfo && room.priceInfo.avgPrice) || 0,
				priceDetails: (room.priceInfo && room.priceInfo.priceDetails) || []
			}

			console.log('预订数据:', bookingData)

			// 跳转到预订确认页面
			uni.navigateTo({
				url: `/pages/shopping/hotel/booking?data=${encodeURIComponent(JSON.stringify(bookingData))}`
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.hotel-rooms-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.date-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	padding: 24rpx 32rpx;
	margin-bottom: 20rpx;

	.date-item {
		display: flex;
		flex-direction: column;
		align-items: center;

		.date-label {
			font-size: 24rpx;
			color: #999;
			margin-bottom: 8rpx;
		}

		.date-value {
			font-size: 28rpx;
			color: #333;
			font-weight: bold;
		}
	}

	.nights-info {
		font-size: 28rpx;
		color: #2979ff;
		font-weight: bold;
	}
}

.room-list {
	.room-item {
		background-color: #fff;
		margin: 0 20rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);

		.room-image {
			padding: 24rpx 24rpx 0;
		}

		.room-info {
			padding: 24rpx;

			.room-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 12rpx;
			}

			.room-details {
				display: flex;
				margin-bottom: 16rpx;

				text {
					font-size: 24rpx;
					color: #666;
					margin-right: 24rpx;
				}
			}

			.room-facilities {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				margin-bottom: 16rpx;

				.facility-tag {
					background-color: #f0f0f0;
					color: #666;
					font-size: 20rpx;
					padding: 4rpx 8rpx;
					border-radius: 8rpx;
					margin-right: 8rpx;
					margin-bottom: 8rpx;
				}

				.more-facilities {
					font-size: 20rpx;
					color: #2979ff;
				}
			}

			.price-details {
				background-color: #f8f9fa;
				border-radius: 8rpx;
				padding: 16rpx;
				margin-top: 16rpx;

				.price-detail-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 8rpx 0;

					&:not(:last-child) {
						border-bottom: 1rpx solid #eee;
					}

					.detail-date {
						font-size: 24rpx;
						color: #333;
					}

					.detail-type {
						font-size: 22rpx;
						color: #666;
					}

					.detail-price {
						font-size: 24rpx;
						color: #ff6b35;
						font-weight: bold;
					}
				}
			}
		}

		.room-booking {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			padding: 0 24rpx 24rpx;

			.price-section {
				flex: 1;

				.total-price {
					display: flex;
					align-items: baseline;
					margin-bottom: 8rpx;

					.price-symbol {
						font-size: 24rpx;
						color: #ff6b35;
					}

					.price-number {
						font-size: 36rpx;
						font-weight: bold;
						color: #ff6b35;
					}
				}

				.avg-price {
					font-size: 22rpx;
					color: #999;
					margin-bottom: 8rpx;
				}

				.price-toggle {
					display: flex;
					align-items: center;
					font-size: 22rpx;
					color: #2979ff;

					text {
						margin-right: 4rpx;
					}
				}
			}

			.availability-section {
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				.remaining-rooms {
					font-size: 22rpx;
					color: #ff6b35;
					margin-bottom: 12rpx;
				}

				.unavailable-reason {
					font-size: 22rpx;
					color: #999;
					margin-bottom: 12rpx;
				}

				.book-btn {
					width: 160rpx;
					height: 64rpx;
				}
			}
		}
	}
}

.no-rooms {
	padding: 100rpx 0;
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 0;

	text {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #666;
	}
}
</style>
