<template>
	<view class="date-select-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<u-icon name="arrow-left" size="20" color="#333"></u-icon>
				</view>
				<view class="navbar-title">选择日期</view>
				<view class="navbar-right" @click="closeCalendar">
					<u-icon name="close" size="20" color="#333"></u-icon>
				</view>
			</view>
		</view>

		<!-- 日期选择区域 -->
		<view class="date-section">
			<view class="section-title">选择入住日期</view>
			<view class="section-subtitle">请选择您的入住和离店日期</view>

			<view class="date-row">
				<view class="date-item" :class="{ active: currentSelectType === 'checkIn' }" @click="selectDateType('checkIn')">
					<view class="date-label">入住日期</view>
					<view class="date-value">{{ formatDisplayDate(checkInDate) || '2025-07-26' }}</view>
					<view class="date-week">{{ getWeekDay(checkInDate) || '周六' }}</view>
				</view>
				<view class="date-separator">
					<u-icon name="arrow-right" size="16" color="#999"></u-icon>
				</view>
				<view class="date-item" :class="{ active: currentSelectType === 'checkOut' }" @click="selectDateType('checkOut')">
					<view class="date-label">离店日期</view>
					<view class="date-value">{{ formatDisplayDate(checkOutDate) || '2025-07-27' }}</view>
					<view class="date-week">{{ getWeekDay(checkOutDate) || '周日' }}</view>
				</view>
			</view>
		</view>

		<!-- 自定义日历 -->
		<view class="calendar-container">
			<view class="calendar-header">
				<view class="month-selector">
					<view class="month-nav" @click="prevMonth">
						<u-icon name="arrow-left" size="16" color="#666"></u-icon>
					</view>
					<view class="month-title">{{ currentMonthText }}</view>
					<view class="month-nav" @click="nextMonth">
						<u-icon name="arrow-right" size="16" color="#666"></u-icon>
					</view>
				</view>

				<view class="week-header">
					<view class="week-day" v-for="day in weekDays" :key="day">{{ day }}</view>
				</view>
			</view>

			<view class="calendar-body">
				<view class="calendar-month" v-for="(month, monthIndex) in displayMonths" :key="monthIndex">
					<view class="month-title-small">{{ month.title }}</view>
					<view class="calendar-grid">
						<view
							class="calendar-day"
							v-for="(day, dayIndex) in month.days"
							:key="dayIndex"
							:class="getDayClass(day)"
							@click="selectDate(day)"
						>
							<view class="day-number">{{ day.day }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部确认按钮 -->
		<view class="confirm-footer">
			<u-button
				type="primary"
				:disabled="!canConfirm"
				@click="confirmDates"
				class="confirm-btn"
			>
				确定
			</u-button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DateSelect',
	data() {
		return {
			hotelId: null,
			checkInDate: '',
			checkOutDate: '',
			currentSelectType: 'checkIn', // 当前选择类型：checkIn 或 checkOut

			// 日历相关
			currentMonth: new Date(),
			weekDays: ['一', '二', '三', '四', '五', '六', '日'],
			displayMonths: [],

			// 日期限制
			minDate: new Date(),
			maxDate: new Date(new Date().getTime() + 90 * 24 * 60 * 60 * 1000)
		}
	},

	computed: {
		// 入住晚数
		nightsCount() {
			if (this.checkInDate && this.checkOutDate) {
				const checkIn = new Date(this.checkInDate)
				const checkOut = new Date(this.checkOutDate)
				return Math.ceil((checkOut - checkIn) / (24 * 60 * 60 * 1000))
			}
			return 0
		},

		// 是否可以确认
		canConfirm() {
			return this.checkInDate && this.checkOutDate && this.nightsCount > 0
		},

		// 当前月份文本
		currentMonthText() {
			const year = this.currentMonth.getFullYear()
			const month = this.currentMonth.getMonth() + 1
			return `${year}年${month}月`
		}
	},

	onLoad(options) {
		console.log('页面加载参数:', options)

		if (options.hotelId) {
			this.hotelId = options.hotelId
		}

		// 设置默认日期（今天和明天）
		const today = new Date()
		const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

		// 如果有传入的日期，使用传入的日期，否则使用默认日期
		this.checkInDate = options.checkInDate || this.formatDate(today)
		this.checkOutDate = options.checkOutDate || this.formatDate(tomorrow)

		console.log('初始化日期:', {
			checkInDate: this.checkInDate,
			checkOutDate: this.checkOutDate
		})

		// 初始化日历
		this.initCalendar()
	},

	onReady() {
		// 页面渲染完成后再次初始化日历，确保数据正确显示
		this.$nextTick(() => {
			this.initCalendar()
		})
	},

	methods: {
		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 格式化显示日期
		formatDisplayDate(dateStr) {
			if (!dateStr) return ''
			return dateStr
		},

		// 获取星期几
		getWeekDay(dateStr) {
			if (!dateStr) return ''
			const date = new Date(dateStr)
			const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			return weekDays[date.getDay()]
		},

		// 选择日期类型
		selectDateType(type) {
			console.log('选择日期类型:', type)
			this.currentSelectType = type
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 关闭日历
		closeCalendar() {
			uni.navigateBack()
		},

		// 初始化日历
		initCalendar() {
			this.generateCalendarMonths()
		},

		// 生成日历月份数据
		generateCalendarMonths() {
			const months = []
			const startDate = new Date(this.currentMonth)

			// 生成当前月和下个月的数据
			for (let i = 0; i < 2; i++) {
				const monthDate = new Date(startDate.getFullYear(), startDate.getMonth() + i, 1)
				const monthData = this.generateMonthData(monthDate)
				months.push(monthData)
			}

			this.displayMonths = months
		},

		// 生成单个月份的数据
		generateMonthData(monthDate) {
			const year = monthDate.getFullYear()
			const month = monthDate.getMonth()
			const firstDay = new Date(year, month, 1)
			const lastDay = new Date(year, month + 1, 0)
			const daysInMonth = lastDay.getDate()

			// 获取第一天是星期几（0=周日，1=周一...）
			let firstDayOfWeek = firstDay.getDay()
			// 转换为周一开始（0=周一，1=周二...）
			firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1

			const days = []

			// 添加上个月的日期（填充空白）
			const prevMonth = new Date(year, month - 1, 0)
			const prevMonthDays = prevMonth.getDate()
			for (let i = firstDayOfWeek - 1; i >= 0; i--) {
				days.push({
					day: prevMonthDays - i,
					date: this.formatDate(new Date(year, month - 1, prevMonthDays - i)),
					isCurrentMonth: false,
					isDisabled: true
				})
			}

			// 添加当前月的日期
			for (let day = 1; day <= daysInMonth; day++) {
				const currentDate = new Date(year, month, day)
				const dateStr = this.formatDate(currentDate)
				const isDisabled = currentDate < this.minDate || currentDate > this.maxDate

				days.push({
					day: day,
					date: dateStr,
					isCurrentMonth: true,
					isDisabled: isDisabled,
					isSelected: dateStr === this.checkInDate || dateStr === this.checkOutDate,
					isInRange: this.isDateInRange(dateStr),
					isStart: dateStr === this.checkInDate,
					isEnd: dateStr === this.checkOutDate
				})
			}

			// 补充下个月的日期（填充到42个格子）
			const remainingDays = 42 - days.length
			for (let day = 1; day <= remainingDays; day++) {
				days.push({
					day: day,
					date: this.formatDate(new Date(year, month + 1, day)),
					isCurrentMonth: false,
					isDisabled: true
				})
			}

			return {
				title: `${year}年${month + 1}月`,
				days: days
			}
		},

		// 判断日期是否在选中范围内
		isDateInRange(dateStr) {
			if (!this.checkInDate || !this.checkOutDate) return false
			const date = new Date(dateStr)
			const checkIn = new Date(this.checkInDate)
			const checkOut = new Date(this.checkOutDate)
			return date > checkIn && date < checkOut
		},



		// 获取日期样式类
		getDayClass(day) {
			const classes = ['calendar-day']

			if (!day.isCurrentMonth) {
				classes.push('other-month')
			}

			if (day.isDisabled) {
				classes.push('disabled')
			}

			if (day.isSelected) {
				classes.push('selected')
			}

			if (day.isStart) {
				classes.push('range-start')
			}

			if (day.isEnd) {
				classes.push('range-end')
			}

			if (day.isInRange) {
				classes.push('in-range')
			}

			return classes
		},

		// 选择日期
		selectDate(day) {
			if (day.isDisabled || !day.isCurrentMonth) {
				return
			}

			const selectedDate = day.date
			console.log('选择日期:', selectedDate)

			if (!this.checkInDate || (this.checkInDate && this.checkOutDate)) {
				// 重新开始选择
				this.checkInDate = selectedDate
				this.checkOutDate = ''
				this.currentSelectType = 'checkOut'
			} else if (this.checkInDate && !this.checkOutDate) {
				// 选择离店日期
				if (new Date(selectedDate) > new Date(this.checkInDate)) {
					this.checkOutDate = selectedDate
				} else {
					// 如果选择的日期早于入住日期，重新设置入住日期
					this.checkInDate = selectedDate
					this.checkOutDate = ''
				}
			}

			// 重新生成日历数据
			this.generateCalendarMonths()
		},

		// 上一个月
		prevMonth() {
			this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1)
			this.generateCalendarMonths()
		},

		// 下一个月
		nextMonth() {
			this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1)
			this.generateCalendarMonths()
		},

		// 确认日期选择
		confirmDates() {
			console.log('确认日期选择')

			if (!this.canConfirm) {
				uni.showToast({
					title: '请选择完整的入住和离店日期',
					icon: 'none'
				})
				return
			}

			// 如果有hotelId，跳转到房型列表页面
			if (this.hotelId) {
				const params = {
					hotelId: this.hotelId,
					checkInDate: this.checkInDate,
					checkOutDate: this.checkOutDate
				}

				console.log('跳转参数:', params)

				const query = Object.keys(params).map(key => `${key}=${params[key]}`).join('&')

				uni.redirectTo({
					url: `/pages/shopping/hotel/rooms?${query}`
				})
			} else {
				// 没有hotelId，返回首页并传递日期参数
				const pages = getCurrentPages()
				const prevPage = pages[pages.length - 2]

				if (prevPage) {
					// 更新上一页的日期数据
					prevPage.$vm.checkInDate = this.checkInDate
					prevPage.$vm.checkOutDate = this.checkOutDate
					// 重新加载酒店列表
					prevPage.$vm.loadHotelList()
				}

				uni.navigateBack()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.date-select-page {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

// 自定义导航栏
.custom-navbar {
	background-color: #fff;
	padding-top: var(--status-bar-height);
	border-bottom: 1rpx solid #eee;

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.navbar-left, .navbar-right {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.navbar-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}
}

.date-section {
	background-color: #fff;
	padding: 40rpx 32rpx;

	.section-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 12rpx;
	}

	.section-subtitle {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.date-row {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.date-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 24rpx;
			border: 2rpx solid #2979ff;
			border-radius: 12rpx;
			background-color: #f0f8ff;
			transition: all 0.3s;

			&.active {
				border-color: #1976d2;
				background-color: #e3f2fd;
			}

			.date-label {
				font-size: 24rpx;
				color: #666;
				margin-bottom: 8rpx;
			}

			.date-value {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 4rpx;
			}

			.date-week {
				font-size: 24rpx;
				color: #666;
			}
		}

		.date-separator {
			margin: 0 24rpx;
			display: flex;
			align-items: center;
		}
	}
}

// 自定义日历样式
.calendar-container {
	flex: 1;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.calendar-header {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.month-selector {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;

		.month-nav {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			background-color: #f5f5f5;
		}

		.month-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.week-header {
		display: flex;

		.week-day {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			padding: 16rpx 0;
		}
	}
}

.calendar-body {
	flex: 1;
	overflow-y: auto;

	.calendar-month {
		padding: 0 32rpx 40rpx;

		.month-title-small {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
			text-align: center;
			padding: 24rpx 0;
		}

		.calendar-grid {
			display: flex;
			flex-wrap: wrap;

			.calendar-day {
				width: calc(100% / 7);
				aspect-ratio: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;
				margin-bottom: 8rpx;

				.day-number {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 4rpx;
				}



				// 其他月份的日期
				&.other-month {
					.day-number {
						color: #ccc;
					}
				}

				// 禁用状态
				&.disabled {
					.day-number {
						color: #ddd;
					}
				}

				// 选中状态
				&.selected {
					background-color: #ff6b35;
					border-radius: 8rpx;

					.day-number {
						color: #fff;
					}
				}

				// 范围开始
				&.range-start {
					background-color: #ff6b35;
					border-radius: 8rpx;

					.day-number {
						color: #fff;
					}

					&::after {
						content: '开始';
						position: absolute;
						bottom: 4rpx;
						font-size: 18rpx;
						color: #fff;
					}
				}

				// 范围结束
				&.range-end {
					background-color: #ff6b35;
					border-radius: 8rpx;

					.day-number {
						color: #fff;
					}
				}

				// 范围内
				&.in-range {
					background-color: rgba(255, 107, 53, 0.1);

					.day-number {
						color: #ff6b35;
					}
				}
			}
		}
	}
}

.confirm-footer {
	background-color: #fff;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #eee;

	.confirm-btn {
		width: 100%;
		height: 88rpx;
		border-radius: 44rpx;
		background-color: #ff6b35;

		&:disabled {
			background-color: #ccc;
		}
	}
}
</style>
