/*
 金梭酒店项目数据库表结构设计

 基于CRMEB多商户电商系统扩展
 新增3张酒店专用表，复用现有eb_merchant、eb_product、eb_order等表

 创建时间: 2025-01-17
 版本: v1.0
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 酒店房间基础信息表（新增）
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_room`;
CREATE TABLE `eb_hotel_room` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `room_name` varchar(100) NOT NULL COMMENT '房型名称',
  `room_type` varchar(50) DEFAULT '' COMMENT '房型类型(标准间,大床房,套房等)',
  `room_facilities` text COMMENT '房间设施(JSON格式)',
  `room_area` decimal(5,2) DEFAULT 0.00 COMMENT '房间面积(平方米)',
  `room_floor` varchar(20) DEFAULT '' COMMENT '楼层信息',
  `bed_type` varchar(50) DEFAULT '' COMMENT '床型(单人床,双人床,大床等)',
  `max_guests` int DEFAULT 2 COMMENT '最大入住人数',
  `base_price` decimal(8,2) DEFAULT 0.00 COMMENT '房型基础价格',
  `total_rooms` int NOT NULL DEFAULT 0 COMMENT '总房间数',
  `room_images` text COMMENT '房间图片(JSON格式)',
  `room_description` text COMMENT '房间描述',
  `max_booking_days` int DEFAULT 30 COMMENT '最大可预订天数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_id` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mer_id` (`mer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_room_type` (`room_type`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='酒店房间基础信息表';

-- ----------------------------
-- 2. 房型价格策略表（新增）
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_room_price_strategy`;
CREATE TABLE `eb_hotel_room_price_strategy` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `room_id` int NOT NULL COMMENT '房间ID(关联eb_hotel_room)',
  `strategy_name` varchar(100) NOT NULL COMMENT '策略名称(如:基础价格,周末价格,节假日价格)',
  `strategy_type` tinyint NOT NULL DEFAULT 1 COMMENT '策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期',
  `week_days` varchar(20) DEFAULT '' COMMENT '适用星期(1,2,3,4,5,6,7 逗号分隔)',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `specific_dates` text COMMENT '具体日期列表(JSON格式)',
  `price_value` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '价格值(固定价格)',
  `priority` int NOT NULL DEFAULT 0 COMMENT '优先级(数字越大优先级越高,节假日>周末>基础价格)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_id` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mer_room` (`mer_id`, `room_id`),
  KEY `idx_dates` (`start_date`, `end_date`),
  KEY `idx_priority` (`priority`),
  KEY `idx_strategy_type` (`strategy_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='酒店房型价格策略表';

-- ----------------------------
-- 3. 取消规则配置表（新增）
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_cancel_rule`;
CREATE TABLE `eb_hotel_cancel_rule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `advance_hours` int NOT NULL COMMENT '提前取消小时数',
  `penalty_type` tinyint NOT NULL DEFAULT 1 COMMENT '扣费类型:1-按比例,2-固定金额',
  `penalty_value` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '扣费值(比例或金额)',
  `min_advance_hours` int DEFAULT 0 COMMENT '最小提前取消小时数(小于此时间不可取消)',
  `description` varchar(255) DEFAULT '' COMMENT '规则描述',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(按提前时间从大到小)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_id` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mer_id` (`mer_id`),
  KEY `idx_advance_hours` (`advance_hours`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='酒店取消规则表';

-- ----------------------------
-- 初始化基础数据
-- ----------------------------

-- 插入示例价格策略数据（金梭大酒店）
-- 假设商户ID=1为金梭大酒店，房间ID=1为豪华大床房
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `price_value`, `priority`, `status`, `create_id`, `create_time`) VALUES
(1, 1, '基础价格(工作日)', 1, 100.00, 1, 1, 1, NOW()),
(1, 1, '周末价格', 2, 150.00, 2, 1, 1, NOW()),
(1, 1, '节假日价格', 3, 300.00, 3, 1, 1, NOW());

-- 插入示例取消规则数据（金梭大酒店）
-- 按照用户需求：48小时以上免费，24-48小时扣5%，12-24小时扣10%，2小时内不可取消，其他时间(2-12小时)全额退款
INSERT INTO `eb_hotel_cancel_rule` (`mer_id`, `rule_name`, `advance_hours`, `penalty_type`, `penalty_value`, `min_advance_hours`, `description`, `sort`, `status`, `create_id`, `create_time`) VALUES
(1, '48小时前取消', 48, 1, 0.00, 2, '入住前48小时以上取消，全额退款', 1, 1, 1, NOW()),
(1, '24-48小时取消', 24, 1, 5.00, 2, '入住前24-48小时取消，扣除5%手续费', 2, 1, 1, NOW()),
(1, '12-24小时取消', 12, 1, 10.00, 2, '入住前12-24小时取消，扣除10%手续费', 3, 1, 1, NOW()),
(1, '2小时内不可取消', 0, 1, 100.00, 0, '入住前2小时内不可取消订单', 4, 1, 1, NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 表结构说明
-- ----------------------------
/*
1. eb_hotel_room: 酒店房间基础信息表
   - 存储房型的基本信息：名称、类型、设施、面积、床型等
   - 包含总房间数、最大可预订天数等业务配置
   - 通过mer_id关联商户表，支持多商户模式

2. eb_hotel_room_price_strategy: 房型价格策略表
   - 支持多种价格策略：基础价格、周末价格、节假日价格等
   - 通过priority字段控制优先级：节假日 > 周末 > 基础价格
   - 集成中国日历接口，自动匹配日期类型

3. eb_hotel_cancel_rule: 取消规则配置表
   - 支持按时间段配置不同的取消规则
   - 支持按比例或固定金额扣费
   - 可配置最小取消时间限制

复用现有表：
- eb_merchant: 酒店商户信息
- eb_product: 通过定时任务生成的酒店商品（每个房型+日期=一个商品）
- eb_order: 酒店预订订单
- eb_order_detail: 订单详情（支持多房型组合下单）
*/
