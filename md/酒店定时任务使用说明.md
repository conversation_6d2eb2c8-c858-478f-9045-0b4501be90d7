# 酒店商品同步定时任务使用说明

## 📋 功能概述

酒店商品同步定时任务系统是基于CRMEB框架开发的自动化商品管理解决方案，主要功能包括：

1. **自动商品生成**：根据酒店房间信息和价格策略，自动生成商品到eb_product表
2. **价格策略计算**：支持工作日、周末、节假日等多种价格策略
3. **分类自动管理**：自动创建和维护商户分类和平台分类
4. **过期商品清理**：定期清理过期的酒店商品数据
5. **完整数据同步**：包含商品详情、属性值、分销设置等完整信息

## 🚀 快速开始

### 1. 数据库初始化

执行初始化脚本：
```sql
-- 执行初始化脚本
source md/hotel_product_sync_init.sql;
```

### 2. 配置定时任务

在管理后台的定时任务管理中启用以下任务：

| 任务名称 | Bean名称 | 方法名称 | Cron表达式 | 说明 |
|---------|----------|----------|------------|------|
| 酒店商品同步 | HotelProductSyncTask | syncHotelProducts | 0 0 2 * * ? | 每日凌晨2点执行 |
| 过期商品清理 | HotelProductSyncTask | cleanExpiredProducts | 0 0 3 * * ? | 每日凌晨3点执行 |

### 3. 准备酒店数据

确保以下数据已正确配置：

#### 3.1 酒店房间数据 (eb_hotel_room)
```sql
INSERT INTO eb_hotel_room (mer_id, room_name, room_type, total_rooms, status, ...) 
VALUES (1, '豪华大床房', '大床房', 10, 1, ...);
```

#### 3.2 价格策略数据 (eb_hotel_room_price_strategy)
```sql
INSERT INTO eb_hotel_room_price_strategy (room_id, strategy_type, price_value, priority, ...) 
VALUES 
(1, 1, 100.00, 1, ...), -- 工作日价格
(1, 2, 150.00, 2, ...), -- 周末价格
(1, 3, 300.00, 3, ...); -- 节假日价格
```

## 📊 核心功能详解

### 1. 商品生成逻辑

#### 1.1 商品名称格式
```
格式：{酒店名称}-{房型名称}-{入住日期}
示例：金梭大酒店-豪华大床房-2025-01-20
```

#### 1.2 SKU编码格式
```
格式：HOTEL_{商户ID}_{房间ID}_{日期}
示例：HOTEL_1_1_20250120
```

#### 1.3 价格计算规则
- **节假日价格** > **周末价格** > **工作日价格**
- 按优先级(priority)从高到低匹配
- 市场价 = 商品价格 × 1.2
- 成本价 = 商品价格 × 0.8

### 2. 分类管理

#### 2.1 自动分类创建
- **平台分类**：eb_product_category表，名称为"酒店预订"
- **商户分类**：eb_merchant_product_category表，每个商户独立创建

#### 2.2 分类字段映射
```java
// eb_product表字段映射
product.setCateId(String.valueOf(merchantCategoryId)); // varchar类型
product.setCategoryId(platformCategoryId);             // int类型
```

### 3. 分销设置

#### 3.1 默认分销配置
- 一级分佣：10%
- 二级分佣：5%
- 分佣类型：默认分佣(2)

#### 3.2 分销字段设置
```java
// 商品表分销设置
product.setIsSub(true); // 参与分销

// 属性值表分销设置
attrValue.setBrokerage(10);    // 一级分佣10%
attrValue.setBrokerageTwo(5);  // 二级分佣5%
```

## 🔧 配置参数

### 系统配置参数

| 配置键 | 默认值 | 说明 |
|--------|--------|------|
| hotel_product_sync_enabled | 1 | 是否启用同步：1-启用，0-禁用 |
| hotel_product_generate_days | 30 | 商品生成天数 |
| hotel_product_expired_days | 30 | 过期商品清理天数 |
| hotel_default_image | /static/images/hotel_default.jpg | 默认商品图片 |
| hotel_brokerage_first | 10 | 一级分佣比例(%) |
| hotel_brokerage_second | 5 | 二级分佣比例(%) |

### 常量配置

参考 `HotelConstants.java` 文件中的常量定义。

## 🧪 测试验证

### 1. 执行测试脚本
```sql
source md/hotel_product_sync_test.sql;
```

### 2. 手动触发任务
在定时任务管理界面点击"立即执行"按钮。

### 3. 验证数据完整性
```sql
-- 检查商品生成情况
SELECT COUNT(*) FROM eb_product 
WHERE type = 2 AND delivery_method = '2' AND is_del = 0;

-- 检查分类创建情况
SELECT * FROM eb_product_category WHERE name = '酒店预订';
SELECT * FROM eb_merchant_product_category WHERE name = '酒店预订';
```

## 📈 监控和日志

### 1. 任务执行日志
查看 `eb_schedule_job_log` 表了解任务执行状态。

### 2. 同步统计日志
查看 `eb_hotel_sync_log` 表了解详细同步统计。

### 3. 应用日志
```bash
# 查看同步任务日志
tail -f logs/crmeb.log | grep "HotelProductSync"
```

## ⚠️ 注意事项

### 1. 数据依赖
- 确保 `eb_hotel_room` 表有有效的房间数据
- 确保 `eb_hotel_room_price_strategy` 表有完整的价格策略
- 确保中国日历服务 `ChineseCalendarService` 正常工作

### 2. 性能考虑
- 大量房间数据时，建议分批处理
- 定期清理过期商品避免数据膨胀
- 监控数据库索引性能

### 3. 错误处理
- 任务失败时会记录详细错误日志
- 支持部分失败继续处理其他数据
- 提供数据验证和修复功能

## 🔄 维护操作

### 1. 手动同步指定商户
```java
// 通过定时任务参数指定商户ID
syncHotelProductsByMerchant("1,2,3");
```

### 2. 清理测试数据
```sql
-- 清理指定商户的测试商品
DELETE FROM eb_product WHERE mer_id = 999 AND type = 2;
```

### 3. 重建分类
```sql
-- 删除现有分类后重新执行初始化脚本
DELETE FROM eb_merchant_product_category WHERE name = '酒店预订';
```

## 📞 技术支持

如遇到问题，请检查：
1. 数据库连接是否正常
2. 相关服务是否启动
3. 日志中的错误信息
4. 数据完整性验证结果

更多技术细节请参考源代码注释和相关文档。
