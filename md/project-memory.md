# CRMEB项目记忆文档

## 项目基本信息

### 项目概述
- **项目名称**: CRMEB Java多商户电商系统
- **技术栈**: SpringBoot 2.2.6 + MyBatis Plus 3.3.1 + MySQL + Redis + Vue
- **架构模式**: 前后端分离，多模块Maven项目
- **官方文档**: https://doc.crmeb.com/java/crmeb_java

### 项目结构
```
D:/dljs-sys/
├── crmeb-admin/          # 后台管理接口(商户后台管理)
├── crmeb-front/          # 移动端接口
├── crmeb-service/        # 业务服务层
├── crmeb-common/         # 工具类和公共组件
├── crmeb-generate/       # 代码生成器
├── dljs-sys-mer-web/     # 商户端PC前端页面(Vue)
├── dljs-sys-web/         # 管理端PC前端页面(Vue)
├── sql/                  # 数据库初始化脚本
├── md/                   # 开发文档和数据库文件
├── shell/                # 启动和部署脚本
└── cunzhi-memory/        # AI助手记忆存储
```

## 开发规范

### 代码规范
1. **架构模式**: Controller-Service-DAO三层架构
2. **数据库操作**: 使用MyBatis Plus进行数据库操作
3. **响应封装**: 统一使用CommonResult封装响应
4. **API文档**: 添加Swagger注解用于API文档
5. **权限控制**: 使用@PreAuthorize进行权限控制
6. **异常处理**: 使用CrmebException统一异常处理
7. **参数验证**: 使用@Validated注解进行参数验证
8. **代码简化**: 实体类使用Lombok注解简化代码

### 技术特性
1. **RESTful接口**: 标准RESTful接口设计原则
2. **权限管理**: Spring Security权限管理，可控制到按钮级别
3. **多端支持**: H5、微信公众号、小程序、APP
4. **缓存队列**: Redis缓存和队列处理
5. **表单生成**: Vue拖拽生成控件
6. **数据统计**: ECharts图表展示

### 用户要求
- ❌ 不要生成总结性Markdown文档
- ❌ 不要生成测试脚本
- ❌ 不要编译，用户自己编译
- ❌ 不要运行，用户自己运行
- ✅ 所有修改必须有中文注释说明意图
- ✅ 保持修改范围最小化，避免不必要的代码更改
- ✅ 严格按照CRMEB现有架构模式开发
- ✅ 不扩展现有表结构，最大化复用现有功能

## 金梭酒店项目

### 项目概述
实现酒店预订、入住、退房的数据化管理，包括酒店筛选、房型选择、微信支付确认、商家确认预约、预约结果生成二维码核销凭证等功能。

### 数据库设计
新建3个酒店专用表：
1. **eb_hotel_room**: 酒店房间表
2. **eb_hotel_room_price_strategy**: 价格策略表
3. **eb_hotel_cancel_rule**: 取消规则表

所有表包含标准字段：is_del、create_id、create_time、update_time

### 核心功能

#### 价格计算引擎
- **日期类型识别**: 调用ChineseCalendarService.getDateType()获取WORKDAY/WEEKEND/HOLIDAY/TRANSFER_WORKDAY
- **策略集合获取**: 查询房间所有启用价格策略按优先级排序
- **策略匹配过滤**: 根据strategyType匹配(1基础价格工作日,2周末价格,3节假日价格,4日期范围,5具体日期)
- **优先级排序**: 按priority降序取最高优先级策略的priceValue
- **异常处理**: 无策略或异常时返回BigDecimal.ZERO

#### 中国日历接口
- **API地址**: https://unpkg.com/holiday-calendar@1.1.9/data/CN/2025.json
- **功能**: 查询法定节假日，额外计算周末和工作日逻辑
- **缓存策略**: 三级缓存(本地+Redis+远程)
- **架构分布**: 
  - crmeb-common: 常量枚举和请求响应类
  - crmeb-service: 核心业务逻辑和缓存服务
  - crmeb-admin: 后台管理接口
  - crmeb-front: 前端查询接口

#### 定时任务策略
- **混合策略**: 智能插入+更新机制
- **时间分层保护**: 
  - 7天外: 可更新价格
  - 3-7天: 无订单时更新价格
  - 1-3天: 只更新库存
  - 24小时内: 完全锁定
- **数据清理**: 30天自动清理
- **性能优化**: 批量处理
- **监控告警**: 完整监控体系

#### 取消规则逻辑
- **规则配置**: 提前小时数和扣费策略
- **匹配逻辑**: 降序排序，找到第一个满足advanceHours>=rule.getAdvanceHours()的规则
- **特殊处理**: penalty_value=100%表示不可取消
- **状态返回**: -1不可取消、0免费取消、>0收费取消

### 开发进度

#### 已完成功能
1. **基础架构**: 数据库表、实体类、定时任务、中国日历接口
2. **第一阶段后端**: 
   - HotelRoomController(房型管理)
   - HotelPriceController(价格策略管理)  
   - HotelCancelRuleController(取消规则管理)
3. **第一阶段前端**: 
   - dljs-sys-mer-web/src/views/hotel/room/(房型管理页面)
   - dljs-sys-mer-web/src/views/hotel/price/(价格策略管理页面)
   - dljs-sys-mer-web/src/views/hotel/cancel/(取消规则管理页面)
4. **组件开发**: RoomForm.vue、PriceForm.vue、PriceCalendar.vue、CancelForm.vue、CancelCalculator.vue

#### 待开发功能
1. **第二阶段**: 用户端预订接口(HotelBookingController)
2. **第三阶段**: 业务服务层完善和系统优化
3. **统计报表**: 酒店订单统计和数据分析

### 技术实现要点

#### 商品生成机制
- **设计理念**: 单规格商品设计(每个房型+日期=一个独立商品)
- **商品识别**: 通过商品名称解析酒店信息
- **库存管理**: 复用eb_product表的stock字段
- **订单处理**: 完全复用现有电商订单流程

#### 前端展示方案
- **设计模式**: 基于携程/美团模式
- **展示理念**: "前端聚合展示+后端拆分存储"
- **用户流程**: 酒店列表→房型选择→日期价格→下单确认
- **查询逻辑**: 房型聚合查询、日期价格查询
- **下单处理**: 多日期订单处理

## 常用代码模式

### Controller层示例
```java
@RestController
@RequestMapping("/api/admin/hotel")
@ApiOperation("酒店管理")
public class HotelController {
    // 使用@PreAuthorize进行权限控制
    // 使用@ApiOperation添加接口文档
}
```

### Service层模式
```java
@Service
public class HotelServiceImpl implements HotelService {
    // 使用@Service注解和事务管理
    // 使用MyBatis Plus进行数据库操作
}
```

### 实体类模式
```java
@Data
@TableName("eb_hotel_room")
public class HotelRoom {
    // 使用@TableName指定表名
    // 使用Lombok注解简化代码
}
```

### 工具类使用
- **分页查询**: PageHelper.startPage()
- **Redis操作**: RedisUtil工具类
- **日期处理**: CrmebDateUtil
- **字符串工具**: StrUtil
- **对象工具**: ObjectUtil
- **权限验证**: SecurityUtil.getLoginUserVo()

## 数据库信息

### 核心表结构
1. **用户体系**: eb_user、eb_user_brokerage_record、eb_user_balance_record
2. **商户体系**: eb_merchant
3. **商品体系**: eb_product、eb_category
4. **订单体系**: eb_order、eb_order_detail
5. **分销体系**: 多表支持一级二级分销佣金计算
6. **统计体系**: 按日月统计各种业务数据

### 数据库文件
- **java_mer_trip.sql**: 带结构和数据的完整脚本
- **java_mer_trip_nodata.sql**: 仅包含表结构的脚本
- **hotel_tables_ddl.sql**: 酒店表结构DDL脚本
- **hotel_sample_data.sql**: 酒店示例数据
- **hotel_tables_test.sql**: 酒店表测试脚本

## 问题修复记录

### 编译错误修复
- 将不存在的自定义Pagination组件替换为Element UI的el-pagination组件
- 修复时间类型转换错误(LocalDateTime改为Date类型)
- 移除Java 9+特性(List.of()、var关键字)提高兼容性
- 修复LocalDate类型转换错误，添加转换工具方法

### 业务逻辑修复
- 修复酒店取消费用计算逻辑错误
- 修复价格日历组件渲染错误
- 修复酒店管理页面编辑和查看功能数据传递问题
- 修复取消规则表单字段映射错误
- 优化取消规则表单的免费取消逻辑

### UI界面优化
- 统一酒店管理页面布局(房型、价格、取消规则页面)
- 修改操作列为链接样式，添加分隔符
- 调整操作列宽度为190px，固定右侧显示

---

*文档生成时间: 2025-07-21*
*项目路径: D:/dljs-sys*
