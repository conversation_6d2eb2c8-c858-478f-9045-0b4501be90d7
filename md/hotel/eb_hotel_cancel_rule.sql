/*
 Navicat Premium Data Transfer

 Source Server         : wzblcy.cn
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : wzblcy.cn:10179
 Source Schema         : java_mer_trip

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 21/07/2025 18:10:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for eb_hotel_cancel_rule
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_cancel_rule`;
CREATE TABLE `eb_hotel_cancel_rule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `mer_id` int(11) NOT NULL COMMENT '商户ID(酒店ID)',
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则名称',
  `advance_hours` int(11) NOT NULL COMMENT '提前取消小时数',
  `penalty_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '扣费类型:1-按比例,2-固定金额',
  `penalty_value` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '扣费值(比例或金额)',
  `min_advance_hours` int(11) NULL DEFAULT 0 COMMENT '最小提前取消小时数(小于此时间不可取消)',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则描述',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序(按提前时间从大到小)',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `is_del` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `create_id` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mer_id`(`mer_id`) USING BTREE,
  INDEX `idx_advance_hours`(`advance_hours`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店取消规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of eb_hotel_cancel_rule
-- ----------------------------
INSERT INTO `eb_hotel_cancel_rule` VALUES (1, 3, '48小时前取消', 48, 1, 0.00, 2, '入住前48小时取消，全额退款', 1, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (2, 3, '24-48小时取消', 24, 1, 5.00, 2, '入住前24-48小时取消，扣除5%手续费', 2, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (3, 3, '12-24小时取消', 12, 1, 10.00, 2, '入住前12-24小时取消，扣除10%手续费', 3, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (4, 3, '2小时内不可取消', 2, 1, 100.00, 0, '入住前2小时内不可取消', 4, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (5, 3, '48小时前取消', 48, 1, 0.00, 2, '入住前48小时取消，全额退款', 1, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (6, 3, '24-48小时取消', 24, 1, 5.00, 2, '入住前24-48小时取消，扣除5%手续费', 2, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (7, 3, '12-24小时取消', 12, 1, 10.00, 2, '入住前12-24小时取消，扣除10%手续费', 3, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (8, 3, '2-12小时取消', 2, 1, 20.00, 2, '入住前2-12小时取消，扣除20%手续费', 4, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_cancel_rule` VALUES (9, 3, '2小时内不可取消', 0, 1, 30.00, 0, '入住前2小时内不可取消订单', 5, 1, 0, 1, '2025-07-17 07:21:11', '2025-07-21 16:55:08');
INSERT INTO `eb_hotel_cancel_rule` VALUES (10, 3, '测试免费规则', 0, 1, 0.00, 0, '测试免费规则', 0, 1, 0, 5, '2025-07-21 14:34:47', '2025-07-21 14:34:47');
INSERT INTO `eb_hotel_cancel_rule` VALUES (11, 3, '免费', 0, 1, 0.00, 0, '免费取消,不走逻辑判断', 0, 1, 0, 5, '2025-07-21 16:06:44', '2025-07-21 16:06:44');

SET FOREIGN_KEY_CHECKS = 1;
