/*
 Navicat Premium Data Transfer

 Source Server         : wzblcy.cn
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : wzblcy.cn:10179
 Source Schema         : java_mer_trip

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 21/07/2025 18:11:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for eb_hotel_room_price_strategy
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_room_price_strategy`;
CREATE TABLE `eb_hotel_room_price_strategy`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `mer_id` int(11) NOT NULL COMMENT '商户ID(酒店ID)',
  `room_id` int(11) NOT NULL COMMENT '房间ID(关联eb_hotel_room)',
  `strategy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策略名称(如:基础价格,周末价格,节假日价格)',
  `strategy_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '策略类型:1-基础价格(工作日),2-周末价格,3-节假日价格,4-按日期范围,5-按具体日期',
  `week_days` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '适用星期(1,2,3,4,5,6,7 逗号分隔)',
  `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `specific_dates` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '具体日期列表(JSON格式)',
  `price_value` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '价格值(固定价格)',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级(数字越大优先级越高,节假日>周末>基础价格)',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `is_del` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `create_id` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mer_room`(`mer_id`, `room_id`) USING BTREE,
  INDEX `idx_dates`(`start_date`, `end_date`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_strategy_type`(`strategy_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店房型价格策略表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of eb_hotel_room_price_strategy
-- ----------------------------
INSERT INTO `eb_hotel_room_price_strategy` VALUES (1, 3, 1, '基础价格(工作日)', 1, '', NULL, NULL, NULL, 100.00, 1, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (2, 3, 1, '周末价格', 2, '', NULL, NULL, NULL, 150.00, 2, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (3, 3, 1, '节假日价格', 3, '', NULL, NULL, NULL, 300.00, 3, 1, 0, 1, '2025-07-17 07:21:04', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (4, 3, 1, '基础价格(工作日)', 1, '1,2,3,4,5', NULL, NULL, NULL, 100.00, 1, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (5, 3, 1, '周末价格', 2, '6,7', NULL, NULL, NULL, 150.00, 2, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (6, 3, 1, '节假日价格', 3, '', NULL, NULL, NULL, 300.00, 3, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (7, 3, 2, '基础价格(工作日)', 1, '1,2,3,4,5', NULL, NULL, NULL, 80.00, 1, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (8, 3, 2, '周末价格', 2, '6,7', NULL, NULL, NULL, 120.00, 2, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (9, 3, 2, '节假日价格', 3, '', NULL, NULL, NULL, 250.00, 3, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (10, 3, 3, '基础价格(工作日)', 1, '1,2,3,4,5', NULL, NULL, NULL, 200.00, 1, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (11, 3, 3, '周末价格', 2, '6,7', NULL, NULL, NULL, 280.00, 2, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (12, 3, 3, '节假日价格', 3, '', NULL, NULL, NULL, 500.00, 3, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (13, 3, 4, '基础价格(工作日)', 1, '1,2,3,4,5', NULL, NULL, NULL, 150.00, 1, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (14, 3, 4, '周末价格', 2, '6,7', NULL, NULL, NULL, 200.00, 2, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (15, 3, 4, '节假日价格', 3, '', NULL, NULL, NULL, 400.00, 3, 1, 0, 1, '2025-07-17 07:21:11', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (16, 3, 5, '基础价格(工作日)', 1, '1,2,3,4,5', NULL, NULL, NULL, 120.00, 1, 1, 0, 1, '2025-07-17 07:21:12', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (17, 3, 5, '周末价格', 2, '6,7', NULL, NULL, NULL, 180.00, 2, 1, 0, 1, '2025-07-17 07:21:12', NULL);
INSERT INTO `eb_hotel_room_price_strategy` VALUES (18, 3, 5, '节假日价格', 3, '', NULL, NULL, NULL, 350.00, 3, 1, 0, 1, '2025-07-17 07:21:12', NULL);

SET FOREIGN_KEY_CHECKS = 1;
