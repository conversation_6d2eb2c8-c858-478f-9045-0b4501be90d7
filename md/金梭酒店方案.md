# 金梭酒店方案

## 一、功能实现效果

实现酒店预订、入住、退房的数据化管理，用户通过 "金梭岛" 微信小程序实现全流程操作，包括在线选房（支持 房型图片预览）、微信支付集成、一键联系前台、定位导航至酒店、电子下单凭证生成（可直接出示核销）；减少前台人工干预，将提升客房预订处理效率，缩短入住办理时间。同时，系统自动同步房态信息，确保前台、客房服务、用户数据实时一致，避免出现超售、房态混乱等情况。

## 二、预订操作流程 （全部开发）

1. 酒店筛选（1 步）（3天）
2. 房型选择（含日期 / 价格 / 环境图片对比）（1 步）（5天）
   1.  酒店详情界面将呈现酒店基础信息：

   2. 房型选择机制：提供单选形式的房型列表（标间 / 套房等）
   3. 时间选择组件：采用日历联动控件，开始时间默认定位当前日期 + 1 天，结束时间需晚于开始时间，形成入住 - 离店时间范围选择
   4. 房源校验逻辑：实时调用房态接口，当所选房型在指定时间段内剩余房源≤0 时，对应日期格子置灰显示 "无房" 状态，禁止用户点击选择，顶部动态提示条同步显示 "XX 房型在所选日期已售罄"
   5. 价格计算规则：
      1. 基础价与特殊日期优先级 服务端以房型基础价为计算基准，若预订日期覆盖节假日或特殊日期，按以下优先级规则取值（如：基础房费 300 元 / 晚，周末（特殊日期）400 元 / 晚，五一小长假（节假日）600 元 / 晚。若所选日期同时符合周末和五一小长假条件，优先按五一小长假 600 元 / 晚计算房价）
      2. 多日期组合计算 若预订日期跨不同价格区间（如同时包含平日、周末、节假日），系统将按各每天日期对应的价格分别核算，再汇总总价。
      3. 支持显示价格组成明细（如：基础房费 300 元 ×2 晚 + 周末房费 400 元 ×2 晚 + 节假日房费 400 元 ×3 晚，合计：2600元）
      4. 选择变更时触发价格动态计算刷新
   6. 交互反馈优化：
      1. 未选择房型时时间控件置灰锁定
      2. 选择跨日期范围超过 30 天时弹出提示 "最多支持预订 30 天"
      3. 已选时间区间在日历上用蓝色区块高亮显示，时间选择完成后，在详情界面上显示" xx月xx日 - xx月xx日 共x晚"的简略信息
3. 【支付完成】微信支付确认（1 步）（3天）
4. 【商家确认】商家确认预约房源（线下操作）
5. 【预约结果】预约成功生成二维码核销凭证、预约失败订单自动退款（0步）（3天）

## 三、后台数据维护

1. 酒店商家基本信息维护（适配开发）（2天）

- 可录入酒店名称、地址、联系方式等基础信息

2. 房源类型与基础信息设置（大量开发）（5天）

- 商品添加时，商品类型选择"酒店预约"
- 在商品中可添加多种房型（大床房、双人间、三人间等），房型表示为商品分类
- 为每种房型设置基础信息、简介、价格、相片
- 配置各房型的库存数量，可实时更新剩余房源
- 系统自动刷新最新一天的房源日期和库存数量

3. 特殊时段价格信息维护（全部开发）（3天）

- 可单独设置特殊日期（每周周五、每周周六）的价格
- 支持配置小长假（如端午、中秋等）的价格
- 系统自动根据设置获取特殊时段价格

4. 酒店订单管理（大量开发）（5天）

- 订单流程管理包括：订单创建与录入、订单处理与确认、订单状态追踪、订单修改与取消

## 四、取消酒店预订

1. 配置酒店取消规则，一家酒店有一个套规则，规则只针对游客，商家没有限制
2. 配置提前多长时间取消，可以全额退款，比如：入住前48小时的，取消可以全额退回
3. 配置提前多长时间取消，会被扣除多少比例的金额，比如：

入住前24至48小时，取消扣5%，原价为268的价格，退款金额为268 - （268*0.05）= 254.6元

入住前12至24小时，取消扣10%，原价为268的价格，退款金额为268 - （268*0.1）= 241.2元

1. 配置最晚取消时间，比如：入住前2小时，不可以取消订单





# 分销方案

# 一、核心功能概述

分销功能通过 “用户申请成为分销员→推广商品获取提成→销售成功后提现报酬” 的闭环流程，激励用户主动推广商品，实现平台与分销员的共赢。

# 二、功能模块拆解

## 1. 分销员申请与审核

- 申请入口：用户在小程序中进入 ”我的“、“分销赚钱” 页面，填写信息 “申请成为分销员”
- 审核机制：
  - 人工审核：平台运营人员手动审核。
- 身份标识：审核通过后，用户成为正式分销员，获得专属分销推广权限。

## 2. 分销信息展示与提成标识

- 商品筛选：分销员可在 “商品” 详情中查看商品分销成功的报酬。
- 提成展示：
  - 商品详情页显著位置标注 “分享赚XX 元”。

## 3. 推广与订单追踪

- 推广链接：分销员可生成商品专属推广链接及商品分销海报，通过社交平台分享链接或者商品分销海报。
- 订单绑定机制：
  - 用户通过分销链接下单后，系统自动绑定分销关系。
  - 订单状态变更（如订单支付、订单收货、订单完成）时，实时更新分销员的“累计佣金”、“可提现余额”。

## 4. 收益结算与提现

- 收益计算规则：
  - 单笔订单结算：
    - 一级佣金 = 产品最终成交价 * 一级返佣比例
    - 二级佣金 = 产品最终成交价 * 二级返佣比例
  - 提现规则：当“可提现余额”达到最低提现门槛，可发起提现申请。
- 提现流程：
  - 分销员在 “分销中心” 页面点击 “立即提现”。
  - 系统自动校验 “可提现余额”并展示“提现的确认”页面，分销员需要上传“微信收款码”，填写“微信号”及“姓名”;也可填写银行卡信息 （姓名，卡号等）。
  - 提现申请提交后，由财务人员线下进行核实转账处理，提现申请通过后系统自动扣减对应因金额并更新提现状态。

## 5.平台端操作

- 审核专员：负责分销人员准入资格审核，并在平台中完成审核状态更新。
- 财务专员：
  - 负责提现申请流程处理：审核分销员提交的提现信息（账户准确性、金额合规性），对通过审核的申请执行线下扫码转账操作；
  - 转账完成同步系统数据，更新提现订单状态（如 “审核通过”“打款中”“已完成”）。
- 商品运营专员：
  -  在商品录入系统时，精准配置分销返佣规则：

  - 一级分销返佣比例
  - 二级分销返佣比例





# 金梭酒店项目数据库设计方案

基于对金梭酒店方案文档的深入分析和现有数据库表结构的评估，我为您制定以下数据库设计方案：

### 📊 **现有表结构复用分析**

#### ✅ **可直接复用的表**

1. **eb_merchant** - 酒店商户信息
   - 已有字段完全满足：名称、地址、联系方式、经纬度、星级、头像、封面图等
   - 无需修改，直接用于酒店商户管理
2. **eb_user** - 用户管理
   - 完整支持：用户注册、分销员身份、佣金管理等
   - 无需修改
3. **eb_user_brokerage_record** - 分销佣金记录
   - 完全支持酒店分销功能
   - 无需修改
4. **eb_category** - 分类管理
   - 可用于房型分类管理
   - 无需修改

#### 🔄 **需要扩展的表**

1. **eb_product** - 商品表（用作房型信息）
   - ✅ 现有字段可用：名称、简介、图片、价格、库存、分销佣金等
   - ⚠️ 需要扩展：酒店特有的房型属性和预订规则
2. **eb_order** - 订单表（用作酒店预订订单）
   - ✅ 现有字段可用：订单流程、支付、状态管理等
   - ⚠️ 需要扩展：入住/离店时间、核销码等酒店特有字段

### 🆕 **需要新建的酒店专用表**

#### **1. 酒店房型价格策略表**

```sql
eb_hotel_room_price_strategy
```

- 管理特殊时段价格（周末、节假日）
- 支持灵活的价格规则配置

#### **2. 酒店房态管理表**

```sql
eb_hotel_room_inventory
```

- 按日期管理房间库存
- 实时房态查询和更新

#### **3. 酒店预订扩展表**

```sql
eb_hotel_booking_extend
```

- 存储酒店预订特有信息
- 入住/离店时间、特殊需求等

#### **4. 酒店取消规则表**

```sql
eb_hotel_cancel_rule
```

- 配置取消政策
- 时间段和扣费比例管理

### 🎯 **具体实施方案**

#### **阶段一：表结构设计**

1. 设计4个新表的完整结构
2. 确定eb_product和eb_order的扩展字段
3. 编写DDL脚本

#### 阶段二：核心功能开发

1. 酒店商户管理（2天）- 复用eb_merchant
2. 房型管理（5天）- 基于eb_product扩展
3. 价格策略管理（3天）- 新建价格策略表
4. 预订流程（5天）- 基于eb_order扩展

#### **阶段三：高级功能**

1. 房态实时管理（3天）
2. 取消规则配置（3天）
3. 核销码生成（2天）



# 金梭酒店项目开发计划

## 📋 项目概述

基于现有CRMEB多商户电商系统，扩展开发酒店预订功能，实现完整的酒店预订、入住、退房数据化管理。

## 🗄️ 数据库设计方案

### 复用现有表结构

- **eb_merchant** → 酒店商户管理
- **eb_product** → 房型信息管理（扩展酒店相关字段）
- **eb_order** → 酒店预订订单（扩展入住时间等字段）
- **eb_user + 分销体系** → 用户管理和分销功能

### 新建酒店专用表

1. **eb_hotel_room_price_strategy** - 房型价格策略表
2. **eb_hotel_room_inventory** - 房态管理表
3. **eb_hotel_booking_extend** - 预订扩展信息表
4. **eb_hotel_cancel_rule** - 取消规则配置表

## 🚀 开发阶段规划

### 阶段一：数据库设计与基础架构（3天）

#### Day 1: 数据库表结构设计

- 设计4个新表的完整结构
- 确定现有表的扩展字段

#### Day 2: 实体类和基础配置

- 创建酒店相关Entity类
- 配置MyBatis Plus映射
- 创建基础DAO接口

#### Day 3: 基础服务层搭建

- 创建酒店相关Service接口和实现
- 配置事务管理
- 编写基础CRUD方法

### 阶段二：核心功能开发（15天）

#### 模块1: 酒店商户管理（2天）

**Day 4-5**

- 酒店商户信息维护接口
- 酒店基础信息CRUD
- 酒店图片上传管理
- 酒店地理位置管理

**涉及文件：**

- `crmeb-service/src/main/java/com/zbkj/service/service/HotelMerchantService.java`
- `crmeb-admin/src/main/java/com/zbkj/admin/controller/HotelMerchantController.java`

#### 模块2: 房型管理（5天）

**Day 6-8: 房型基础管理**

- 房型信息CRUD（基于eb_product扩展）
- 房型分类管理
- 房型图片和设施管理
- 房型基础价格设置

**Day 9-10: 房型库存管理**

- 房态日历管理
- 库存批量设置
- 房态实时查询接口
- 库存预警功能

**涉及文件：**

- `crmeb-service/src/main/java/com/zbkj/service/service/HotelRoomService.java`
- `crmeb-service/src/main/java/com/zbkj/service/service/HotelInventoryService.java`
- `crmeb-admin/src/main/java/com/zbkj/admin/controller/HotelRoomController.java`

#### 模块3: 价格策略管理（3天）

**Day 11-13**

- 价格策略配置接口
- 周末价格设置
- 节假日价格设置
- 价格计算引擎
- 价格预览功能

**涉及文件：**

- `crmeb-service/src/main/java/com/zbkj/service/service/HotelPriceStrategyService.java`
- `crmeb-admin/src/main/java/com/zbkj/admin/controller/HotelPriceController.java`

#### 模块4: 预订流程（5天）

**Day 14-16: 前端预订接口**

- 酒店筛选接口
- 房型查询接口
- 房态查询接口
- 价格计算接口
- 预订下单接口

**Day 17-18: 订单管理**

- 预订订单管理
- 订单状态流转
- 核销码生成
- 订单确认功能

**涉及文件：**

- `crmeb-service/src/main/java/com/zbkj/service/service/HotelBookingService.java`
- `crmeb-front/src/main/java/com/zbkj/front/controller/HotelBookingController.java`
- `crmeb-admin/src/main/java/com/zbkj/admin/controller/HotelOrderController.java`

### 阶段三：高级功能开发（8天）

#### 模块5: 取消规则管理（3天）

**Day 19-21**

- 取消规则配置
- 取消费用计算
- 自动退款处理
- 取消规则验证

#### 模块6: 核销功能（2天）

**Day 22-23**

- 核销码生成算法
- 核销接口开发
- 核销记录管理
- 二维码生成

#### 模块7: 统计报表（3天）

**Day 24-26**

- 酒店经营数据统计
- 房态统计报表
- 收入统计分析
- 预订趋势分析

### 阶段四：测试与优化（2天）

#### Day 27-28

- 功能测试
- 性能优化
- 接口文档完善
- 部署准备

## 📁 项目文件结构

```
crmeb-service/
├── src/main/java/com/zbkj/service/
│   ├── dao/
│   │   ├── HotelRoomPriceStrategyDao.java
│   │   ├── HotelRoomInventoryDao.java
│   │   ├── HotelBookingExtendDao.java
│   │   └── HotelCancelRuleDao.java
│   ├── model/
│   │   ├── HotelRoomPriceStrategy.java
│   │   ├── HotelRoomInventory.java
│   │   ├── HotelBookingExtend.java
│   │   └── HotelCancelRule.java
│   └── service/
│       ├── HotelMerchantService.java
│       ├── HotelRoomService.java
│       ├── HotelInventoryService.java
│       ├── HotelPriceStrategyService.java
│       └── HotelBookingService.java

crmeb-admin/
└── src/main/java/com/zbkj/admin/controller/
    ├── HotelMerchantController.java
    ├── HotelRoomController.java
    ├── HotelPriceController.java
    └── HotelOrderController.java

crmeb-front/
└── src/main/java/com/zbkj/front/controller/
    └── HotelBookingController.java
```

## 🎯 关键技术点

### 1. 价格计算引擎

- 基础价格 + 策略价格的复合计算
- 多日期跨价格区间的组合计算
- 实时价格缓存机制

### 2. 房态管理

- 基于日期的库存管理
- 实时库存扣减和释放
- 超售预防机制

### 3. 订单状态管理

- 酒店订单特有的状态流转
- 自动确认和取消机制
- 核销码生成和验证

### 4. 分销集成

- 复用现有分销体系
- 酒店订单的佣金计算
- 分销推广链接生成

## ⚠️ 注意事项

1. **数据一致性**: 房态更新需要考虑并发控制
2. **价格计算**: 确保价格计算的准确性和性能
3. **订单状态**: 酒店订单状态与通用订单状态的兼容
4. **分销兼容**: 确保与现有分销系统的无缝集成

## 📊 预期成果

- 完整的酒店预订管理系统
- 灵活的价格策略配置
- 实时的房态管理
- 完善的订单流程
- 集成的分销推广功能

# SQL

```
-- ===================================================================
-- 金梭酒店项目数据库设计方案
-- 基于现有CRMEB系统扩展酒店预订功能
-- ===================================================================

-- ----------------------------
-- 1. 酒店房型价格策略表
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_room_price_strategy`;
CREATE TABLE `eb_hotel_room_price_strategy` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `product_id` int NOT NULL COMMENT '房型ID(关联eb_product)',
  `strategy_name` varchar(100) NOT NULL COMMENT '策略名称(如:周末价格,节假日价格)',
  `strategy_type` tinyint NOT NULL DEFAULT 1 COMMENT '策略类型:1-按星期,2-按日期范围,3-按具体日期',
  `week_days` varchar(20) DEFAULT '' COMMENT '适用星期(1,2,3,4,5,6,7 逗号分隔)',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `specific_dates` text COMMENT '具体日期列表(JSON格式)',
  `price_type` tinyint NOT NULL DEFAULT 1 COMMENT '价格类型:1-固定价格,2-基础价格倍数,3-基础价格增减',
  `price_value` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '价格值',
  `priority` int NOT NULL DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mer_product` (`mer_id`, `product_id`),
  KEY `idx_dates` (`start_date`, `end_date`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店房型价格策略表';

-- ----------------------------
-- 2. 酒店房态管理表
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_room_inventory`;
CREATE TABLE `eb_hotel_room_inventory` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `product_id` int NOT NULL COMMENT '房型ID(关联eb_product)',
  `inventory_date` date NOT NULL COMMENT '库存日期',
  `total_rooms` int NOT NULL DEFAULT 0 COMMENT '总房间数',
  `available_rooms` int NOT NULL DEFAULT 0 COMMENT '可用房间数',
  `booked_rooms` int NOT NULL DEFAULT 0 COMMENT '已预订房间数',
  `locked_rooms` int NOT NULL DEFAULT 0 COMMENT '锁定房间数(维修等)',
  `price` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '当日价格',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-停售,1-正常销售',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mer_product_date` (`mer_id`, `product_id`, `inventory_date`),
  KEY `idx_date` (`inventory_date`),
  KEY `idx_available` (`available_rooms`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店房态管理表';

-- ----------------------------
-- 3. 酒店预订扩展表
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_booking_extend`;
CREATE TABLE `eb_hotel_booking_extend` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '扩展ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号(关联eb_order)',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `product_id` int NOT NULL COMMENT '房型ID',
  `check_in_date` date NOT NULL COMMENT '入住日期',
  `check_out_date` date NOT NULL COMMENT '离店日期',
  `nights` int NOT NULL DEFAULT 1 COMMENT '入住天数',
  `room_count` int NOT NULL DEFAULT 1 COMMENT '房间数量',
  `guest_name` varchar(50) DEFAULT '' COMMENT '入住人姓名',
  `guest_phone` varchar(20) DEFAULT '' COMMENT '入住人手机号',
  `guest_id_card` varchar(20) DEFAULT '' COMMENT '入住人身份证号',
  `special_requirements` text COMMENT '特殊要求',
  `verify_code` varchar(12) DEFAULT '' COMMENT '核销码',
  `verify_status` tinyint NOT NULL DEFAULT 0 COMMENT '核销状态:0-未核销,1-已核销',
  `verify_time` timestamp NULL DEFAULT NULL COMMENT '核销时间',
  `verify_user_id` int DEFAULT 0 COMMENT '核销人员ID',
  `booking_status` tinyint NOT NULL DEFAULT 0 COMMENT '预订状态:0-待确认,1-已确认,2-已入住,3-已离店,4-已取消',
  `cancel_reason` varchar(255) DEFAULT '' COMMENT '取消原因',
  `cancel_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_mer_dates` (`mer_id`, `check_in_date`, `check_out_date`),
  KEY `idx_verify_code` (`verify_code`),
  KEY `idx_guest_phone` (`guest_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店预订扩展表';

-- ----------------------------
-- 4. 酒店取消规则表
-- ----------------------------
DROP TABLE IF EXISTS `eb_hotel_cancel_rule`;
CREATE TABLE `eb_hotel_cancel_rule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `mer_id` int NOT NULL COMMENT '商户ID(酒店ID)',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `advance_hours` int NOT NULL COMMENT '提前取消小时数',
  `penalty_type` tinyint NOT NULL DEFAULT 1 COMMENT '扣费类型:1-按比例,2-固定金额',
  `penalty_value` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '扣费值(比例或金额)',
  `min_advance_hours` int DEFAULT 0 COMMENT '最小提前取消小时数(小于此时间不可取消)',
  `description` varchar(255) DEFAULT '' COMMENT '规则描述',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序(按提前时间从大到小)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-禁用,1-启用',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mer_id` (`mer_id`),
  KEY `idx_advance_hours` (`advance_hours`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店取消规则表';

-- ----------------------------
-- 5. 现有表扩展字段(eb_product)
-- ----------------------------
ALTER TABLE `eb_product` 
ADD COLUMN `hotel_room_type` varchar(50) DEFAULT '' COMMENT '房型类型(标准间,大床房,套房等)' AFTER `system_form_id`,
ADD COLUMN `hotel_facilities` text COMMENT '房间设施(JSON格式)' AFTER `hotel_room_type`,
ADD COLUMN `hotel_area` decimal(5,2) DEFAULT 0.00 COMMENT '房间面积(平方米)' AFTER `hotel_facilities`,
ADD COLUMN `hotel_floor` varchar(20) DEFAULT '' COMMENT '楼层信息' AFTER `hotel_area`,
ADD COLUMN `hotel_bed_type` varchar(50) DEFAULT '' COMMENT '床型(单人床,双人床,大床等)' AFTER `hotel_floor`,
ADD COLUMN `hotel_max_guests` int DEFAULT 2 COMMENT '最大入住人数' AFTER `hotel_bed_type`,
ADD COLUMN `hotel_base_price` decimal(8,2) DEFAULT 0.00 COMMENT '房型基础价格' AFTER `hotel_max_guests`;

-- ----------------------------
-- 6. 现有表扩展字段(eb_order)
-- ----------------------------
ALTER TABLE `eb_order`
ADD COLUMN `hotel_check_in_date` date DEFAULT NULL COMMENT '入住日期' AFTER `order_extend`,
ADD COLUMN `hotel_check_out_date` date DEFAULT NULL COMMENT '离店日期' AFTER `hotel_check_in_date`,
ADD COLUMN `hotel_nights` int DEFAULT 0 COMMENT '入住天数' AFTER `hotel_check_out_date`,
ADD COLUMN `hotel_room_count` int DEFAULT 0 COMMENT '房间数量' AFTER `hotel_nights`;


```

