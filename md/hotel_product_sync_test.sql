/*
 酒店商品同步定时任务测试脚本
 
 功能说明：
 1. 测试数据准备
 2. 同步功能验证
 3. 数据完整性检查
 4. 性能测试查询
 
 创建时间: 2025-01-17
 版本: v1.0
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 测试数据准备
-- ----------------------------

-- 清理测试数据（可选）
-- DELETE FROM `eb_product` WHERE `name` LIKE '%测试酒店%' AND `type` = 2;
-- DELETE FROM `eb_hotel_room` WHERE `room_name` LIKE '%测试%';

-- 插入测试商户（如果不存在）
INSERT IGNORE INTO `eb_merchant` (`id`, `name`, `phone`, `address`, `latitude`, `longitude`, `star`, `avatar`, `cover_image`, `status`, `is_del`, `create_time`, `update_time`) VALUES
(999, '测试酒店', '13800138000', '测试地址123号', '39.908823', '116.397470', 5, '/static/images/test_hotel_avatar.jpg', '/static/images/test_hotel_cover.jpg', 1, 0, NOW(), NOW());

-- 插入测试酒店房间
INSERT IGNORE INTO `eb_hotel_room` (`id`, `mer_id`, `room_name`, `room_type`, `room_facilities`, `room_area`, `room_floor`, `bed_type`, `max_guests`, `base_price`, `total_rooms`, `room_images`, `room_description`, `max_booking_days`, `status`, `sort`, `is_del`, `create_id`, `create_time`, `update_time`) VALUES
(9991, 999, '测试豪华大床房', '大床房', '["免费WiFi", "空调", "电视", "独立卫浴", "吹风机"]', 35.00, '3-8楼', '大床(1.8m)', 2, 100.00, 10, '["test_room1.jpg", "test_room2.jpg"]', '测试用豪华大床房', 30, 1, 1, 0, 1, NOW(), NOW()),
(9992, 999, '测试标准双人间', '双人间', '["免费WiFi", "空调", "电视", "独立卫浴"]', 28.00, '2-6楼', '双人床(1.5m)', 2, 80.00, 15, '["test_room3.jpg", "test_room4.jpg"]', '测试用标准双人间', 30, 1, 2, 0, 1, NOW(), NOW());

-- 插入测试价格策略
INSERT IGNORE INTO `eb_hotel_room_price_strategy` (`id`, `mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`, `update_time`) VALUES
-- 测试豪华大床房价格策略
(99901, 999, 9991, '基础价格(工作日)', 1, '1,2,3,4,5', 100.00, 1, 1, 0, 1, NOW(), NOW()),
(99902, 999, 9991, '周末价格', 2, '6,7', 150.00, 2, 1, 0, 1, NOW(), NOW()),
(99903, 999, 9991, '节假日价格', 3, '', 300.00, 3, 1, 0, 1, NOW(), NOW()),
-- 测试标准双人间价格策略
(99904, 999, 9992, '基础价格(工作日)', 1, '1,2,3,4,5', 80.00, 1, 1, 0, 1, NOW(), NOW()),
(99905, 999, 9992, '周末价格', 2, '6,7', 120.00, 2, 1, 0, 1, NOW(), NOW()),
(99906, 999, 9992, '节假日价格', 3, '', 250.00, 3, 1, 0, 1, NOW(), NOW());

-- ----------------------------
-- 2. 同步前数据状态检查
-- ----------------------------

-- 检查测试房间数据
SELECT '=== 测试房间数据 ===' as section;
SELECT 
    hr.id,
    hr.room_name,
    hr.total_rooms,
    hr.status,
    COUNT(hps.id) as price_strategy_count
FROM `eb_hotel_room` hr
LEFT JOIN `eb_hotel_room_price_strategy` hps ON hr.id = hps.room_id AND hps.is_del = 0
WHERE hr.mer_id = 999 AND hr.is_del = 0
GROUP BY hr.id, hr.room_name, hr.total_rooms, hr.status;

-- 检查价格策略数据
SELECT '=== 价格策略数据 ===' as section;
SELECT 
    hps.room_id,
    hr.room_name,
    hps.strategy_name,
    hps.strategy_type,
    hps.price_value,
    hps.priority,
    hps.status
FROM `eb_hotel_room_price_strategy` hps
JOIN `eb_hotel_room` hr ON hps.room_id = hr.id
WHERE hps.mer_id = 999 AND hps.is_del = 0
ORDER BY hps.room_id, hps.priority;

-- 检查同步前商品数量
SELECT '=== 同步前商品统计 ===' as section;
SELECT 
    COUNT(*) as total_hotel_products,
    COUNT(CASE WHEN mer_id = 999 THEN 1 END) as test_merchant_products
FROM `eb_product` 
WHERE `type` = 2 AND `delivery_method` = '2' AND `is_del` = 0;

-- ----------------------------
-- 3. 分类检查
-- ----------------------------

-- 检查平台分类
SELECT '=== 平台分类检查 ===' as section;
SELECT 
    id,
    name,
    level,
    sort,
    is_show,
    create_time
FROM `eb_product_category` 
WHERE `name` = '酒店预订' AND `is_del` = 0;

-- 检查商户分类
SELECT '=== 商户分类检查 ===' as section;
SELECT 
    mpc.id,
    mpc.mer_id,
    m.name as merchant_name,
    mpc.name as category_name,
    mpc.sort,
    mpc.is_show,
    mpc.create_time
FROM `eb_merchant_product_category` mpc
JOIN `eb_merchant` m ON mpc.mer_id = m.id
WHERE mpc.name = '酒店预订' AND mpc.is_del = 0 AND mpc.mer_id = 999;

-- ----------------------------
-- 4. 模拟定时任务执行（手动测试）
-- ----------------------------

-- 注意：以下查询仅用于验证逻辑，实际同步需要通过Java代码执行

-- 模拟生成商品名称
SELECT '=== 模拟商品生成 ===' as section;
SELECT 
    hr.id as room_id,
    m.name as hotel_name,
    hr.room_name,
    DATE_ADD(CURDATE(), INTERVAL seq.n DAY) as check_in_date,
    CONCAT(m.name, '-', hr.room_name, '-', DATE_ADD(CURDATE(), INTERVAL seq.n DAY)) as product_name,
    CASE DAYOFWEEK(DATE_ADD(CURDATE(), INTERVAL seq.n DAY))
        WHEN 1 THEN '周日'
        WHEN 2 THEN '周一' 
        WHEN 3 THEN '周二'
        WHEN 4 THEN '周三'
        WHEN 5 THEN '周四'
        WHEN 6 THEN '周五'
        WHEN 7 THEN '周六'
    END as day_of_week,
    CASE 
        WHEN DAYOFWEEK(DATE_ADD(CURDATE(), INTERVAL seq.n DAY)) IN (1, 7) THEN '周末'
        ELSE '工作日'
    END as date_type
FROM `eb_hotel_room` hr
JOIN `eb_merchant` m ON hr.mer_id = m.id
CROSS JOIN (
    SELECT 1 as n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 
    UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10
) seq
WHERE hr.mer_id = 999 AND hr.is_del = 0 AND hr.status = 1
ORDER BY hr.id, seq.n
LIMIT 20;

-- ----------------------------
-- 5. 同步后验证查询（执行同步任务后运行）
-- ----------------------------

-- 验证商品生成情况
SELECT '=== 同步后商品验证 ===' as section;
SELECT 
    p.id,
    p.name,
    p.price,
    p.stock,
    p.cate_id,
    p.category_id,
    p.type,
    p.delivery_method,
    p.is_show,
    p.create_time
FROM `eb_product` p
WHERE p.mer_id = 999 
  AND p.type = 2 
  AND p.delivery_method = '2'
  AND p.is_del = 0
  AND p.name LIKE '%测试%'
ORDER BY p.name
LIMIT 10;

-- 验证商品属性值
SELECT '=== 商品属性值验证 ===' as section;
SELECT 
    pav.id,
    pav.product_id,
    p.name as product_name,
    pav.sku,
    pav.price,
    pav.stock,
    pav.brokerage,
    pav.brokerage_two,
    pav.type
FROM `eb_product_attr_value` pav
JOIN `eb_product` p ON pav.product_id = p.id
WHERE p.mer_id = 999 
  AND p.type = 2 
  AND p.name LIKE '%测试%'
  AND pav.is_del = 0
ORDER BY p.name
LIMIT 10;

-- 验证商品详情
SELECT '=== 商品详情验证 ===' as section;
SELECT 
    pd.id,
    pd.product_id,
    p.name as product_name,
    pd.type,
    LENGTH(pd.description) as description_length,
    SUBSTRING(pd.description, 1, 100) as description_preview
FROM `eb_product_description` pd
JOIN `eb_product` p ON pd.product_id = p.id
WHERE p.mer_id = 999 
  AND p.type = 2 
  AND p.name LIKE '%测试%'
ORDER BY p.name
LIMIT 5;

-- ----------------------------
-- 6. 性能测试查询
-- ----------------------------

-- 查询性能测试
SELECT '=== 性能测试 ===' as section;

-- 测试酒店商品查询性能
EXPLAIN SELECT 
    p.id,
    p.name,
    p.price,
    p.stock
FROM `eb_product` p
WHERE p.mer_id = 999 
  AND p.type = 2 
  AND p.delivery_method = '2'
  AND p.is_del = 0
ORDER BY p.name;

-- 测试房间价格策略查询性能
EXPLAIN SELECT 
    hps.price_value,
    hps.priority
FROM `eb_hotel_room_price_strategy` hps
WHERE hps.room_id = 9991 
  AND hps.status = 1 
  AND hps.is_del = 0
ORDER BY hps.priority DESC;

-- ----------------------------
-- 7. 数据统计分析
-- ----------------------------

-- 酒店商品统计
SELECT '=== 酒店商品统计 ===' as section;
SELECT 
    COUNT(*) as total_products,
    COUNT(DISTINCT mer_id) as merchant_count,
    MIN(price) as min_price,
    MAX(price) as max_price,
    AVG(price) as avg_price,
    SUM(stock) as total_stock
FROM `eb_product` 
WHERE `type` = 2 AND `delivery_method` = '2' AND `is_del` = 0;

-- 按商户统计
SELECT '=== 按商户统计 ===' as section;
SELECT 
    p.mer_id,
    m.name as merchant_name,
    COUNT(p.id) as product_count,
    MIN(p.price) as min_price,
    MAX(p.price) as max_price,
    AVG(p.price) as avg_price,
    SUM(p.stock) as total_stock
FROM `eb_product` p
JOIN `eb_merchant` m ON p.mer_id = m.id
WHERE p.type = 2 AND p.delivery_method = '2' AND p.is_del = 0
GROUP BY p.mer_id, m.name
ORDER BY product_count DESC;

-- ----------------------------
-- 8. 清理测试数据（可选）
-- ----------------------------

-- 取消注释以下语句来清理测试数据
/*
-- 清理测试商品
DELETE FROM `eb_product_description` WHERE `product_id` IN (
    SELECT id FROM `eb_product` WHERE `mer_id` = 999 AND `type` = 2
);

DELETE FROM `eb_product_attr_value` WHERE `product_id` IN (
    SELECT id FROM `eb_product` WHERE `mer_id` = 999 AND `type` = 2
);

DELETE FROM `eb_product` WHERE `mer_id` = 999 AND `type` = 2;

-- 清理测试房间和价格策略
DELETE FROM `eb_hotel_room_price_strategy` WHERE `mer_id` = 999;
DELETE FROM `eb_hotel_room` WHERE `mer_id` = 999;

-- 清理测试商户分类
DELETE FROM `eb_merchant_product_category` WHERE `mer_id` = 999;

-- 清理测试商户
DELETE FROM `eb_merchant` WHERE `id` = 999;

SELECT '测试数据清理完成' as cleanup_status;
*/

-- ----------------------------
-- 9. 测试结果总结
-- ----------------------------

SELECT '=== 测试完成 ===' as section;
SELECT 
    '请检查以上查询结果，确认：' as instruction,
    '1. 测试数据已正确插入' as step1,
    '2. 分类配置正确' as step2,
    '3. 同步任务执行成功' as step3,
    '4. 商品数据完整' as step4,
    '5. 性能查询正常' as step5;

SET FOREIGN_KEY_CHECKS = 1;
