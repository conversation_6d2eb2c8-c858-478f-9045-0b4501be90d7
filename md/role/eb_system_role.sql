/*
 Navicat Premium Data Transfer

 Source Server         : wzblcy.cn
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : wzblcy.cn:10179
 Source Schema         : java_mer_trip

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 18/07/2025 13:29:16
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for eb_system_role
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_role`;
CREATE TABLE `eb_system_role`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '身份管理id',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `role_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份管理名称',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份管理权限(menus_id)',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '管理员类型：1= 平台超管, 2=商户超管, 3=系统管理员，4=商户管理员',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '身份管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of eb_system_role
-- ----------------------------
INSERT INTO `eb_system_role` VALUES (1, 0, '超级管理员', '', 0, 1, 1, '2020-04-18 11:19:25', '2022-04-08 20:54:33');
INSERT INTO `eb_system_role` VALUES (2, 0, '商户超级管理员', '', 0, 1, 2, '2022-03-04 10:03:54', '2022-04-08 20:54:39');
INSERT INTO `eb_system_role` VALUES (4, 0, '管理员', '', 0, 1, 3, '2022-09-14 12:10:59', '2022-11-03 15:13:37');
INSERT INTO `eb_system_role` VALUES (5, 3, '店铺上货员', '', 0, 1, 4, '2022-10-21 11:18:56', '2022-10-21 11:18:56');
INSERT INTO `eb_system_role` VALUES (9, 0, '普通管理员', '', 0, 1, 3, '2022-11-03 12:16:08', '2022-11-03 14:23:33');
INSERT INTO `eb_system_role` VALUES (10, 0, '11', '', 0, 0, 3, '2025-05-15 09:21:54', '2025-05-15 09:21:54');
INSERT INTO `eb_system_role` VALUES (11, 0, '后台管理员', '', 0, 1, 3, '2025-06-16 11:44:26', '2025-06-16 11:44:26');

SET FOREIGN_KEY_CHECKS = 1;
