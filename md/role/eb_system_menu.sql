/*
 Navicat Premium Data Transfer

 Source Server         : wzblcy.cn
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : wzblcy.cn:10179
 Source Schema         : java_mer_trip

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 18/07/2025 13:28:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for eb_system_menu
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_menu`;
CREATE TABLE `eb_system_menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'icon',
  `perms` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `menu_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'M' COMMENT '类型，M-目录，C-菜单，A-按钮',
  `sort` int(11) NOT NULL DEFAULT 99999 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '显示状态',
  `is_delte` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除',
  `type` int(11) NOT NULL COMMENT '系统菜单类型：3-平台,4-商户',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of eb_system_menu
-- ----------------------------
INSERT INTO `eb_system_menu` VALUES (1, 0, '主页', 'menu', '', '/dashboard', 'C', 99999, 1, 0, 3, '2021-11-16 12:11:17', '2023-06-25 15:55:00');
INSERT INTO `eb_system_menu` VALUES (2, 0, '商品', 's-goods', '', '/product', 'M', 9998, 1, 0, 3, '2021-11-16 12:11:17', '2022-11-10 14:35:18');
INSERT INTO `eb_system_menu` VALUES (3, 0, '订单', 's-order', '', '/order', 'M', 9995, 1, 0, 3, '2021-11-16 12:11:17', '2022-11-10 14:37:04');
INSERT INTO `eb_system_menu` VALUES (4, 0, '人员管理', 'user-solid', '', '/user', 'M', 280, 1, 0, 3, '2021-11-16 12:11:17', '2025-06-24 17:36:48');
INSERT INTO `eb_system_menu` VALUES (5, 0, '财务', 's-finance', '', '/finance', 'M', 9980, 1, 0, 3, '2021-11-16 12:11:18', '2023-06-25 16:09:59');
INSERT INTO `eb_system_menu` VALUES (6, 0, '设置', 's-tools', '', '/operation', 'M', 9891, 1, 0, 3, '2021-11-16 12:11:18', '2023-06-25 16:08:03');
INSERT INTO `eb_system_menu` VALUES (7, 6, '系统维护', '', '', '/operation/maintain', 'M', 0, 1, 0, 3, '2021-11-16 12:11:18', '2024-04-18 15:33:12');
INSERT INTO `eb_system_menu` VALUES (8, 0, '登录管理', '', '', NULL, 'M', 9891, 0, 0, 3, '2021-12-03 16:31:43', '2023-06-25 16:08:13');
INSERT INTO `eb_system_menu` VALUES (9, 0, '分类服务(素材/设置/文章)', '', '', '', 'M', 9890, 0, 0, 3, '2021-12-03 17:15:29', '2023-06-25 16:08:29');
INSERT INTO `eb_system_menu` VALUES (10, 0, '公共服务', '', '', '', 'M', 9889, 0, 0, 3, '2021-12-08 18:56:36', '2023-06-25 16:08:36');
INSERT INTO `eb_system_menu` VALUES (11, 0, '商户', 's-shop', 'addShanghu', '/merchant', 'C', 9997, 1, 0, 3, '2022-03-14 12:57:51', '2023-08-01 17:06:23');
INSERT INTO `eb_system_menu` VALUES (14, 4, '管理权限', '', '', '/operation/roleManager', 'M', 9700, 1, 0, 3, '2021-11-16 17:23:37', '2025-06-24 17:37:47');
INSERT INTO `eb_system_menu` VALUES (15, 0, '装修', 's-home', '', '/page', 'M', 9990, 1, 0, 3, '2021-11-16 17:23:37', '2023-07-03 19:42:52');
INSERT INTO `eb_system_menu` VALUES (16, 7, '开发配置', '', '', '/operation/maintain/devconfiguration', 'M', 10, 1, 0, 3, '2021-11-16 17:38:38', '2024-02-27 09:19:05');
INSERT INTO `eb_system_menu` VALUES (17, 6, '物流设置', '', '', '/operation/logistics', 'M', 9500, 1, 0, 3, '2021-11-16 17:38:38', '2024-04-18 15:32:55');
INSERT INTO `eb_system_menu` VALUES (18, 2, '商品分类', '', '', '/product/category', 'C', 10, 1, 0, 3, '2021-11-16 12:19:02', '2024-04-18 15:28:41');
INSERT INTO `eb_system_menu` VALUES (19, 14, '角色管理', '', 'platform:admin:role:list', '/operation/roleManager/identityManager', 'C', 10, 1, 0, 3, '2021-11-16 17:30:23', '2024-02-24 10:26:32');
INSERT INTO `eb_system_menu` VALUES (20, 14, '管理员列表', '', 'platform:admin:list', '/operation/roleManager/adminList', 'C', 8, 1, 0, 3, '2021-11-16 17:30:23', '2024-02-24 10:26:39');
INSERT INTO `eb_system_menu` VALUES (21, 14, '权限规则', '', '', '/operation/roleManager/promiseRules', 'C', 0, 1, 0, 3, '2021-11-16 17:30:23', '2024-02-24 10:26:44');
INSERT INTO `eb_system_menu` VALUES (22, 15, '一键换色', '', '', '/page/design/theme', 'C', 900, 1, 0, 3, '2021-11-16 17:30:23', '2024-04-18 15:31:36');
INSERT INTO `eb_system_menu` VALUES (23, 15, '页面设计', '', '', '/page/design/viewDesign', 'C', 10, 1, 0, 3, '2021-11-16 17:30:23', '2024-04-18 15:31:41');
INSERT INTO `eb_system_menu` VALUES (24, 1074, '素材管理', '', 'platform:attachment:list', '/page/design/picture', 'C', 0, 1, 0, 3, '2021-11-16 17:38:38', '2025-06-24 17:45:37');
INSERT INTO `eb_system_menu` VALUES (25, 7, '商业授权', '', '', '/operation/maintain/authCRMEB', 'C', 8, 1, 0, 3, '2021-11-16 17:38:38', '2023-07-19 16:09:32');
INSERT INTO `eb_system_menu` VALUES (26, 16, '配置分类', '', '', '/operation/maintain/devconfiguration/configCategory', 'C', 10, 1, 0, 3, '2021-11-16 17:41:33', '2023-07-19 16:08:42');
INSERT INTO `eb_system_menu` VALUES (27, 16, '组合数据', '', 'platform:system:group:list', '/operation/maintain/devconfiguration/combineddata', 'C', 10, 1, 0, 3, '2021-11-16 17:41:33', '2023-07-19 16:08:49');
INSERT INTO `eb_system_menu` VALUES (28, 16, '表单配置', '', 'platform:system:form:list', '/operation/maintain/devconfiguration/formConfig', 'C', 10, 1, 0, 3, '2021-11-16 17:41:33', '2023-07-19 16:09:02');
INSERT INTO `eb_system_menu` VALUES (33, 6, '消息通知', '', '', '/operation/notification', 'C', 9701, 1, 0, 3, '2022-02-10 09:58:00', '2024-04-18 15:32:41');
INSERT INTO `eb_system_menu` VALUES (34, 2, '保障服务', '', '', '/product/guarantee', 'C', 10, 1, 0, 3, '2022-03-14 10:16:06', '2024-04-18 15:28:46');
INSERT INTO `eb_system_menu` VALUES (35, 11, '商户分类', '', '', '/merchant/classify', 'C', 100, 1, 0, 3, '2022-03-14 12:59:57', '2024-04-18 15:29:17');
INSERT INTO `eb_system_menu` VALUES (36, 11, '商户列表', '', '', '/merchant/list', 'C', 80, 1, 0, 3, '2022-03-14 13:01:10', '2024-04-18 15:29:25');
INSERT INTO `eb_system_menu` VALUES (37, 11, '商户菜单管理', '', '', '/merchant/system', 'C', 10, 1, 0, 3, '2022-03-14 13:02:12', '2024-04-18 15:29:42');
INSERT INTO `eb_system_menu` VALUES (38, 11, '商户入驻申请', '', '', '/merchant/application', 'C', 60, 1, 0, 3, '2022-03-14 13:02:55', '2024-04-18 15:29:36');
INSERT INTO `eb_system_menu` VALUES (40, 2, '品牌列表', '', '', '/product/brand', 'C', 10, 1, 0, 3, '2022-03-14 13:14:50', '2024-04-18 15:28:52');
INSERT INTO `eb_system_menu` VALUES (43, 2, '商品列表', '', '', '/product/list', 'C', 1, 1, 0, 3, '2022-03-07 17:26:31', '2024-04-18 15:28:57');
INSERT INTO `eb_system_menu` VALUES (44, 3, '订单列表', '', '', '/order/list', 'C', 1, 1, 0, 3, '2022-03-07 17:26:31', '2024-04-18 15:29:56');
INSERT INTO `eb_system_menu` VALUES (45, 3, '退款单', '', '', '/order/refund', 'C', 1, 1, 0, 3, '2022-03-07 17:26:31', '2024-04-18 15:30:03');
INSERT INTO `eb_system_menu` VALUES (46, 2, '商品评论', '', '', '/product/comment', 'C', 1, 1, 0, 3, '2022-03-07 17:26:31', '2024-04-18 15:29:02');
INSERT INTO `eb_system_menu` VALUES (48, 4, '用户列表', '', '', '/user/index', 'C', 10, 1, 0, 3, '2022-03-07 17:26:31', '2024-04-15 12:06:43');
INSERT INTO `eb_system_menu` VALUES (50, 287, '短信记录', 'wind-power', '', '/operation/messageRecord', 'C', 10, 1, 0, 3, '2022-04-01 17:10:30', '2023-08-01 17:01:38');
INSERT INTO `eb_system_menu` VALUES (51, 803, '资金流水', '', '', '/finance/journalAccount/capitalFlow', 'C', 10, 1, 0, 3, '2022-04-07 16:44:24', '2023-06-10 12:17:09');
INSERT INTO `eb_system_menu` VALUES (53, 5, '商户结算设置', '', '', '/finance/closingSetting', 'C', 10, 1, 0, 3, '2022-04-07 18:12:20', '2024-04-18 15:31:57');
INSERT INTO `eb_system_menu` VALUES (55, 4, '用户标签', '', '', '/user/label', 'C', 10, 1, 0, 3, '2022-04-21 22:03:11', '2024-04-15 12:06:49');
INSERT INTO `eb_system_menu` VALUES (56, 6, '平台设置', '', '', '/operation/setting', 'C', 9800, 1, 0, 3, '2022-04-22 17:03:59', '2024-04-18 15:32:31');
INSERT INTO `eb_system_menu` VALUES (57, 166, '入驻协议', '', '', '/merchant/type/accord', 'C', 1, 1, 0, 3, '2022-04-27 15:19:36', '2022-07-28 16:04:01');
INSERT INTO `eb_system_menu` VALUES (58, 11, '店铺类型', '', 'platform:merchant:type:list', '/merchant/type/list', 'C', 70, 1, 0, 3, '2022-04-27 15:26:16', '2024-04-18 15:29:31');
INSERT INTO `eb_system_menu` VALUES (59, 17, '物流公司', '', '', '/operation/logistics/companyList', 'C', 0, 1, 0, 3, '2022-05-05 10:30:21', '2023-07-19 15:53:36');
INSERT INTO `eb_system_menu` VALUES (60, 7, '敏感操作日志', '', '', '/operation/maintain/sensitiveLog', 'C', 0, 1, 0, 3, '2022-05-09 15:34:50', '2023-07-19 16:09:42');
INSERT INTO `eb_system_menu` VALUES (61, 6, '协议管理', '', '', '/operation/agreement', 'C', 9600, 1, 0, 3, '2022-06-13 10:10:52', '2024-04-18 15:32:50');
INSERT INTO `eb_system_menu` VALUES (62, 21, '平台端菜单列表', '', 'platform:menu:list', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:15:00', '2022-07-28 16:05:28');
INSERT INTO `eb_system_menu` VALUES (63, 21, '新增菜单', '', 'platform:menu:add', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:15:41', '2022-07-28 16:16:59');
INSERT INTO `eb_system_menu` VALUES (64, 21, '删除菜单', '', 'platform:menu:delete', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:15:58', '2022-07-28 16:16:59');
INSERT INTO `eb_system_menu` VALUES (65, 21, '修改菜单', '', 'platform:menu:update', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:16:15', '2022-07-28 16:16:59');
INSERT INTO `eb_system_menu` VALUES (66, 21, '菜单详情', '', 'platform:menu:info', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:16:24', '2022-07-28 16:17:00');
INSERT INTO `eb_system_menu` VALUES (67, 21, '修改菜单显示状态', '', 'platform:menu:show:status', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:16:34', '2022-07-28 16:17:00');
INSERT INTO `eb_system_menu` VALUES (68, 21, '菜单缓存树', '', 'platform:menu:cache:tree', NULL, 'A', 1, 1, 0, 3, '2022-03-07 11:16:45', '2022-07-28 16:17:00');
INSERT INTO `eb_system_menu` VALUES (69, 399, '无效关键词回复', '', '', '/operation/application/publicAccount/wxReply/replyIndex', 'C', 0, 1, 0, 3, '2022-07-25 16:36:49', '2023-07-19 15:54:17');
INSERT INTO `eb_system_menu` VALUES (70, 399, '微信菜单', '', '', '/operation/application/publicAccount/wxMenus', 'C', 0, 1, 0, 3, '2022-07-25 17:28:03', '2024-02-26 12:11:01');
INSERT INTO `eb_system_menu` VALUES (71, 398, '小程序', '', '', '/operation/application/publicRoutine', 'M', 0, 1, 0, 3, '2022-07-25 17:39:00', '2023-07-19 15:55:04');
INSERT INTO `eb_system_menu` VALUES (73, 0, '营销', 's-marketing', '', '/marketing', 'M', 9994, 1, 0, 3, '2022-07-25 17:41:12', '2023-06-25 16:06:55');
INSERT INTO `eb_system_menu` VALUES (74, 73, '积分', '', '', '/marketing/integral', 'M', 880, 1, 0, 3, '2022-07-25 17:41:39', '2024-04-18 15:30:30');
INSERT INTO `eb_system_menu` VALUES (75, 74, '积分配置', '', '', '/marketing/integral/integralconfig', 'C', 0, 1, 0, 3, '2022-07-25 17:42:44', '2022-07-28 16:20:38');
INSERT INTO `eb_system_menu` VALUES (76, 74, '积分日志', '', '', '/marketing/integral/integrallog', 'C', 0, 1, 0, 3, '2022-07-25 17:43:11', '2022-11-09 18:47:13');
INSERT INTO `eb_system_menu` VALUES (89, 0, '分销', 's-check', '', '/distribution', 'M', 9993, 1, 0, 3, '2022-07-25 17:58:27', '2023-06-25 16:07:10');
INSERT INTO `eb_system_menu` VALUES (90, 89, '分销配置', '', '', '/distribution/distributionconfig', 'C', 0, 1, 0, 3, '2022-07-25 17:58:51', '2024-04-18 15:31:14');
INSERT INTO `eb_system_menu` VALUES (91, 89, '分销员管理', '', '', '/distribution/index', 'C', 0, 1, 0, 3, '2022-07-25 17:59:12', '2024-04-18 15:31:20');
INSERT INTO `eb_system_menu` VALUES (93, 73, '公告内容', '', '', '/marketing/content', 'M', 600, 1, 0, 3, '2022-07-26 17:08:54', '2024-04-18 15:30:55');
INSERT INTO `eb_system_menu` VALUES (94, 93, '文章分类', '', '', '/marketing/content/classifManager', 'C', 9, 1, 0, 3, '2022-07-26 17:10:21', '2023-07-19 15:08:14');
INSERT INTO `eb_system_menu` VALUES (95, 93, '文章管理', '', '', '/marketing/content/articleManager', 'C', 0, 1, 0, 3, '2022-07-26 17:11:55', '2023-07-19 15:08:20');
INSERT INTO `eb_system_menu` VALUES (96, 94, '新增文章分类', '', 'platform:article:category:add', '', 'A', 0, 1, 0, 3, '2022-07-26 17:12:24', '2022-07-28 16:19:39');
INSERT INTO `eb_system_menu` VALUES (97, 94, '删除文章分类', '', 'platform:article:category:delete', '', 'A', 0, 1, 0, 3, '2022-07-26 17:12:44', '2022-07-28 16:19:39');
INSERT INTO `eb_system_menu` VALUES (98, 94, '修改文章分类', '', 'platform:article:category:update', '', 'A', 0, 1, 0, 3, '2022-07-26 17:12:58', '2022-07-28 16:19:39');
INSERT INTO `eb_system_menu` VALUES (99, 95, '文章新增', '', 'platform:article:save', '', 'A', 0, 1, 0, 3, '2022-07-26 17:13:16', '2022-07-28 16:19:46');
INSERT INTO `eb_system_menu` VALUES (100, 95, '删除文章', '', 'platform:article:delete', '', 'A', 0, 1, 0, 3, '2022-07-26 17:13:33', '2022-07-28 16:19:46');
INSERT INTO `eb_system_menu` VALUES (101, 95, '文章编辑', '', 'platform:article:update', '', 'A', 0, 1, 0, 3, '2022-07-26 17:13:47', '2022-07-28 16:19:46');
INSERT INTO `eb_system_menu` VALUES (102, 95, '文章详情', '', 'platform:article:info', '', 'A', 0, 1, 0, 3, '2022-07-26 17:14:01', '2022-07-28 16:19:46');
INSERT INTO `eb_system_menu` VALUES (103, 94, '文章分类开关', '', 'platform:article:category:switch', '', 'A', 0, 1, 0, 3, '2022-07-28 16:07:56', '2022-07-28 17:50:47');
INSERT INTO `eb_system_menu` VALUES (104, 5, '账单管理', '', '', '/finance/statement', 'C', 10, 1, 0, 3, '2022-04-08 19:25:19', '2024-04-18 15:32:03');
INSERT INTO `eb_system_menu` VALUES (105, 8, '平台端登出', '', 'platform:logout', '', 'A', 1, 1, 0, 3, '2022-03-03 16:12:33', '2022-07-28 16:56:22');
INSERT INTO `eb_system_menu` VALUES (106, 8, '平台端获取用户详情', '', 'platform:login:user:info', '', 'A', 1, 1, 0, 3, '2022-03-03 16:14:10', '2022-07-28 16:56:22');
INSERT INTO `eb_system_menu` VALUES (107, 8, '平台端获取管理员可访问目录', '', 'platform:login:menus', '', 'A', 1, 1, 0, 3, '2022-03-03 16:14:56', '2022-07-28 16:56:23');
INSERT INTO `eb_system_menu` VALUES (108, 8, '修改登录用户信息', '', 'platform:login:admin:update', '', 'A', 1, 1, 0, 3, '2022-04-24 10:31:28', '2022-04-24 10:31:53');
INSERT INTO `eb_system_menu` VALUES (109, 10, '表单模板详情', '', 'platform:system:form:info', '', 'A', 1, 1, 0, 3, '2021-12-07 09:33:10', '2022-07-28 16:26:09');
INSERT INTO `eb_system_menu` VALUES (110, 10, '根据key存储', '', 'platform:system:config:saveuniq', '', 'A', 1, 1, 0, 3, '2021-12-08 18:57:26', '2022-07-28 16:27:53');
INSERT INTO `eb_system_menu` VALUES (111, 10, '根据key获取', '', 'platform:system:config:getuniq', '', 'A', 1, 1, 0, 3, '2021-12-08 18:58:55', '2022-07-28 16:28:11');
INSERT INTO `eb_system_menu` VALUES (112, 10, '检测表单name是否存在', '', 'platform:system:config:check', '', 'A', 1, 1, 0, 3, '2021-12-13 10:30:58', '2022-07-28 16:27:50');
INSERT INTO `eb_system_menu` VALUES (113, 10, '整体保存Config表单数据', '', 'platform:system:config:save:form', '', 'A', 1, 1, 0, 3, '2021-12-13 10:31:24', '2022-07-28 16:29:30');
INSERT INTO `eb_system_menu` VALUES (114, 10, '更新配置信息', '', 'platform:system:config:update', '', 'A', 1, 1, 0, 3, '2021-12-13 10:31:44', '2022-07-28 16:28:33');
INSERT INTO `eb_system_menu` VALUES (115, 10, '组合数据详情', '', 'platform:system:group:info', '', 'A', 1, 1, 0, 3, '2021-12-13 10:32:28', '2022-07-28 16:29:00');
INSERT INTO `eb_system_menu` VALUES (116, 10, '加载表单配置详情', '', 'platform:system:config:info', '', 'A', 1, 1, 0, 3, '2022-04-12 14:26:15', '2022-07-28 16:28:57');
INSERT INTO `eb_system_menu` VALUES (118, 28, '表单添加', NULL, 'platform:system:form:save', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 18:04:44');
INSERT INTO `eb_system_menu` VALUES (119, 28, '表单修改', NULL, 'platform:system:form:update', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-11-09 18:43:48');
INSERT INTO `eb_system_menu` VALUES (120, 27, '数据组添加', NULL, 'platform:system:group:save', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 16:43:09');
INSERT INTO `eb_system_menu` VALUES (121, 27, '数据组修改', NULL, 'platform:system:group:update', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 16:44:01');
INSERT INTO `eb_system_menu` VALUES (122, 27, '数据组删除', NULL, 'platform:system:group:delete', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-11-07 10:43:52');
INSERT INTO `eb_system_menu` VALUES (123, 27, '组合数据添加', NULL, 'platform:system:group:data:save', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 16:44:23');
INSERT INTO `eb_system_menu` VALUES (124, 27, '组合数据修改', NULL, 'platform:system:group:data:update', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 16:44:43');
INSERT INTO `eb_system_menu` VALUES (125, 27, '组合数据删除', NULL, 'platform:system:group:data:delete', '', 'A', 1, 1, 0, 3, '2021-11-16 17:44:35', '2022-07-28 16:44:31');
INSERT INTO `eb_system_menu` VALUES (126, 27, '组合数据详情', '', 'platform:system:group:data:info', '', 'A', 1, 1, 0, 3, '2021-12-02 11:35:48', '2022-07-28 16:44:46');
INSERT INTO `eb_system_menu` VALUES (127, 27, '分页组合数据详情', '', 'platform:system:group:data:list', '', 'A', 1, 1, 0, 3, '2021-12-03 17:39:37', '2022-07-28 16:44:05');
INSERT INTO `eb_system_menu` VALUES (128, 35, '商户分类分页列表', '', 'platform:merchant:category:list', '', 'A', 1, 1, 0, 3, '2022-03-03 17:09:51', '2022-07-28 16:56:18');
INSERT INTO `eb_system_menu` VALUES (129, 35, '添加商户分类', '', 'platform:merchant:category:add', '', 'A', 1, 1, 0, 3, '2022-03-03 17:10:02', '2022-07-28 16:56:18');
INSERT INTO `eb_system_menu` VALUES (130, 35, '编辑商户分类', '', 'platform:merchant:category:update', '', 'A', 1, 1, 0, 3, '2022-03-03 17:10:06', '2022-07-28 16:56:18');
INSERT INTO `eb_system_menu` VALUES (131, 35, '删除商户分类', '', 'platform:merchant:category:delete', '', 'A', 1, 1, 0, 3, '2022-03-03 17:11:04', '2022-07-28 16:56:18');
INSERT INTO `eb_system_menu` VALUES (132, 35, '获取全部商户分类列表', '', 'platform:merchant:category:all', '', 'A', 1, 1, 0, 3, '2022-03-03 17:11:08', '2022-07-28 16:56:18');
INSERT INTO `eb_system_menu` VALUES (133, 58, '添加商户类型', '', 'platform:merchant:type:add', NULL, 'A', 1, 1, 0, 3, '2022-03-03 17:11:15', '2022-07-28 16:59:58');
INSERT INTO `eb_system_menu` VALUES (134, 58, '编辑类型类型', '', 'platform:merchant:type:update', NULL, 'A', 1, 1, 0, 3, '2022-03-03 17:11:23', '2022-07-28 16:59:58');
INSERT INTO `eb_system_menu` VALUES (135, 58, '删除商户类型', '', 'platform:merchant:type:delete', NULL, 'A', 1, 1, 0, 3, '2022-03-03 17:27:04', '2022-07-28 16:59:58');
INSERT INTO `eb_system_menu` VALUES (136, 58, '获取全部商户类型列表', '', 'platform:merchant:type:all', NULL, 'A', 1, 1, 0, 3, '2022-03-03 17:27:54', '2022-07-28 16:59:58');
INSERT INTO `eb_system_menu` VALUES (137, 38, '商户入驻分页列表', NULL, 'platform:merchant:apply:page:list', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:10', '2022-04-20 00:32:11');
INSERT INTO `eb_system_menu` VALUES (138, 38, '审核', NULL, 'platform:merchant:apply:audit', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:17', '2022-04-20 00:36:24');
INSERT INTO `eb_system_menu` VALUES (139, 38, '备注', NULL, 'platform:merchant:apply:remark', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:36:24');
INSERT INTO `eb_system_menu` VALUES (140, 36, '商户分页列表', NULL, 'platform:merchant:page:list', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:37:42');
INSERT INTO `eb_system_menu` VALUES (141, 36, '添加商户', NULL, 'platform:merchant:add', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:37:42');
INSERT INTO `eb_system_menu` VALUES (142, 36, '编辑商户', NULL, 'platform:merchant:update', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-05-14 14:35:25');
INSERT INTO `eb_system_menu` VALUES (143, 36, '重置商户密码', NULL, 'platform:merchant:reset:password', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-08-22 14:58:27');
INSERT INTO `eb_system_menu` VALUES (144, 36, '修改复制商品数量', NULL, 'platform:merchant:copy:prodcut:num', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:39:34');
INSERT INTO `eb_system_menu` VALUES (145, 36, '商户详情', NULL, 'platform:merchant:detail', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:37:43');
INSERT INTO `eb_system_menu` VALUES (146, 36, '推荐开关', NULL, 'platform:merchant:recommend:switch', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:39:44');
INSERT INTO `eb_system_menu` VALUES (147, 36, '关闭商户', NULL, 'platform:merchant:close', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:37:43');
INSERT INTO `eb_system_menu` VALUES (148, 36, '开启商户', NULL, 'platform:merchant:open', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:37:43');
INSERT INTO `eb_system_menu` VALUES (149, 36, '商户分页列表表头数量', NULL, 'platform:merchant:list:header:num', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-22 17:02:48');
INSERT INTO `eb_system_menu` VALUES (150, 37, '平台端商户菜单列表', NULL, 'platform:merchant:menu:list', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (151, 37, '新增菜单', NULL, 'platform:merchant:menu:add', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (152, 37, '删除菜单', NULL, 'platform:merchant:menu:delete', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (153, 37, '修改菜单', NULL, 'platform:merchant:menu:update', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (154, 37, '菜单详情', NULL, 'platform:merchant:menu:info', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (155, 37, '修改菜单显示状态', NULL, 'platform:merchant:menu:show:status', NULL, 'A', 1, 1, 0, 3, '2022-03-07 17:26:31', '2022-04-20 00:38:23');
INSERT INTO `eb_system_menu` VALUES (156, 20, '管理员添加', NULL, 'platform:admin:save', '', 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-05-10 17:12:01');
INSERT INTO `eb_system_menu` VALUES (157, 20, '管理员修改', NULL, 'platform:admin:update', '', 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-19 23:36:46');
INSERT INTO `eb_system_menu` VALUES (158, 20, '管理员删除', NULL, 'platform:admin:delete', '', 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-19 23:36:46');
INSERT INTO `eb_system_menu` VALUES (159, 20, '管理员状态更新', NULL, 'platform:admin:update:status', '', 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-19 23:36:46');
INSERT INTO `eb_system_menu` VALUES (160, 20, '管理员详情', '', 'platform:admin:info', '', 'A', 1, 1, 0, 3, '2021-12-06 11:04:41', '2022-04-19 23:36:46');
INSERT INTO `eb_system_menu` VALUES (161, 24, '删除素材', NULL, 'platform:attachment:delete', 'api/admin/system/attachment/delete', 'A', 1, 1, 0, 3, '2021-11-16 17:41:33', '2022-04-20 00:01:26');
INSERT INTO `eb_system_menu` VALUES (163, 24, '修改图片分组', '', 'platform:attachment:move', '', 'A', 1, 1, 0, 3, '2021-12-10 15:38:24', '2022-04-20 00:01:32');
INSERT INTO `eb_system_menu` VALUES (164, 24, '图片上传', '', 'platform:upload:image', '', 'A', 1, 1, 0, 3, '2022-04-06 17:13:36', '2022-04-20 00:01:33');
INSERT INTO `eb_system_menu` VALUES (165, 24, '文件上传', '', 'platform:upload:file', '', 'A', 1, 1, 0, 3, '2022-04-06 17:14:12', '2022-04-20 00:01:35');
INSERT INTO `eb_system_menu` VALUES (166, 19, '权限新增', NULL, 'platform:admin:role:save', NULL, 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-20 00:30:00');
INSERT INTO `eb_system_menu` VALUES (167, 19, '权限删除', NULL, 'platform:admin:role:delete', NULL, 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-20 00:30:00');
INSERT INTO `eb_system_menu` VALUES (168, 19, '权限更新', NULL, 'platform:admin:role:update', NULL, 'A', 1, 1, 0, 3, '2021-11-16 17:35:43', '2022-04-20 00:30:00');
INSERT INTO `eb_system_menu` VALUES (169, 19, '角色详情', NULL, 'platform:admin:role:info', NULL, 'A', 1, 1, 0, 3, '2021-12-03 16:39:41', '2022-04-20 00:27:02');
INSERT INTO `eb_system_menu` VALUES (170, 19, '修改角色状态', '', 'platform:admin:role:update:status', '', 'A', 1, 1, 0, 3, '2021-12-06 10:51:09', '2022-04-19 23:36:46');
INSERT INTO `eb_system_menu` VALUES (171, 0, '首页', 's-grid', '', '/dashboard', 'M', 9999, 1, 0, 4, '2022-03-14 17:02:46', '2022-04-24 10:50:50');
INSERT INTO `eb_system_menu` VALUES (172, 0, '商品', 's-goods', '', '/product', 'M', 6600, 1, 0, 4, '2022-03-14 17:05:29', '2023-07-01 10:40:13');
INSERT INTO `eb_system_menu` VALUES (173, 0, '订单', 's-order', '', '/order', 'M', 6100, 1, 0, 4, '2022-03-14 17:27:37', '2023-07-01 10:40:05');
INSERT INTO `eb_system_menu` VALUES (174, 0, '用户', 'user-solid', '', '/user', 'M', 5400, 1, 0, 4, '2022-03-14 17:33:47', '2023-07-01 10:39:58');
INSERT INTO `eb_system_menu` VALUES (175, 0, '财务', 's-finance', '', '/finance', 'M', 3500, 1, 0, 4, '2022-03-14 18:30:51', '2024-03-15 14:14:46');
INSERT INTO `eb_system_menu` VALUES (176, 0, '设置', 's-tools', '', '/operation', 'M', 1880, 1, 0, 4, '2022-03-26 11:29:43', '2024-02-26 10:46:06');
INSERT INTO `eb_system_menu` VALUES (177, 619, '优惠券', '', '', '/coupon', 'M', 9999, 1, 0, 4, '2022-03-28 11:10:10', '2023-07-01 11:05:10');
INSERT INTO `eb_system_menu` VALUES (178, 0, '维护', 's-help', '', '/maintain', 'M', 999, 1, 0, 4, '2022-04-01 09:18:45', '2024-02-26 10:46:25');
INSERT INTO `eb_system_menu` VALUES (179, 0, '公共', '', '', '', 'M', 3000, 0, 0, 4, '2022-04-20 12:16:30', '2023-07-01 10:39:24');
INSERT INTO `eb_system_menu` VALUES (180, 0, '登录管理', '', '', '', 'M', 2300, 0, 0, 4, '2022-04-21 00:50:52', '2023-07-01 10:39:16');
INSERT INTO `eb_system_menu` VALUES (181, 0, '基础分类(素材/设置/文章)', '', '', '', 'M', 2000, 0, 0, 4, '2022-04-21 01:02:22', '2023-07-01 10:39:06');
INSERT INTO `eb_system_menu` VALUES (182, 172, '商品管理', '', '', '/product/list', 'C', 12, 1, 0, 4, '2022-03-14 17:24:41', '2024-04-10 17:52:12');
INSERT INTO `eb_system_menu` VALUES (183, 172, '商品分类', '', '', '/product/classify', 'C', 10, 1, 0, 4, '2022-03-14 17:25:39', '2022-04-20 09:24:53');
INSERT INTO `eb_system_menu` VALUES (184, 172, '商品规格', '', '', '/product/attr', 'C', 10, 1, 0, 4, '2022-03-14 17:26:06', '2022-04-20 09:24:53');
INSERT INTO `eb_system_menu` VALUES (185, 172, '商品评价', '', '', '/product/comment', 'C', 10, 1, 0, 4, '2022-03-14 17:26:33', '2022-09-15 15:04:06');
INSERT INTO `eb_system_menu` VALUES (186, 173, '订单管理', '', '', '/order/list', 'C', 10, 1, 0, 4, '2022-03-14 17:33:00', '2022-04-20 09:25:02');
INSERT INTO `eb_system_menu` VALUES (187, 173, '退款单', '', '', '/order/refund', 'C', 10, 1, 0, 4, '2022-03-14 17:33:13', '2022-04-20 09:25:02');
INSERT INTO `eb_system_menu` VALUES (188, 174, '用户列表', '', '', '/user/index', 'C', 10, 1, 0, 4, '2022-03-14 17:36:23', '2022-04-20 00:13:29');
INSERT INTO `eb_system_menu` VALUES (189, 175, '资金流水', '', '', '/finance/capitalFlow', 'C', 10, 1, 0, 4, '2022-03-14 18:53:26', '2022-10-20 10:45:08');
INSERT INTO `eb_system_menu` VALUES (190, 175, '结算记录 ', '', '', '/finance/closingRecord', 'C', 10, 1, 0, 4, '2022-03-14 18:53:48', '2022-10-20 10:49:44');
INSERT INTO `eb_system_menu` VALUES (191, 175, '账单管理', '', '', '/finance/statement', 'C', 10, 1, 0, 4, '2022-03-14 18:55:22', '2022-10-20 10:45:13');
INSERT INTO `eb_system_menu` VALUES (192, 176, '商户基本设置', '', '', '/operation/modifyStoreInfo', 'C', 10, 1, 0, 4, '2022-03-26 17:19:23', '2022-04-20 09:25:30');
INSERT INTO `eb_system_menu` VALUES (193, 176, 'PC商城设置', '', '', '/operation/mallConfiguration', 'C', 10, 1, 0, 4, '2022-03-26 17:43:18', '2023-11-06 17:42:46');
INSERT INTO `eb_system_menu` VALUES (194, 176, '管理权限', '', '', '/operation/roleManager', 'C', 0, 1, 0, 4, '2022-04-24 16:01:28', '2022-04-24 16:01:28');
INSERT INTO `eb_system_menu` VALUES (195, 177, '优惠券列表', '', '', '/coupon/list', 'C', 10, 1, 0, 4, '2022-03-28 11:12:30', '2022-04-24 15:49:24');
INSERT INTO `eb_system_menu` VALUES (196, 177, '领取记录', '', '', '/coupon/record', 'C', 10, 1, 0, 4, '2022-03-28 11:13:42', '2022-07-28 17:27:18');
INSERT INTO `eb_system_menu` VALUES (197, 178, '素材管理', '', '', '/maintain/picture', 'C', 10, 1, 0, 4, '2022-03-28 15:58:34', '2022-04-20 09:27:27');
INSERT INTO `eb_system_menu` VALUES (198, 178, '敏感操作日志', '', '', '/maintain/sensitiveLog', 'C', 10, 1, 0, 4, '2022-04-01 09:23:52', '2022-07-28 17:27:18');
INSERT INTO `eb_system_menu` VALUES (199, 178, '物流管理', '', '', '/maintain/logistics', 'C', 1, 0, 0, 4, '2022-06-22 17:40:50', '2022-11-14 18:54:52');
INSERT INTO `eb_system_menu` VALUES (200, 179, '查询表单模板信息', '', '', '', 'A', 1, 0, 0, 4, '2022-03-26 11:57:15', '2022-07-28 17:27:30');
INSERT INTO `eb_system_menu` VALUES (201, 179, '文件上传', '', 'merchant:upload:file', '', 'A', 1, 1, 0, 4, '2022-04-09 11:09:52', '2022-04-21 01:04:21');
INSERT INTO `eb_system_menu` VALUES (202, 179, '图片上传', '', 'merchant:upload:image', '', 'A', 1, 1, 0, 4, '2022-04-09 11:10:34', '2022-04-21 01:04:27');
INSERT INTO `eb_system_menu` VALUES (203, 179, '表单组件详情', '', 'merchant:config:form:info', '', 'A', 1, 1, 0, 4, '2022-04-12 10:58:48', '2022-08-16 10:01:52');
INSERT INTO `eb_system_menu` VALUES (204, 179, '获取公共配置1getuniq', '', 'merchant:config:getuniq', '', 'A', 1, 1, 0, 4, '2022-04-20 12:17:05', '2022-08-16 09:58:28');
INSERT INTO `eb_system_menu` VALUES (205, 179, '获取公共配置2key', '', 'merchant:config:get', '', 'A', 1, 1, 0, 4, '2022-04-20 12:17:31', '2022-08-16 09:58:48');
INSERT INTO `eb_system_menu` VALUES (206, 180, '菜单缓存树', '', 'merchant:menu:cache:tree', '', 'A', 1, 1, 0, 4, '2022-03-07 11:17:18', '2022-07-28 17:27:26');
INSERT INTO `eb_system_menu` VALUES (207, 180, '登出', '', 'merchant:logout', '', 'A', 1, 1, 0, 4, '2022-03-07 11:17:23', '2022-07-28 17:27:26');
INSERT INTO `eb_system_menu` VALUES (208, 180, '获取管理员可访问目录', '', 'merchant:login:menus', '', 'A', 1, 1, 0, 4, '2022-03-07 11:17:46', '2022-07-28 17:27:26');
INSERT INTO `eb_system_menu` VALUES (209, 180, '修改登录用户信息', '', 'merchant:login:admin:update', '', 'A', 1, 1, 0, 4, '2022-04-24 10:37:32', '2022-04-24 10:37:48');
INSERT INTO `eb_system_menu` VALUES (210, 180, '商户端获取用户详情', '', 'merchant:login:user:info', '', 'A', 0, 1, 0, 4, '2022-05-16 15:54:43', '2022-05-16 15:54:43');
INSERT INTO `eb_system_menu` VALUES (211, 197, '素材列表', '', 'merchant:attachment:list', '', 'A', 1, 1, 0, 4, '2022-04-06 19:45:21', '2022-04-20 09:55:00');
INSERT INTO `eb_system_menu` VALUES (212, 197, '素材删除', '', 'merchant:attachment:delete', '', 'A', 1, 1, 0, 4, '2022-04-06 19:45:45', '2022-04-20 09:55:00');
INSERT INTO `eb_system_menu` VALUES (213, 197, '移动素材', '', 'merchant:attachment:move', '', 'A', 1, 1, 0, 4, '2022-04-06 19:46:08', '2022-04-20 09:55:00');
INSERT INTO `eb_system_menu` VALUES (214, 192, '商户端商户基础信息', NULL, 'merchant:base:info', NULL, 'A', 1, 1, 0, 4, '2022-03-07 17:26:31', '2022-04-20 10:04:43');
INSERT INTO `eb_system_menu` VALUES (215, 192, '商户端商户配置信息', NULL, 'merchant:config:info', NULL, 'A', 1, 1, 0, 4, '2022-03-07 17:26:31', '2022-04-20 10:04:43');
INSERT INTO `eb_system_menu` VALUES (216, 192, '商户端商户配置信息编辑', NULL, 'merchant:config:info:edit', NULL, 'A', 1, 1, 0, 4, '2022-03-07 17:26:31', '2022-04-20 10:04:43');
INSERT INTO `eb_system_menu` VALUES (217, 192, '商户端商户开关', NULL, 'merchant:switch:update', NULL, 'A', 1, 1, 0, 4, '2022-03-07 17:26:31', '2022-04-20 10:04:43');
INSERT INTO `eb_system_menu` VALUES (218, 194, '角色管理', '', '', '/operation/roleManager/identityManager', 'C', 10, 1, 0, 4, '2022-03-26 17:21:03', '2022-04-24 16:02:20');
INSERT INTO `eb_system_menu` VALUES (219, 194, '管理员列表', '', 'merchant:admin:list', '/operation/roleManager/adminList', 'C', 0, 1, 0, 4, '2022-04-24 15:59:06', '2022-05-05 10:12:09');
INSERT INTO `eb_system_menu` VALUES (220, 219, '新增后台管理员', '', 'merchant:admin:save', '', 'A', 0, 1, 0, 4, '2022-05-11 15:30:05', '2022-05-11 15:30:05');
INSERT INTO `eb_system_menu` VALUES (221, 219, '删除后台管理员', '', 'merchant:admin:delete', '', 'A', 0, 1, 0, 4, '2022-05-11 15:30:29', '2022-05-11 15:30:29');
INSERT INTO `eb_system_menu` VALUES (222, 219, '修改后台管理员', '', 'merchant:admin:update', '', 'A', 0, 1, 0, 4, '2022-05-11 15:30:45', '2022-05-11 15:30:45');
INSERT INTO `eb_system_menu` VALUES (223, 219, '后台管理员详情', '', 'merchant:admin:info', '', 'A', 0, 1, 0, 4, '2022-05-11 15:31:04', '2022-05-11 15:31:04');
INSERT INTO `eb_system_menu` VALUES (224, 219, '修改后台管理员状态', '', 'merchant:admin:update:status', '', 'A', 0, 1, 0, 4, '2022-05-11 15:31:18', '2022-05-11 15:31:18');
INSERT INTO `eb_system_menu` VALUES (225, 218, '角色列表', '', 'merchant:admin:role:list', '', 'A', 1, 1, 0, 4, '2022-04-07 20:40:35', '2022-04-21 01:03:43');
INSERT INTO `eb_system_menu` VALUES (226, 218, '创建角色', '', 'merchant:admin:role:save', '', 'A', 1, 1, 0, 4, '2022-04-07 20:41:06', '2022-04-21 01:03:52');
INSERT INTO `eb_system_menu` VALUES (227, 218, '删除角色', '', 'merchant:admin:role:delete', '', 'A', 1, 1, 0, 4, '2022-04-07 20:41:33', '2022-04-21 01:03:57');
INSERT INTO `eb_system_menu` VALUES (228, 218, '更新角色', '', 'merchant:admin:role:update', '', 'A', 1, 1, 0, 4, '2022-04-07 20:41:54', '2022-04-21 01:04:03');
INSERT INTO `eb_system_menu` VALUES (229, 218, '角色详情', '', 'merchant:admin:role:info', '', 'A', 1, 1, 0, 4, '2022-04-07 20:42:15', '2022-04-21 01:04:08');
INSERT INTO `eb_system_menu` VALUES (230, 218, '修改角色状态', '', 'merchant:admin:role:update:status', '', 'A', 1, 1, 0, 4, '2022-04-07 20:42:42', '2022-04-21 01:04:14');
INSERT INTO `eb_system_menu` VALUES (231, 18, '商品分类列表', '', 'platform:product:category:list', '', 'A', 0, 1, 0, 3, '2022-07-29 09:45:31', '2022-07-29 09:45:31');
INSERT INTO `eb_system_menu` VALUES (232, 18, '新增商品分类', '', 'platform:product:category:add', '', 'A', 0, 1, 0, 3, '2022-07-29 09:45:48', '2023-08-01 16:29:05');
INSERT INTO `eb_system_menu` VALUES (233, 18, '删除商品分类', '', 'platform:product:category:delete', '', 'A', 0, 1, 0, 3, '2022-07-29 09:46:01', '2022-07-29 09:46:01');
INSERT INTO `eb_system_menu` VALUES (234, 18, '修改商品分类', '', 'platform:product:category:update', '', 'A', 0, 1, 0, 3, '2022-07-29 09:46:19', '2022-07-29 09:46:19');
INSERT INTO `eb_system_menu` VALUES (235, 18, '修改商品分类显示状态', '', 'platform:product:category:show:status', '', 'A', 0, 1, 0, 3, '2022-07-29 09:46:32', '2022-07-29 09:46:32');
INSERT INTO `eb_system_menu` VALUES (236, 18, '商品分类缓存树', '', 'platform:product:category:cache:tree', '', 'A', 0, 1, 0, 3, '2022-07-29 09:46:50', '2022-07-29 09:46:50');
INSERT INTO `eb_system_menu` VALUES (237, 95, '文章列表', '', 'platform:article:list', '', 'A', 0, 1, 0, 3, '2022-07-29 09:59:24', '2022-07-29 09:59:24');
INSERT INTO `eb_system_menu` VALUES (238, 34, '保障服务列表', '', 'platform:product:guarantee:list', '', 'A', 0, 1, 0, 3, '2022-07-29 10:28:56', '2022-07-29 10:28:56');
INSERT INTO `eb_system_menu` VALUES (239, 34, '新增保障服务', '', 'platform:product:guarantee:add', '', 'A', 0, 1, 0, 3, '2022-07-29 10:29:12', '2022-07-29 10:29:12');
INSERT INTO `eb_system_menu` VALUES (240, 34, '删除保障服务', '', 'platform:product:guarantee:delete', '', 'A', 0, 1, 0, 3, '2022-07-29 10:29:26', '2022-07-29 10:29:26');
INSERT INTO `eb_system_menu` VALUES (241, 34, '修改保障服务', '', 'platform:product:guarantee:update', '', 'A', 0, 1, 0, 3, '2022-07-29 10:29:39', '2022-07-29 10:29:39');
INSERT INTO `eb_system_menu` VALUES (242, 34, '修改保障服务显示状态', '', 'platform:product:guarantee:show:status', '', 'A', 0, 1, 0, 3, '2022-07-29 10:29:55', '2022-07-29 10:29:55');
INSERT INTO `eb_system_menu` VALUES (243, 40, '品牌分页列表', '', 'platform:product:brand:list', '', 'A', 0, 1, 0, 3, '2022-07-29 10:34:49', '2022-07-29 10:34:49');
INSERT INTO `eb_system_menu` VALUES (244, 40, '新增品牌', '', 'platform:product:brand:add', '', 'A', 0, 1, 0, 3, '2022-07-29 10:35:10', '2022-07-29 10:35:10');
INSERT INTO `eb_system_menu` VALUES (245, 40, '删除品牌', '', 'platform:product:brand:delete', '', 'A', 0, 1, 0, 3, '2022-07-29 10:35:23', '2022-07-29 10:35:23');
INSERT INTO `eb_system_menu` VALUES (246, 40, '修改品牌', '', 'platform:product:brand:update', '', 'A', 0, 1, 0, 3, '2022-07-29 10:35:37', '2022-07-29 10:35:37');
INSERT INTO `eb_system_menu` VALUES (247, 40, '修改品牌显示状态', '', 'platform:product:brand:show:status', '', 'A', 0, 1, 0, 3, '2022-07-29 10:35:52', '2022-07-29 10:35:52');
INSERT INTO `eb_system_menu` VALUES (248, 40, '品牌缓存列表(全部)', '', 'platform:product:brand:cache:list', '', 'A', 0, 1, 0, 3, '2022-07-29 10:36:07', '2022-07-29 10:36:07');
INSERT INTO `eb_system_menu` VALUES (249, 9, '分页分类列表', '', 'platform:category:list', '', 'A', 0, 1, 0, 3, '2022-07-29 15:36:30', '2022-07-29 15:36:30');
INSERT INTO `eb_system_menu` VALUES (250, 9, '新增分类', '', 'platform:category:save', '', 'A', 0, 1, 0, 3, '2022-07-29 15:37:01', '2022-07-29 15:37:17');
INSERT INTO `eb_system_menu` VALUES (251, 9, '删除分类', '', 'platform:category:delete', '', 'A', 0, 1, 0, 3, '2022-07-29 15:37:32', '2022-07-29 15:37:32');
INSERT INTO `eb_system_menu` VALUES (252, 9, '修改分类', '', 'platform:category:update', '', 'A', 0, 1, 0, 3, '2022-07-29 15:37:47', '2022-07-29 15:37:47');
INSERT INTO `eb_system_menu` VALUES (253, 9, '分类详情', '', 'platform:category:info', '', 'A', 0, 1, 0, 3, '2022-07-29 15:38:00', '2022-07-29 15:38:00');
INSERT INTO `eb_system_menu` VALUES (254, 9, '获取分类tree结构的列表', '', 'platform:category:list:tree', '', 'A', 0, 1, 0, 3, '2022-07-29 15:38:17', '2022-07-29 15:38:17');
INSERT INTO `eb_system_menu` VALUES (255, 9, '根据id集合获取分类列表', '', 'platform:category:list:ids', '', 'A', 0, 1, 0, 3, '2022-07-29 15:38:33', '2022-07-29 15:38:33');
INSERT INTO `eb_system_menu` VALUES (256, 9, '更改分类状态', '', 'platform:category:update:status', '', 'A', 0, 1, 0, 3, '2022-07-29 15:38:45', '2022-07-29 15:38:45');
INSERT INTO `eb_system_menu` VALUES (257, 176, '运费模板', '', '', '/operation/freightSet', 'C', 1, 1, 0, 4, '2022-08-01 12:20:46', '2022-08-04 14:39:28');
INSERT INTO `eb_system_menu` VALUES (258, 257, '运费模板分页列表', '', 'merchant:shipping:templates:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:21:09', '2022-08-01 12:21:09');
INSERT INTO `eb_system_menu` VALUES (259, 257, '新增运费模板', '', 'merchant:shipping:templates:save', '', 'A', 1, 1, 0, 4, '2022-08-01 12:21:30', '2022-08-01 12:21:30');
INSERT INTO `eb_system_menu` VALUES (260, 257, '删除运费模板', '', 'merchant:shipping:templates:delete', '', 'A', 1, 1, 0, 4, '2022-08-01 12:21:44', '2022-08-01 12:21:44');
INSERT INTO `eb_system_menu` VALUES (261, 257, '运费模板修改', '', 'merchant:shipping:templates:update', '', 'A', 1, 1, 0, 4, '2022-08-01 12:21:58', '2022-08-01 12:21:58');
INSERT INTO `eb_system_menu` VALUES (262, 257, '运费模板详情', '', 'merchant:shipping:templates:info', '', 'A', 1, 1, 0, 4, '2022-08-01 12:22:11', '2022-08-01 12:22:11');
INSERT INTO `eb_system_menu` VALUES (263, 183, '商户商品分类列表', '', 'merchant:product:category:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:25:35', '2022-08-01 12:25:35');
INSERT INTO `eb_system_menu` VALUES (265, 183, '新增商户商品分类', '', 'merchant:product:category:add', '', 'A', 1, 1, 0, 4, '2022-08-01 12:25:56', '2022-08-01 12:25:56');
INSERT INTO `eb_system_menu` VALUES (266, 183, '删除商户商品分类', '', 'merchant:product:category:delete', '', 'A', 1, 1, 0, 4, '2022-08-01 12:26:08', '2022-08-01 12:26:08');
INSERT INTO `eb_system_menu` VALUES (267, 183, '修改商户商品分类', '', 'merchant:product:category:update', '', 'A', 1, 1, 0, 4, '2022-08-01 12:26:22', '2022-08-01 12:26:22');
INSERT INTO `eb_system_menu` VALUES (268, 183, '修改商户商品分类显示状态', '', 'merchant:product:category:show:status', '', 'A', 1, 1, 0, 4, '2022-08-01 12:26:34', '2022-08-01 12:26:34');
INSERT INTO `eb_system_menu` VALUES (269, 183, '商户商品分类缓存树', '', 'merchant:product:category:cache:tree', '', 'A', 1, 1, 0, 4, '2022-08-01 12:26:47', '2022-08-01 12:26:47');
INSERT INTO `eb_system_menu` VALUES (270, 184, '商户端商品规格分页列表', '', 'merchant:product:rule:page:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:27:25', '2022-08-01 12:27:25');
INSERT INTO `eb_system_menu` VALUES (271, 184, '新增商品规格', '', 'merchant:product:rule:save', '', 'A', 1, 1, 0, 4, '2022-08-01 12:28:00', '2022-08-01 12:28:00');
INSERT INTO `eb_system_menu` VALUES (272, 184, '删除商品规格', '', 'merchant:product:rule:delete', '', 'A', 1, 1, 0, 4, '2022-08-01 12:28:12', '2022-08-01 12:28:12');
INSERT INTO `eb_system_menu` VALUES (273, 184, '修改商品规格', '', 'merchant:product:rule:update', '', 'A', 1, 1, 0, 4, '2022-08-01 12:28:25', '2022-08-01 12:28:25');
INSERT INTO `eb_system_menu` VALUES (274, 184, '商品规格详情', '', 'merchant:product:rule:info', '', 'A', 1, 1, 0, 4, '2022-08-01 12:28:36', '2022-08-01 12:28:36');
INSERT INTO `eb_system_menu` VALUES (275, 172, '商品品牌', '', '', '/', 'C', 1, 0, 0, 4, '2022-08-01 12:30:24', '2022-08-01 12:30:24');
INSERT INTO `eb_system_menu` VALUES (276, 275, '品牌分页列表', '', 'merchant:plat:product:brand:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:30:38', '2022-08-01 12:30:38');
INSERT INTO `eb_system_menu` VALUES (277, 275, '品牌缓存列表(全部)', '', 'merchant:plat:product:brand:cache:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:30:52', '2022-08-01 12:30:52');
INSERT INTO `eb_system_menu` VALUES (278, 172, '平台商品分类', '', '', '/', 'C', 1, 0, 0, 4, '2022-08-01 12:31:24', '2022-08-01 12:31:24');
INSERT INTO `eb_system_menu` VALUES (279, 278, '分类缓存树', '', 'merchant:plat:product:category:cache:tree', '', 'A', 1, 1, 0, 4, '2022-08-01 12:31:43', '2022-08-01 12:31:43');
INSERT INTO `eb_system_menu` VALUES (280, 172, '商品保障服务', '', '', '/', 'C', 1, 0, 0, 4, '2022-08-01 12:32:12', '2022-08-01 12:32:12');
INSERT INTO `eb_system_menu` VALUES (281, 280, '保障服务列表', '', 'merchant:plat:product:guarantee:list', '', 'A', 1, 1, 0, 4, '2022-08-01 12:32:25', '2022-08-01 12:32:25');
INSERT INTO `eb_system_menu` VALUES (284, 181, '基础分类分页列表', '', 'merchant:category:list', '', 'A', 1, 1, 0, 4, '2022-08-04 09:42:25', '2022-08-04 09:42:25');
INSERT INTO `eb_system_menu` VALUES (285, 181, '新增基础分类', '', 'merchant:category:save', '', 'A', 1, 1, 0, 4, '2022-08-04 09:42:42', '2022-08-04 09:42:42');
INSERT INTO `eb_system_menu` VALUES (286, 181, '删除基础分类', '', 'merchant:category:delete', '', 'A', 1, 1, 0, 4, '2022-08-04 09:42:55', '2022-08-04 09:42:55');
INSERT INTO `eb_system_menu` VALUES (287, 181, '修改基础分类', '', 'merchant:category:update', '', 'A', 1, 1, 0, 4, '2022-08-04 09:43:10', '2022-08-04 09:43:10');
INSERT INTO `eb_system_menu` VALUES (288, 181, '基础分类详情', '', 'merchant:category:info', '', 'A', 1, 1, 0, 4, '2022-08-04 09:43:23', '2022-08-04 09:43:23');
INSERT INTO `eb_system_menu` VALUES (289, 181, '获取基础分类tree结构的列表', '', 'merchant:category:list:tree', '', 'A', 1, 1, 0, 4, '2022-08-04 09:43:35', '2022-08-04 09:43:35');
INSERT INTO `eb_system_menu` VALUES (290, 181, '根据id集合获取基础分类列表', '', 'merchant:category:list:ids', '', 'A', 1, 1, 0, 4, '2022-08-04 09:43:48', '2022-08-04 09:43:48');
INSERT INTO `eb_system_menu` VALUES (291, 181, '更改基础分类状态', '', 'merchant:category:update:status', '', 'A', 1, 1, 0, 4, '2022-08-04 09:44:15', '2022-08-04 09:44:15');
INSERT INTO `eb_system_menu` VALUES (292, 179, '获取城市tree结构的列表', '', 'merchant:city:list:tree', '', 'A', 1, 1, 0, 4, '2022-08-04 14:40:50', '2022-08-16 15:47:35');
INSERT INTO `eb_system_menu` VALUES (293, 182, '商品分页列表', '', 'merchant:product:page:list', '', 'A', 1, 1, 0, 4, '2022-08-09 17:56:46', '2022-08-09 17:56:46');
INSERT INTO `eb_system_menu` VALUES (294, 182, '新增商品', '', 'merchant:product:save', '', 'A', 1, 1, 0, 4, '2022-08-09 17:57:03', '2022-08-09 17:57:03');
INSERT INTO `eb_system_menu` VALUES (295, 182, '商品修改', '', 'merchant:product:update', '', 'A', 1, 1, 0, 4, '2022-08-09 17:57:18', '2022-08-09 17:57:18');
INSERT INTO `eb_system_menu` VALUES (296, 182, '商品详情', '', 'merchant:product:info', '', 'A', 1, 1, 0, 4, '2022-08-09 17:57:31', '2022-08-09 17:57:31');
INSERT INTO `eb_system_menu` VALUES (297, 182, '删除商品', '', 'merchant:product:delete', '', 'A', 1, 1, 0, 4, '2022-08-10 12:19:18', '2022-08-10 12:19:18');
INSERT INTO `eb_system_menu` VALUES (298, 182, '恢复回收站商品', '', 'merchant:product:restore', '', 'A', 1, 1, 0, 4, '2022-08-10 12:19:32', '2022-08-10 12:19:32');
INSERT INTO `eb_system_menu` VALUES (299, 182, '商品表头数量', '', 'merchant:product:tabs:headers', '', 'A', 1, 1, 0, 4, '2022-08-10 12:19:52', '2022-08-10 12:19:52');
INSERT INTO `eb_system_menu` VALUES (300, 182, '商品上架', '', 'merchant:product:up', '', 'A', 1, 1, 0, 4, '2022-08-10 12:20:05', '2022-08-10 12:20:05');
INSERT INTO `eb_system_menu` VALUES (301, 182, '商品下架', '', 'merchant:product:down', '', 'A', 1, 1, 0, 4, '2022-08-10 12:20:18', '2022-08-10 12:20:18');
INSERT INTO `eb_system_menu` VALUES (303, 43, '商品分页列表', '', 'platform:product:page:list', '', 'A', 0, 1, 0, 3, '2022-08-10 12:21:00', '2022-08-10 12:21:00');
INSERT INTO `eb_system_menu` VALUES (304, 43, '商品表头数量', '', 'platform:product:tabs:headers', '', 'A', 0, 1, 0, 3, '2022-08-10 12:21:15', '2022-08-10 12:21:15');
INSERT INTO `eb_system_menu` VALUES (305, 43, '商品审核', '', 'platform:product:audit', '', 'A', 0, 1, 0, 3, '2022-08-10 12:21:28', '2022-08-10 12:21:28');
INSERT INTO `eb_system_menu` VALUES (306, 43, '商品详情', '', 'platform:product:info', '', 'A', 0, 1, 0, 3, '2022-08-10 12:21:41', '2022-08-10 12:21:41');
INSERT INTO `eb_system_menu` VALUES (307, 182, '快捷添加库存', '', 'merchant:product:quick:stock:add', '', 'A', 1, 1, 0, 4, '2022-08-11 15:15:18', '2022-08-11 15:15:18');
INSERT INTO `eb_system_menu` VALUES (308, 182, '商品免审编辑', '', 'merchant:product:review:free:edit', '', 'A', 1, 1, 0, 4, '2022-08-11 16:02:42', '2022-08-11 16:02:42');
INSERT INTO `eb_system_menu` VALUES (309, 172, '保障服务组合', '', '', '/product/guarantee/group', 'C', 1, 1, 0, 4, '2022-08-12 11:39:43', '2022-08-16 11:03:55');
INSERT INTO `eb_system_menu` VALUES (310, 309, '保障服务组合列表', '', 'merchant:product:guarantee:group:list', '', 'A', 1, 1, 0, 4, '2022-08-12 11:40:01', '2022-08-12 11:40:01');
INSERT INTO `eb_system_menu` VALUES (311, 309, '新增保障服务组合', '', 'merchant:product:guarantee:group:add', '', 'A', 1, 1, 0, 4, '2022-08-12 11:40:16', '2022-08-12 11:40:16');
INSERT INTO `eb_system_menu` VALUES (312, 309, '编辑保障服务组合', '', 'merchant:product:guarantee:group:edit', '', 'A', 1, 1, 0, 4, '2022-08-12 11:40:29', '2022-08-12 11:40:29');
INSERT INTO `eb_system_menu` VALUES (313, 309, '删除保障服务组合', '', 'merchant:product:guarantee:group:delete', '', 'A', 1, 1, 0, 4, '2022-08-12 11:40:42', '2022-08-12 11:40:42');
INSERT INTO `eb_system_menu` VALUES (314, 182, '商品提审', '', 'merchant:product:submit:audit', '', 'A', 1, 1, 0, 4, '2022-08-16 11:28:52', '2022-08-16 11:28:52');
INSERT INTO `eb_system_menu` VALUES (316, 179, '获取城市区域tree结构的列表', '', 'merchant:city:region:list:tree', '', 'A', 1, 1, 0, 4, '2022-08-16 15:47:53', '2022-08-16 15:47:53');
INSERT INTO `eb_system_menu` VALUES (317, 43, '商品编辑', '', 'platform:product:update', '', 'A', 0, 1, 0, 3, '2022-08-17 16:52:07', '2022-08-17 16:52:07');
INSERT INTO `eb_system_menu` VALUES (318, 43, '强制下架商品', '', 'platform:product:force:down', '', 'A', 0, 1, 0, 3, '2022-08-17 16:52:46', '2022-08-17 16:52:46');
INSERT INTO `eb_system_menu` VALUES (319, 195, '优惠券分页列表', '', 'merchant:coupon:page:list', '', 'A', 1, 1, 0, 4, '2022-08-18 11:52:36', '2022-08-18 11:52:36');
INSERT INTO `eb_system_menu` VALUES (320, 195, '新增优惠券', '', 'merchant:coupon:save', '', 'A', 1, 1, 0, 4, '2022-08-18 11:52:51', '2022-08-18 11:52:51');
INSERT INTO `eb_system_menu` VALUES (321, 195, '修改优惠券状态', '', 'merchant:coupon:update:status', '', 'A', 1, 1, 0, 4, '2022-08-18 11:53:06', '2022-08-18 11:53:06');
INSERT INTO `eb_system_menu` VALUES (322, 195, '优惠券详情', '', 'merchant:coupon:info', '', 'A', 1, 1, 0, 4, '2022-08-18 11:53:18', '2022-08-18 11:53:18');
INSERT INTO `eb_system_menu` VALUES (323, 195, '删除优惠券', '', 'merchant:coupon:delete', '', 'A', 1, 1, 0, 4, '2022-08-18 11:53:39', '2022-08-18 11:53:39');
INSERT INTO `eb_system_menu` VALUES (324, 195, '商品可用优惠券列表', '', 'merchant:coupon:product:usable:list', '', 'A', 1, 1, 0, 4, '2022-08-18 11:53:56', '2022-08-18 11:53:56');
INSERT INTO `eb_system_menu` VALUES (325, 195, '商品券关联商品编辑', '', 'merchant:coupon:product:join:edit', '', 'A', 1, 1, 0, 4, '2022-08-18 15:03:12', '2022-08-18 15:03:12');
INSERT INTO `eb_system_menu` VALUES (326, 192, '商户端商户结算信息', '', 'merchant:settlement:info', '', 'A', 1, 1, 0, 4, '2022-08-22 14:38:20', '2022-08-22 14:38:20');
INSERT INTO `eb_system_menu` VALUES (327, 192, '商户端商户结算信息编辑', '', 'merchant:settlement:info:edit', '', 'A', 1, 1, 0, 4, '2022-08-22 14:38:53', '2022-08-22 14:38:53');
INSERT INTO `eb_system_menu` VALUES (328, 36, '修改商户手机号', '', 'platform:merchant:update:phone', '', 'A', 0, 1, 0, 3, '2022-08-22 15:55:02', '2022-08-22 15:55:02');
INSERT INTO `eb_system_menu` VALUES (329, 6, '一号通', '', '', '/operation/onePass', 'M', 9400, 1, 0, 3, '2022-08-23 16:35:32', '2024-04-18 15:32:59');
INSERT INTO `eb_system_menu` VALUES (330, 343, '获取用户验证码', '', 'platform:one:pass:send:code', '', 'A', 0, 1, 0, 3, '2022-08-23 16:36:33', '2022-08-25 15:21:20');
INSERT INTO `eb_system_menu` VALUES (331, 343, '账号注册', '', 'platform:one:pass:register', '', 'A', 0, 1, 0, 3, '2022-08-23 16:36:50', '2022-08-25 15:21:50');
INSERT INTO `eb_system_menu` VALUES (332, 343, '一号通用户登录', '', 'platform:one:pass:login', '', 'A', 0, 1, 0, 3, '2022-08-23 16:37:04', '2022-08-25 15:21:50');
INSERT INTO `eb_system_menu` VALUES (333, 343, '是否已经登录', '', 'platform:one:pass:is:login', '', 'A', 0, 1, 0, 3, '2022-08-23 16:37:18', '2022-08-25 15:21:50');
INSERT INTO `eb_system_menu` VALUES (334, 343, '一号通用户信息', '', 'platform:one:pass:info', '', 'A', 0, 1, 0, 3, '2022-08-23 16:37:30', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (335, 343, '用户注销', '', 'platform:one:pass:logout', '', 'A', 0, 1, 0, 3, '2022-08-23 16:37:44', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (336, 343, '修改密码', '', 'platform:one:pass:update:password', '', 'A', 0, 1, 0, 3, '2022-08-23 16:38:05', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (337, 343, '修改手机号——验证账号密码', '', 'platform:one:pass:update:phone:validator', '', 'A', 0, 1, 0, 3, '2022-08-23 16:38:22', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (338, 343, '修改手机号', '', 'platform:one:pass:update:phone', '', 'A', 0, 1, 0, 3, '2022-08-23 16:38:36', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (339, 343, '套餐列表', '', 'platform:one:pass:meal:list', '', 'A', 0, 1, 0, 3, '2022-08-23 16:38:49', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (340, 343, '套餐购买', '', 'platform:one:pass:meal:code', '', 'A', 0, 1, 0, 3, '2022-08-23 16:39:05', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (341, 343, '服务开通', '', 'platform:one:pass:service:open', '', 'A', 0, 1, 0, 3, '2022-08-23 16:39:22', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (342, 343, '用量记录', '', 'platform:one:pass:user:record', '', 'A', 0, 1, 0, 3, '2022-08-23 16:39:37', '2022-08-25 15:21:51');
INSERT INTO `eb_system_menu` VALUES (343, 329, '一号通', '', '', '/operation/onePass/index', 'C', 1, 0, 0, 3, '2022-08-24 10:42:12', '2024-02-22 10:46:00');
INSERT INTO `eb_system_menu` VALUES (344, 329, '一号通短信', 'light-rain', '', '/', 'C', 0, 0, 0, 3, '2022-08-25 15:22:37', '2023-02-02 15:50:03');
INSERT INTO `eb_system_menu` VALUES (345, 344, '修改签名', '', 'platform:one:pass:sms:modify:sign', '', 'A', 0, 1, 0, 3, '2022-08-25 15:22:58', '2022-08-25 15:22:58');
INSERT INTO `eb_system_menu` VALUES (346, 344, '短信模板', '', 'platform:one:pass:sms:temps', '', 'A', 0, 1, 0, 3, '2022-08-25 15:23:11', '2022-08-25 15:23:11');
INSERT INTO `eb_system_menu` VALUES (347, 344, '申请短信模板', '', 'platform:one:pass:sms:temp:apply', '', 'A', 0, 1, 0, 3, '2022-08-25 15:23:26', '2022-08-25 15:23:26');
INSERT INTO `eb_system_menu` VALUES (348, 33, '系统通知列表', '', 'platform:system:notification:list', '', 'A', 0, 1, 0, 3, '2022-08-25 15:25:53', '2022-08-25 15:25:53');
INSERT INTO `eb_system_menu` VALUES (349, 33, '公众号模板开关', '', 'platform:system:notification:wechat:switch', '', 'A', 0, 1, 0, 3, '2022-08-25 15:26:09', '2022-08-25 15:26:09');
INSERT INTO `eb_system_menu` VALUES (350, 33, '小程序订阅模板开关', '', 'platform:system:notification:routine:switch', '', 'A', 0, 1, 0, 3, '2022-08-25 15:26:21', '2022-08-25 15:26:21');
INSERT INTO `eb_system_menu` VALUES (351, 33, '发送短信开关', '', 'platform:system:notification:sms:switch', '', 'A', 0, 1, 0, 3, '2022-08-25 15:26:34', '2022-08-25 15:26:34');
INSERT INTO `eb_system_menu` VALUES (352, 33, '通知详情', '', 'platform:system:notification:detail', '', 'A', 0, 1, 0, 3, '2022-08-25 15:26:46', '2022-08-25 15:26:46');
INSERT INTO `eb_system_menu` VALUES (353, 33, '修改通知', '', 'platform:system:notification:update', '', 'A', 0, 1, 0, 3, '2022-08-25 15:26:58', '2022-08-25 15:26:58');
INSERT INTO `eb_system_menu` VALUES (354, 60, '敏感操作日志分页列表', '', 'platform:log:sensitive:list', '', 'A', 0, 1, 0, 3, '2022-08-26 16:28:36', '2022-08-26 16:28:36');
INSERT INTO `eb_system_menu` VALUES (355, 198, '敏感操作日志分页列表', '', 'merchant:log:sensitive:list', '', 'A', 1, 1, 0, 4, '2022-08-26 16:29:22', '2022-08-26 16:29:22');
INSERT INTO `eb_system_menu` VALUES (356, 55, '用户标签分页列表', '', 'platform:user:tag:list', '', 'A', 0, 1, 0, 3, '2022-09-01 15:51:49', '2022-09-01 15:51:49');
INSERT INTO `eb_system_menu` VALUES (357, 55, '新增用户标签', '', 'platform:user:tag:save', '', 'A', 0, 1, 0, 3, '2022-09-01 15:52:07', '2022-09-01 15:52:07');
INSERT INTO `eb_system_menu` VALUES (358, 55, '删除用户标签', '', 'platform:user:tag:delete', '', 'A', 0, 1, 0, 3, '2022-09-01 15:52:19', '2022-09-01 15:52:19');
INSERT INTO `eb_system_menu` VALUES (359, 55, '修改用户标签', '', 'platform:user:tag:update', '', 'A', 0, 1, 0, 3, '2022-09-01 15:52:30', '2022-09-01 15:52:30');
INSERT INTO `eb_system_menu` VALUES (360, 55, '用户标签全部列表', '', 'platform:user:tag:all:list', '', 'A', 0, 1, 0, 3, '2022-09-01 15:52:45', '2022-09-01 15:52:45');
INSERT INTO `eb_system_menu` VALUES (361, 48, '平台端用户分页列表', '', 'platform:user:page:list', '', 'A', 0, 1, 0, 3, '2022-09-01 15:53:15', '2022-09-01 15:53:15');
INSERT INTO `eb_system_menu` VALUES (362, 48, '修改用户信息', '', 'platform:user:update', '', 'A', 0, 1, 0, 3, '2022-09-01 15:53:28', '2022-09-01 15:53:28');
INSERT INTO `eb_system_menu` VALUES (363, 48, '用户分配标签', '', 'platform:user:tag', '', 'A', 0, 1, 0, 3, '2022-09-01 15:53:42', '2022-09-01 15:53:42');
INSERT INTO `eb_system_menu` VALUES (364, 4, '用户等级', '', '', '/user/level', 'C', 0, 1, 0, 3, '2022-09-03 15:41:11', '2024-04-15 12:06:53');
INSERT INTO `eb_system_menu` VALUES (365, 655, '系统用户等级分页列表', '', 'platform:system:user:level:list', '', 'A', 0, 1, 0, 3, '2022-09-03 15:41:28', '2023-03-06 14:56:49');
INSERT INTO `eb_system_menu` VALUES (366, 655, '新增系统用户等级', '', 'platform:system:user:level:save', '', 'A', 0, 1, 0, 3, '2022-09-03 15:41:41', '2023-03-06 14:56:58');
INSERT INTO `eb_system_menu` VALUES (367, 655, '删除系统用户等级', '', 'platform:system:user:level:delete', '', 'A', 0, 1, 0, 3, '2022-09-03 15:41:54', '2023-03-06 14:57:05');
INSERT INTO `eb_system_menu` VALUES (368, 655, '更新系统用户等级', '', 'platform:system:user:level:update', '', 'A', 0, 1, 0, 3, '2022-09-03 15:42:06', '2023-03-06 14:57:13');
INSERT INTO `eb_system_menu` VALUES (369, 655, '使用/禁用系统用户等级', '', 'platform:system:user:level:use', '', 'A', 0, 1, 0, 3, '2022-09-03 15:42:21', '2023-03-06 14:57:20');
INSERT INTO `eb_system_menu` VALUES (370, 75, '获取积分配置', '', 'platform:integral:get:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:11:45', '2022-09-06 15:11:45');
INSERT INTO `eb_system_menu` VALUES (371, 75, '编辑积分配置', '', 'platform:integral:update:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:12:02', '2022-09-06 15:12:02');
INSERT INTO `eb_system_menu` VALUES (372, 73, '签到', '', '/', '/marketing/sign', 'M', 900, 1, 0, 3, '2022-09-06 15:19:11', '2024-04-18 15:30:24');
INSERT INTO `eb_system_menu` VALUES (373, 374, '获取签到配置', '', 'platform:sign:get:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:19:46', '2022-09-06 15:20:30');
INSERT INTO `eb_system_menu` VALUES (374, 372, '签到配置', '', '/', '/marketing/sign/config', 'C', 0, 1, 0, 3, '2022-09-06 15:20:16', '2022-09-06 16:21:56');
INSERT INTO `eb_system_menu` VALUES (375, 374, '新增连续签到配置', '', 'platform:sign:add:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:20:46', '2022-09-06 15:20:46');
INSERT INTO `eb_system_menu` VALUES (376, 374, '删除连续签到配置', '', 'platform:sign:delete:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:20:59', '2022-09-06 15:20:59');
INSERT INTO `eb_system_menu` VALUES (377, 374, '编辑基础签到配置', '', 'platform:sign:edit:base:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:21:11', '2022-09-06 15:21:11');
INSERT INTO `eb_system_menu` VALUES (378, 374, '编辑连续签到配置', '', 'platform:sign:edit:award:config', '', 'A', 0, 1, 0, 3, '2022-09-06 15:21:23', '2022-09-06 15:21:23');
INSERT INTO `eb_system_menu` VALUES (379, 372, '签到记录', '', '', '/marketing/sign/record', 'C', 0, 1, 0, 3, '2022-09-06 15:21:48', '2022-09-06 16:22:38');
INSERT INTO `eb_system_menu` VALUES (380, 379, '用户签到记录分页列表', '', 'platform:sign:user:record:list', '', 'A', 0, 1, 0, 3, '2022-09-06 15:22:09', '2022-09-06 15:22:09');
INSERT INTO `eb_system_menu` VALUES (381, 61, '用户注册协议 保存', '', 'platform:system:agreement:user:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:01:52', '2022-09-14 11:02:47');
INSERT INTO `eb_system_menu` VALUES (382, 61, '用户注册协议 详情', '', 'platform:system:agreement:user:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:03:33', '2022-09-14 11:03:33');
INSERT INTO `eb_system_menu` VALUES (383, 61, '商户入驻协议 保存', '', 'platform:system:agreement:merincomming:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:04:07', '2022-09-14 11:04:07');
INSERT INTO `eb_system_menu` VALUES (384, 61, '商户入驻协议 详情', '', 'platform:system:agreement:merincomming:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:04:31', '2022-09-14 11:04:31');
INSERT INTO `eb_system_menu` VALUES (385, 61, '用户隐私协议 保存', '', 'platform:system:agreement:userprivacy:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:04:53', '2022-09-14 11:04:53');
INSERT INTO `eb_system_menu` VALUES (386, 61, '用户隐私协议 详情', '', 'platform:system:agreement:userprivacy:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:05:15', '2022-09-14 11:05:15');
INSERT INTO `eb_system_menu` VALUES (387, 61, '用户注销协议 保存', '', 'platform:system:agreement:useraccountcancel:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:05:39', '2022-09-14 11:05:39');
INSERT INTO `eb_system_menu` VALUES (388, 61, '用户注销协议 详情', '', 'platform:system:agreement:useraccountcancel:info', 'platform:system:agreement:useraccountcancel:info', 'A', 0, 1, 0, 3, '2022-09-14 11:06:11', '2022-09-14 11:06:11');
INSERT INTO `eb_system_menu` VALUES (389, 61, '用户注销声明 保存', '', 'platform:system:agreement:useraccountcancelnotice:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:06:35', '2022-09-14 11:06:35');
INSERT INTO `eb_system_menu` VALUES (390, 61, '用户注销声明 详情', '', 'platform:system:agreement:useraccountcancelnotice:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:06:54', '2022-09-14 11:06:54');
INSERT INTO `eb_system_menu` VALUES (391, 61, '关于我们协议 保存', '', 'platform:system:agreement:aboutus:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:07:19', '2022-09-14 11:07:19');
INSERT INTO `eb_system_menu` VALUES (392, 61, '关于我们协议 详情', '', 'platform:system:agreement:aboutus:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:07:36', '2022-09-14 11:07:36');
INSERT INTO `eb_system_menu` VALUES (393, 61, '平台资质证明 保存', '', 'platform:system:agreement:intelligent:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:07:55', '2022-09-14 11:07:55');
INSERT INTO `eb_system_menu` VALUES (394, 61, '平台资质证明 详情', '', 'platform:system:agreement:intelligent:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:08:12', '2022-09-14 11:08:12');
INSERT INTO `eb_system_menu` VALUES (395, 61, '平台规则 保存', '', 'platform:system:agreement:platfromrule:save', '', 'A', 0, 1, 0, 3, '2022-09-14 11:08:32', '2022-09-14 11:08:32');
INSERT INTO `eb_system_menu` VALUES (396, 61, '平台规则 详情', '', 'platform:system:agreement:platfromrule:info', '', 'A', 0, 1, 0, 3, '2022-09-14 11:08:50', '2022-09-14 11:08:50');
INSERT INTO `eb_system_menu` VALUES (397, 10, '组合数据列表', '', 'platform:system:group:list', '', 'A', 0, 1, 0, 3, '2022-09-14 18:11:06', '2022-09-14 18:11:06');
INSERT INTO `eb_system_menu` VALUES (398, 6, '应用设置', '', '', '/operation/application', 'M', 9300, 1, 0, 3, '2022-09-15 16:39:17', '2024-04-18 15:33:06');
INSERT INTO `eb_system_menu` VALUES (399, 398, '公众号', '', '', '/operation/application/publicAccount', 'M', 99, 1, 0, 3, '2022-09-15 16:41:46', '2023-07-19 15:54:07');
INSERT INTO `eb_system_menu` VALUES (401, 399, '微信关注回复', '', '', '/operation/application/publicAccount/wxReply/follow', 'C', 0, 1, 0, 3, '2022-09-15 16:44:00', '2023-07-19 15:54:43');
INSERT INTO `eb_system_menu` VALUES (402, 48, '操作用户积分', '', 'platform:user:operate:integer', '', 'A', 0, 1, 0, 3, '2022-09-22 19:28:08', '2022-09-22 19:28:08');
INSERT INTO `eb_system_menu` VALUES (403, 48, '操作用户余额', '', 'platform:user:operate:balance', '', 'A', 0, 1, 0, 3, '2022-09-22 19:28:25', '2022-09-22 19:28:25');
INSERT INTO `eb_system_menu` VALUES (404, 95, '文章开关', '', 'platform:article:switch', '', 'A', 0, 1, 0, 3, '2022-09-27 11:59:20', '2022-09-27 11:59:20');
INSERT INTO `eb_system_menu` VALUES (405, 94, '文章分类列表', '', 'platform:article:category:list', '', 'A', 0, 1, 0, 3, '2022-09-27 11:59:43', '2022-09-27 11:59:43');
INSERT INTO `eb_system_menu` VALUES (406, 186, '商户端订单分页列表', '', 'merchant:order:page:list', '', 'A', 1, 1, 0, 4, '2022-10-08 14:28:23', '2022-10-08 14:28:23');
INSERT INTO `eb_system_menu` VALUES (407, 186, '获取订单各状态数量', '', 'merchant:order:status:num', '', 'A', 1, 1, 0, 4, '2022-10-08 14:28:37', '2022-10-08 14:28:37');
INSERT INTO `eb_system_menu` VALUES (408, 186, '商户删除订单', '', 'merchant:order:delete', '', 'A', 1, 1, 0, 4, '2022-10-08 14:28:53', '2022-10-08 14:28:53');
INSERT INTO `eb_system_menu` VALUES (409, 186, '商户备注订单', '', 'merchant:order:mark', '', 'A', 1, 1, 0, 4, '2022-10-08 14:29:06', '2022-10-08 14:29:06');
INSERT INTO `eb_system_menu` VALUES (410, 186, '订单详情', '', 'merchant:order:info', '', 'A', 1, 1, 0, 4, '2022-10-08 14:29:21', '2022-10-08 14:29:21');
INSERT INTO `eb_system_menu` VALUES (411, 186, '订单发货', '', 'merchant:order:send', '', 'A', 1, 1, 0, 4, '2022-10-08 14:29:34', '2022-10-08 14:29:34');
INSERT INTO `eb_system_menu` VALUES (412, 187, '商户端退款订单分页列表', '', 'merchant:refund:order:page:list', '', 'A', 1, 1, 0, 4, '2022-10-09 17:02:17', '2022-10-09 17:02:17');
INSERT INTO `eb_system_menu` VALUES (413, 187, '商户端获取退款订单各状态数量', '', 'merchant:refund:order:status:num', '', 'A', 1, 1, 0, 4, '2022-10-09 17:02:32', '2022-10-09 17:02:32');
INSERT INTO `eb_system_menu` VALUES (414, 187, '商户备注退款订单', '', 'merchant:refund:order:mark', '', 'A', 1, 1, 0, 4, '2022-10-09 17:02:49', '2022-10-09 17:02:49');
INSERT INTO `eb_system_menu` VALUES (417, 435, '待审核列表', '', '', '/marketing/videoChannel/draftList', 'C', 0, 1, 0, 3, '2022-10-09 17:38:33', '2023-07-19 14:59:00');
INSERT INTO `eb_system_menu` VALUES (425, 417, '草稿商品列表', '', 'platform:pay:component:product:draft:list', '', 'A', 0, 1, 0, 3, '2022-10-09 17:53:22', '2022-10-11 11:22:22');
INSERT INTO `eb_system_menu` VALUES (426, 417, '草稿详情', '', 'platform:pay:component:product:draft:info', '', 'A', 0, 1, 0, 3, '2022-10-09 17:53:55', '2022-10-11 11:21:19');
INSERT INTO `eb_system_menu` VALUES (428, 435, '申请接入', '', '', '/marketing/videoChannel/apply', 'C', 0, 1, 0, 3, '2022-10-09 17:55:49', '2023-07-19 14:59:08');
INSERT INTO `eb_system_menu` VALUES (429, 428, '接入申请', '', 'platform:pay:component:shop:register', '', 'A', 0, 1, 0, 3, '2022-10-09 17:57:15', '2022-10-09 17:57:15');
INSERT INTO `eb_system_menu` VALUES (430, 428, '接入状态', '', 'platform:pay:component:shop:register:check', '', 'A', 0, 1, 0, 3, '2022-10-09 17:57:38', '2022-10-09 17:57:38');
INSERT INTO `eb_system_menu` VALUES (431, 428, '完成接入', '', 'platform:pay:component:shop:register:finish', '', 'A', 0, 1, 0, 3, '2022-10-09 17:58:01', '2022-10-09 17:58:01');
INSERT INTO `eb_system_menu` VALUES (432, 428, '场景接入申请', '', 'platform:pay:component:shop:register:scene', '', 'A', 0, 1, 0, 3, '2022-10-09 17:58:30', '2022-10-09 17:58:30');
INSERT INTO `eb_system_menu` VALUES (433, 90, '分销配置信息获取', '', 'platform:retail:store:config:get', '', 'A', 0, 1, 0, 3, '2022-10-10 11:22:47', '2022-10-10 11:22:47');
INSERT INTO `eb_system_menu` VALUES (434, 90, '分销配置信息保存', '', 'platform:retail:store:config:save', '', 'A', 0, 1, 0, 3, '2022-10-10 11:23:02', '2022-10-10 11:23:02');
INSERT INTO `eb_system_menu` VALUES (435, 73, '视频号', '', '', '/marketing/videoChannel', 'M', 800, 1, 0, 3, '2022-10-11 10:10:26', '2024-04-18 15:30:43');
INSERT INTO `eb_system_menu` VALUES (436, 435, '商品列表', '', '', '/marketing/videoChannel/list', 'C', 0, 1, 0, 3, '2022-10-11 10:15:24', '2023-07-19 14:59:18');
INSERT INTO `eb_system_menu` VALUES (437, 435, '接入前必须', '', '', '', 'M', 0, 0, 0, 3, '2022-10-11 10:48:44', '2023-06-25 16:29:53');
INSERT INTO `eb_system_menu` VALUES (438, 417, '获取类目详情', '', 'platform:pay:component:shop:cat:get', '', 'A', 0, 1, 0, 3, '2022-10-11 10:49:17', '2022-10-15 11:46:16');
INSERT INTO `eb_system_menu` VALUES (439, 437, '上传图片 到微信自定义组件换链接', '', 'platform:pay:component:shop:img:upload', '', 'A', 0, 1, 0, 3, '2022-10-11 10:49:49', '2022-10-11 10:49:49');
INSERT INTO `eb_system_menu` VALUES (440, 437, '上传品牌信息', '', 'platform:pay:component:shop:brand:audit', '', 'A', 0, 1, 0, 3, '2022-10-11 10:50:08', '2022-10-11 10:50:08');
INSERT INTO `eb_system_menu` VALUES (441, 437, '上传类目资质', '', 'platform:pay:component:shop:category:audit', '', 'A', 0, 1, 0, 3, '2022-10-11 10:50:28', '2022-10-11 10:50:28');
INSERT INTO `eb_system_menu` VALUES (442, 437, '查询审核结果', '', 'platform:pay:component:shop:audit:result', '', 'A', 0, 1, 0, 3, '2022-10-11 10:50:51', '2022-10-11 10:50:51');
INSERT INTO `eb_system_menu` VALUES (443, 437, '获取小程序提交过往的入驻资质信息', '', 'platform:pay:component:certificate', '', 'A', 0, 1, 0, 3, '2022-10-11 10:51:12', '2022-10-11 10:51:12');
INSERT INTO `eb_system_menu` VALUES (446, 417, '新增草稿', '', 'platform:pay:component:product:draft:add', '', 'A', 0, 1, 0, 3, '2022-10-11 11:54:19', '2022-10-11 11:54:19');
INSERT INTO `eb_system_menu` VALUES (447, 417, '更新草稿', '', 'platform:pay:component:product:draft:update', '', 'A', 0, 1, 0, 3, '2022-10-11 11:54:49', '2022-10-11 11:54:49');
INSERT INTO `eb_system_menu` VALUES (448, 417, '删除草稿商品', '', 'platform:pay:component:product:draft:delete', '', 'A', 0, 1, 0, 3, '2022-10-11 16:34:45', '2022-10-11 16:34:45');
INSERT INTO `eb_system_menu` VALUES (454, 619, '视频号', '', '', '/videoChannel', 'M', 6666, 1, 0, 4, '2022-10-11 20:20:23', '2023-07-01 11:05:27');
INSERT INTO `eb_system_menu` VALUES (455, 466, '添加草稿商品', '', 'merchant:pay:component:product:draft:add', '', 'A', 1, 1, 0, 4, '2022-10-11 20:20:53', '2022-10-11 20:54:38');
INSERT INTO `eb_system_menu` VALUES (456, 466, '编辑草稿商品', '', 'merchant:pay:component:product:draft:update', '', 'A', 1, 1, 0, 4, '2022-10-11 20:21:14', '2022-10-11 20:54:48');
INSERT INTO `eb_system_menu` VALUES (457, 466, '删除草稿商品', '', 'merchant:pay:component:product:draft:delete', '', 'A', 1, 1, 0, 4, '2022-10-11 20:21:34', '2022-10-11 20:54:55');
INSERT INTO `eb_system_menu` VALUES (458, 466, '商家审核草稿商品', '', 'merchant:pay:component:product:draft:review', '', 'A', 1, 1, 0, 4, '2022-10-11 20:22:06', '2022-10-11 20:55:09');
INSERT INTO `eb_system_menu` VALUES (459, 466, '草稿商品列表（分页）', '', 'merchant:pay:component:product:draft:list', '', 'A', 1, 1, 0, 4, '2022-10-11 20:22:39', '2022-10-11 20:55:18');
INSERT INTO `eb_system_menu` VALUES (460, 466, '草稿商品详情', '', 'merchant:pay:component:product:draft:info', '', 'A', 1, 1, 0, 4, '2022-10-11 20:23:02', '2022-10-11 20:55:26');
INSERT INTO `eb_system_menu` VALUES (461, 466, '获取类目详情', '', 'merchant:pay:component:shop:cat:get', '', 'A', 1, 1, 0, 4, '2022-10-11 20:23:26', '2022-10-11 20:55:32');
INSERT INTO `eb_system_menu` VALUES (462, 466, '上传图片 到微信自定义组件换链接', '', 'merchant:pay:component:shop:img:upload', '', 'A', 1, 1, 0, 4, '2022-10-11 20:23:45', '2022-10-11 20:55:38');
INSERT INTO `eb_system_menu` VALUES (463, 466, '品牌列表 分页', '', 'merchant:pay:component:shop:brand:list', '', 'A', 1, 1, 0, 4, '2022-10-11 20:24:09', '2022-10-11 20:55:45');
INSERT INTO `eb_system_menu` VALUES (464, 466, '品牌列表 不分页', '', 'merchant:pay:component:shop:brand:usable:list', '', 'A', 1, 1, 0, 4, '2022-10-11 20:24:30', '2022-10-11 20:55:51');
INSERT INTO `eb_system_menu` VALUES (465, 417, '审核草稿商品-平台角色操作', '', 'platform:pay:component:product:draft:review', '', 'A', 0, 1, 0, 3, '2022-10-11 20:26:26', '2022-10-11 20:26:26');
INSERT INTO `eb_system_menu` VALUES (466, 454, '草稿商品', '', '', '/videoChannel/draftList', 'C', 1, 1, 0, 4, '2022-10-11 20:54:07', '2022-10-11 20:54:07');
INSERT INTO `eb_system_menu` VALUES (467, 466, '获取类目列表', '', 'merchant:pay:component:cat:list', '', 'A', 1, 1, 0, 4, '2022-10-11 21:01:51', '2022-10-11 21:01:51');
INSERT INTO `eb_system_menu` VALUES (468, 187, '商户端退款订单详情', '', 'merchant:refund:order:detail', '', 'A', 1, 1, 0, 4, '2022-10-12 12:06:21', '2022-10-12 12:06:21');
INSERT INTO `eb_system_menu` VALUES (469, 45, '平台端退款订单分页列表', '', 'platform:refund:order:page:list', '', 'A', 0, 1, 0, 3, '2022-10-12 12:29:35', '2022-10-12 12:29:35');
INSERT INTO `eb_system_menu` VALUES (470, 45, '平台端获取退款订单各状态数量', '', 'platform:refund:order:status:num', '', 'A', 0, 1, 0, 3, '2022-10-12 12:29:50', '2022-10-12 12:29:50');
INSERT INTO `eb_system_menu` VALUES (471, 45, '平台端退款订单详情', '', 'platform:refund:order:detail', '', 'A', 0, 1, 0, 3, '2022-10-12 12:30:08', '2022-10-12 12:30:08');
INSERT INTO `eb_system_menu` VALUES (472, 45, '平台备注退款订单', '', 'platform:refund:order:mark', '', 'A', 0, 1, 0, 3, '2022-10-12 12:30:24', '2022-10-12 12:30:24');
INSERT INTO `eb_system_menu` VALUES (473, 44, '平台端订单分页列表', '', 'platform:order:page:list', '', 'A', 0, 1, 0, 3, '2022-10-12 16:04:13', '2022-10-12 16:04:13');
INSERT INTO `eb_system_menu` VALUES (474, 44, '平台端获取订单各状态数量', '', 'platform:order:status:num', '', 'A', 0, 1, 0, 3, '2022-10-12 16:04:38', '2022-10-12 16:04:38');
INSERT INTO `eb_system_menu` VALUES (475, 44, '平台端订单详情', '', 'platform:order:info', '', 'A', 0, 1, 0, 3, '2022-10-12 16:04:52', '2022-10-12 16:04:52');
INSERT INTO `eb_system_menu` VALUES (476, 186, '订单物流详情', '', 'merchant:order:logistics:info', '', 'A', 1, 1, 0, 4, '2022-10-12 17:08:44', '2022-10-12 17:08:44');
INSERT INTO `eb_system_menu` VALUES (477, 44, '平台端订单物流详情', '', 'platform:order:logistics:info', '', 'A', 0, 1, 0, 3, '2022-10-12 17:09:56', '2022-10-12 17:09:56');
INSERT INTO `eb_system_menu` VALUES (478, 454, '过审商品', '', '', '/videoChannel/productList', 'M', 1, 1, 0, 4, '2022-10-12 18:15:08', '2022-10-13 19:17:54');
INSERT INTO `eb_system_menu` VALUES (479, 478, '商户删除商品', '', 'merchant:pay:component:product:delete', '', 'A', 1, 1, 0, 4, '2022-10-12 18:15:34', '2022-10-12 18:16:32');
INSERT INTO `eb_system_menu` VALUES (480, 478, '商户上架商品', '', 'merchant:pay:component:product:puton', '', 'A', 1, 1, 0, 4, '2022-10-12 18:15:55', '2022-10-12 18:16:24');
INSERT INTO `eb_system_menu` VALUES (481, 478, '商户下架商品', '', 'merchant:pay:component:product:putdown', '', 'A', 1, 1, 0, 4, '2022-10-12 18:16:15', '2022-10-12 18:36:06');
INSERT INTO `eb_system_menu` VALUES (482, 478, '过审商品列表', '', 'merchant:pay:component:product:list', '', 'A', 1, 1, 0, 4, '2022-10-12 18:17:02', '2022-10-12 18:17:02');
INSERT INTO `eb_system_menu` VALUES (483, 478, '过审商品详情', '', 'merchant:pay:component:product:info', '', 'A', 1, 1, 0, 4, '2022-10-12 18:17:29', '2022-10-12 18:17:29');
INSERT INTO `eb_system_menu` VALUES (484, 182, '导入99Api商品', '', 'merchant:product:import:product', '', 'A', 1, 1, 0, 4, '2022-10-13 17:19:32', '2022-10-13 17:19:32');
INSERT INTO `eb_system_menu` VALUES (485, 182, '复制商品', '', 'merchant:product:copy:product', '', 'A', 1, 1, 0, 4, '2022-10-13 17:19:48', '2022-10-13 17:19:48');
INSERT INTO `eb_system_menu` VALUES (486, 182, '获取复制商品配置', '', 'admin:product:copy:config', '', 'A', 1, 1, 0, 4, '2022-10-13 18:01:43', '2022-10-13 18:01:43');
INSERT INTO `eb_system_menu` VALUES (487, 23, '页面首页', '', 'platform:page:layout:index', '', 'A', 0, 1, 0, 3, '2022-10-14 09:52:17', '2022-10-14 09:52:17');
INSERT INTO `eb_system_menu` VALUES (489, 23, '页面首页banner保存', '', 'platform:page:layout:index:banner:save', '', 'A', 0, 1, 0, 3, '2022-10-14 09:53:45', '2022-10-14 09:53:45');
INSERT INTO `eb_system_menu` VALUES (490, 23, '页面首页menu保存', '', 'platform:page:layout:index:menu:save', '', 'A', 0, 1, 0, 3, '2022-10-14 09:54:05', '2022-10-14 09:54:05');
INSERT INTO `eb_system_menu` VALUES (492, 23, '页面用户中心banner保存', '', 'platform:page:layout:index:banner:save', '', 'A', 0, 1, 0, 3, '2022-10-14 09:54:58', '2022-10-14 09:54:58');
INSERT INTO `eb_system_menu` VALUES (493, 23, '页面用户中心导航保存', '', 'platform:page:layout:user:menu:save', '', 'A', 0, 1, 0, 3, '2022-10-14 09:55:19', '2022-10-14 09:55:37');
INSERT INTO `eb_system_menu` VALUES (497, 686, '充值订单分页列表', '', 'platform:recharge:order:list', '', 'A', 0, 1, 0, 3, '2022-10-14 11:42:00', '2023-03-24 17:52:30');
INSERT INTO `eb_system_menu` VALUES (499, 435, '微信商品类目', '', '', '/marketing/videoChannel/weChatcategory', 'C', 0, 1, 0, 3, '2022-10-17 15:50:04', '2023-07-19 14:59:34');
INSERT INTO `eb_system_menu` VALUES (503, 5, '结算管理', '', '', '/finance/closing', 'M', 0, 1, 0, 3, '2022-10-17 19:27:06', '2024-04-18 15:32:09');
INSERT INTO `eb_system_menu` VALUES (504, 503, '商户结算', '', '', '/finance/closing/merchantClosing', 'C', 0, 1, 0, 3, '2022-10-17 19:27:26', '2022-10-20 15:50:04');
INSERT INTO `eb_system_menu` VALUES (505, 503, '用户结算', '', '', '/finance/closing/userClosing', 'C', 0, 1, 0, 3, '2022-10-17 19:27:41', '2022-10-20 15:06:18');
INSERT INTO `eb_system_menu` VALUES (506, 505, '用户结算分页列表', '', 'platform:finance:user:closing:page:list', '', 'A', 0, 1, 0, 3, '2022-10-17 19:27:57', '2022-10-17 19:27:57');
INSERT INTO `eb_system_menu` VALUES (507, 505, '用户结算申请审核', '', 'platform:finance:user:closing:audit', '', 'A', 0, 1, 0, 3, '2022-10-17 19:28:12', '2022-10-17 19:28:12');
INSERT INTO `eb_system_menu` VALUES (508, 505, '用户结算到账凭证', '', 'platform:finance:user:closing:proof', '', 'A', 0, 1, 0, 3, '2022-10-17 19:28:25', '2022-10-17 19:28:25');
INSERT INTO `eb_system_menu` VALUES (509, 505, '用户结算备注', '', 'platform:finance:user:closing:remark', '', 'A', 0, 1, 0, 3, '2022-10-17 19:28:42', '2022-10-17 19:28:42');
INSERT INTO `eb_system_menu` VALUES (510, 53, '获取商户结算设置', '', 'platform:finance:merchant:closing:config', '', 'A', 0, 1, 0, 3, '2022-10-17 21:00:16', '2022-10-17 21:00:16');
INSERT INTO `eb_system_menu` VALUES (511, 53, '编辑商户结算设置', '', 'platform:finance:merchant:closing:config:edit', '', 'A', 0, 1, 0, 3, '2022-10-17 21:00:32', '2022-10-17 21:00:32');
INSERT INTO `eb_system_menu` VALUES (512, 504, '商户结算分页列表', '', 'platform:finance:merchant:closing:page:list', '', 'A', 0, 1, 0, 3, '2022-10-17 21:01:07', '2022-10-17 21:01:07');
INSERT INTO `eb_system_menu` VALUES (513, 504, '商户结算记录详情', '', 'platform:finance:merchant:closing:detail', '', 'A', 0, 1, 0, 3, '2022-10-17 21:01:23', '2022-10-17 21:01:23');
INSERT INTO `eb_system_menu` VALUES (514, 504, '商户结算申请审核', '', 'platform:finance:merchant:closing:audit', '', 'A', 0, 1, 0, 3, '2022-10-17 21:01:55', '2022-10-19 17:27:31');
INSERT INTO `eb_system_menu` VALUES (515, 504, '商户结算到账凭证', '', 'platform:finance:merchant:closing:proof', '', 'A', 0, 1, 0, 3, '2022-10-17 21:02:09', '2022-10-19 17:27:41');
INSERT INTO `eb_system_menu` VALUES (516, 504, '商户结算备注', '', 'platform:finance:merchant:closing:remark', '', 'A', 0, 1, 0, 3, '2022-10-17 21:02:23', '2022-10-19 17:27:51');
INSERT INTO `eb_system_menu` VALUES (517, 190, '获取结算申请基础信息', '', 'merchant:finance:closing:base:info', '', 'A', 1, 1, 0, 4, '2022-10-18 14:46:55', '2022-10-18 14:47:57');
INSERT INTO `eb_system_menu` VALUES (518, 190, '结算申请', '', 'merchant:finance:closing:apply', '', 'A', 1, 1, 0, 4, '2022-10-18 14:47:11', '2022-10-18 14:48:05');
INSERT INTO `eb_system_menu` VALUES (519, 190, '结算记录分页列表', '', 'merchant:finance:closing:page:list', '', 'A', 1, 1, 0, 4, '2022-10-18 14:47:23', '2022-10-18 14:48:13');
INSERT INTO `eb_system_menu` VALUES (520, 190, '结算记录详情', '', 'merchant:finance:closing:detail', '', 'A', 1, 1, 0, 4, '2022-10-18 14:47:41', '2022-10-18 14:48:19');
INSERT INTO `eb_system_menu` VALUES (521, 437, '获取类目列表 树形结构', '', 'platform:pay:component:cat:treelist', '', 'A', 0, 1, 0, 3, '2022-10-20 11:17:50', '2022-10-20 11:17:50');
INSERT INTO `eb_system_menu` VALUES (522, 437, '获取类目列表 分页 用于平台查看和审核类目', '', 'platform:pay:component:cat:list', '', 'A', 0, 1, 0, 3, '2022-10-20 11:18:14', '2022-10-20 11:18:14');
INSERT INTO `eb_system_menu` VALUES (523, 104, '日帐单管理分页列表', '', 'platform:finance:daily:statement:page:list', '', 'A', 0, 1, 0, 3, '2022-10-24 10:59:30', '2022-10-24 10:59:30');
INSERT INTO `eb_system_menu` VALUES (524, 104, '月帐单管理分页列表', '', 'platform:finance:month:statement:page:list', '', 'A', 0, 1, 0, 3, '2022-10-24 10:59:46', '2022-10-24 10:59:46');
INSERT INTO `eb_system_menu` VALUES (525, 189, '资金流水分页列表', '', 'merchant:finance:funds:flow', '', 'A', 1, 1, 0, 4, '2022-10-25 12:04:32', '2022-10-25 12:04:32');
INSERT INTO `eb_system_menu` VALUES (526, 51, '资金流水分页列表', '', 'platform:finance:funds:flow', '', 'A', 0, 1, 0, 3, '2022-10-25 12:05:27', '2022-10-25 12:05:27');
INSERT INTO `eb_system_menu` VALUES (527, 191, '日帐单管理分页列表', '', 'merchant:finance:daily:statement:page:list', '', 'A', 1, 1, 0, 4, '2022-10-25 12:06:13', '2022-10-25 12:06:13');
INSERT INTO `eb_system_menu` VALUES (528, 191, '月帐单管理分页列表', '', 'merchant:finance:month:statement:page:list', '', 'A', 1, 1, 0, 4, '2022-10-25 12:06:28', '2022-10-25 12:06:28');
INSERT INTO `eb_system_menu` VALUES (529, 188, '商户端用户分页列表', '', 'merchant:user:page:list', '', 'A', 1, 1, 0, 4, '2022-10-25 17:23:40', '2022-10-25 17:23:40');
INSERT INTO `eb_system_menu` VALUES (530, 46, '商品评论分页列表', '', 'platform:product:reply:list', '', 'A', 0, 1, 0, 3, '2022-10-26 10:14:23', '2022-10-26 10:14:23');
INSERT INTO `eb_system_menu` VALUES (531, 46, '删除评论', '', 'platform:product:reply:delete', '', 'A', 0, 1, 0, 3, '2022-10-26 10:14:38', '2022-10-26 10:14:38');
INSERT INTO `eb_system_menu` VALUES (532, 185, '商户端商品评论分页列表', '', 'merchant:product:reply:page:list', '', 'A', 1, 1, 0, 4, '2022-10-26 10:15:10', '2022-10-26 10:15:10');
INSERT INTO `eb_system_menu` VALUES (533, 185, '虚拟评论', '', 'merchant:product:reply:virtual', '', 'A', 1, 1, 0, 4, '2022-10-26 10:15:23', '2022-10-26 10:15:23');
INSERT INTO `eb_system_menu` VALUES (534, 185, '删除评论', '', 'merchant:product:reply:delete', '', 'A', 1, 1, 0, 4, '2022-10-26 10:15:35', '2024-04-07 09:43:47');
INSERT INTO `eb_system_menu` VALUES (535, 185, '回复评论', '', 'merchant:product:reply:comment', '', 'A', 1, 1, 0, 4, '2022-10-26 10:15:48', '2022-10-26 10:15:48');
INSERT INTO `eb_system_menu` VALUES (536, 196, '优惠券领取记录分页列表', '', 'merchant:coupon:user:page:list', '', 'A', 1, 1, 0, 4, '2022-10-26 10:39:44', '2022-10-26 10:39:44');
INSERT INTO `eb_system_menu` VALUES (537, 436, '过审商品列表', '', 'platform:pay:component:product:list', '', 'A', 0, 1, 0, 3, '2022-10-26 15:09:22', '2022-10-26 15:09:22');
INSERT INTO `eb_system_menu` VALUES (538, 186, '核销码核销订单', '', 'merchant:order:verification', '', 'A', 1, 1, 0, 4, '2022-10-26 15:38:53', '2022-10-26 15:38:53');
INSERT INTO `eb_system_menu` VALUES (539, 919, '查询全部物流公司', '', 'merchant:express:all', '', 'A', 1, 1, 0, 4, '2022-10-26 15:40:44', '2024-05-08 11:00:42');
INSERT INTO `eb_system_menu` VALUES (540, 919, '查询物流公司面单模板', '', 'merchant:express:template', '', 'A', 1, 1, 0, 4, '2022-10-26 15:40:56', '2024-05-08 11:00:55');
INSERT INTO `eb_system_menu` VALUES (541, 59, '分页显示快递公司列表', '', 'platform:express:list', '', 'A', 0, 1, 0, 3, '2022-10-26 15:41:59', '2022-10-26 15:41:59');
INSERT INTO `eb_system_menu` VALUES (542, 59, '编辑快递公司', '', 'platform:express:update', '', 'A', 0, 1, 0, 3, '2022-10-26 15:42:27', '2022-10-26 15:42:27');
INSERT INTO `eb_system_menu` VALUES (543, 59, '修改快递公司显示状态', '', 'platform:express:update:show', '', 'A', 0, 1, 0, 3, '2022-10-26 15:42:40', '2022-10-26 15:42:40');
INSERT INTO `eb_system_menu` VALUES (544, 59, '同步物流公司', '', 'platform:express:sync', '', 'A', 0, 1, 0, 3, '2022-10-26 15:42:52', '2022-10-26 15:42:52');
INSERT INTO `eb_system_menu` VALUES (545, 59, '快递公司详情', '', 'platform:express:info', '', 'A', 0, 1, 0, 3, '2022-10-26 15:43:04', '2022-10-26 15:43:04');
INSERT INTO `eb_system_menu` VALUES (546, 437, '品牌分页列表', '', 'platform:pay:component:shop:brand:list', '', 'A', 0, 1, 0, 3, '2022-10-31 12:02:59', '2022-10-31 12:02:59');
INSERT INTO `eb_system_menu` VALUES (547, 33, '小程序订阅消息同步', '', 'platform:wechat:routine:sync', '', 'A', 0, 1, 0, 3, '2022-11-01 19:33:42', '2022-11-01 19:33:42');
INSERT INTO `eb_system_menu` VALUES (548, 33, '公众号模板消息同步', '', 'platform:wechat:whcbqhn:sync', '', 'A', 0, 1, 0, 3, '2022-11-01 19:34:12', '2022-11-01 19:34:12');
INSERT INTO `eb_system_menu` VALUES (549, 44, '获取订单发货单列表', '', 'platform:order:invoice:list', '', 'A', 0, 1, 0, 3, '2022-11-01 22:04:52', '2022-11-01 22:04:52');
INSERT INTO `eb_system_menu` VALUES (550, 186, '获取订单发货单列表', '', 'merchant:order:invoice:list', '', 'A', 1, 1, 0, 4, '2022-11-01 22:05:48', '2022-11-01 22:05:48');
INSERT INTO `eb_system_menu` VALUES (551, 186, '订单细节详情列表（发货使用）', '', 'merchant:order:detail:list', '', 'A', 1, 1, 0, 4, '2022-11-02 15:05:33', '2022-11-02 15:05:33');
INSERT INTO `eb_system_menu` VALUES (552, 70, '获取公众号自定义菜单', '', 'platform:wechat:public:customize:menu:get', '', 'A', 0, 1, 0, 3, '2022-11-03 14:16:38', '2022-11-03 14:16:38');
INSERT INTO `eb_system_menu` VALUES (553, 70, '保存公众号自定义菜单', '', 'platform:wechat:public:customize:menu:save', '', 'A', 0, 1, 0, 3, '2022-11-03 14:16:54', '2022-11-03 14:16:54');
INSERT INTO `eb_system_menu` VALUES (554, 70, '删除公众号自定义菜单', '', 'platform:wechat:public:customize:menu:delete', '', 'A', 0, 1, 0, 3, '2022-11-03 14:17:07', '2022-11-03 14:17:07');
INSERT INTO `eb_system_menu` VALUES (555, 399, '获取微信公众号js配置', '', 'platform:wechat:public:js:config', '', 'A', 0, 1, 0, 3, '2022-11-03 14:17:31', '2023-06-25 16:41:47');
INSERT INTO `eb_system_menu` VALUES (556, 399, '微信开放平台上传素材', '', 'platform:wechat:open:media:upload', '', 'A', 0, 1, 0, 3, '2022-11-03 14:17:46', '2023-06-25 16:41:54');
INSERT INTO `eb_system_menu` VALUES (559, 399, '微信关键字回复分页列表', '', 'platform:wechat:public:keywords:reply:list', '', 'A', 0, 1, 0, 3, '2022-11-04 09:31:48', '2023-06-25 16:42:01');
INSERT INTO `eb_system_menu` VALUES (560, 399, '新增微信关键字回复', '', 'platform:wechat:public:keywords:reply:save', '', 'A', 0, 1, 0, 3, '2022-11-04 09:32:06', '2023-06-25 16:42:07');
INSERT INTO `eb_system_menu` VALUES (561, 399, '删除微信关键字回复', '', 'platform:wechat:public:keywords:reply:delete', '', 'A', 0, 1, 0, 3, '2022-11-04 09:32:19', '2023-06-25 16:42:14');
INSERT INTO `eb_system_menu` VALUES (562, 399, '修改微信关键字回复', '', 'platform:wechat:public:keywords:reply:update', '', 'A', 0, 1, 0, 3, '2022-11-04 09:32:36', '2023-06-25 16:42:20');
INSERT INTO `eb_system_menu` VALUES (563, 399, '修改微信关键字回复状态', '', 'platform:wechat:public:keywords:reply:status', '', 'A', 0, 1, 0, 3, '2022-11-04 09:32:51', '2023-06-25 16:42:26');
INSERT INTO `eb_system_menu` VALUES (564, 399, '微信关键字回复详情', '', 'platform:wechat:public:keywords:reply:info', '', 'A', 0, 1, 0, 3, '2022-11-04 09:33:05', '2023-06-25 16:42:33');
INSERT INTO `eb_system_menu` VALUES (565, 399, '根据关键字查询微信关键字回复', '', 'platform:wechat:public:keywords:reply:info:keywords', '', 'A', 0, 1, 0, 3, '2022-11-04 09:33:18', '2023-06-25 16:42:39');
INSERT INTO `eb_system_menu` VALUES (566, 91, '分销员列表', '', 'platform:retail:store:people:list', '', 'A', 0, 1, 0, 3, '2022-11-07 12:23:49', '2022-11-07 12:23:49');
INSERT INTO `eb_system_menu` VALUES (567, 91, '根据条件获取下级推广用户列表', '', 'platform:retail:store:sub:user:list', '', 'A', 0, 1, 0, 3, '2022-11-07 12:24:12', '2022-11-07 12:24:12');
INSERT INTO `eb_system_menu` VALUES (568, 89, '修改用户上级推广人', '', 'platform:retail:store:update:user:spread', '', 'A', 0, 1, 0, 3, '2022-11-07 12:24:36', '2022-11-07 12:24:36');
INSERT INTO `eb_system_menu` VALUES (569, 89, '清除用户上级推广人', '', 'platform:retail:store:clean:user:spread', '', 'A', 0, 1, 0, 3, '2022-11-07 12:24:50', '2022-11-07 12:24:50');
INSERT INTO `eb_system_menu` VALUES (570, 91, '根据条件获取推广订单列表', '', 'platform:retail:store:promotion:order:list', '', 'A', 0, 1, 0, 3, '2022-11-07 14:29:44', '2022-11-07 14:29:44');
INSERT INTO `eb_system_menu` VALUES (571, 76, '积分分页列表', '', 'platform:integral:page:list', '', 'A', 0, 1, 0, 3, '2022-11-09 14:20:00', '2022-11-09 14:20:00');
INSERT INTO `eb_system_menu` VALUES (572, 7, '定时任务管理', '', '', '/operation/maintain/schedule', 'C', 99, 1, 0, 3, '2022-11-22 11:38:50', '2023-07-19 15:58:28');
INSERT INTO `eb_system_menu` VALUES (573, 572, '定时任务', '', '', '/operation/maintain/schedule/list', 'C', 0, 1, 0, 3, '2022-11-22 11:39:38', '2023-07-19 16:08:14');
INSERT INTO `eb_system_menu` VALUES (574, 572, '定时任务日志', '', '', '/operation/maintain/schedule/logList', 'C', 0, 1, 0, 3, '2022-11-22 11:39:56', '2023-07-19 16:08:24');
INSERT INTO `eb_system_menu` VALUES (576, 574, '定时任务日志分页列表', '', 'platform:schedule:job:log:list', '', 'A', 0, 1, 0, 3, '2022-11-22 11:40:32', '2022-11-22 11:40:32');
INSERT INTO `eb_system_menu` VALUES (577, 573, '定时任务列表', '', 'platform:schedule:job:list', '', 'A', 0, 1, 0, 3, '2022-11-22 11:40:56', '2022-11-22 11:40:56');
INSERT INTO `eb_system_menu` VALUES (578, 573, '添加定时任务', '', 'platform:schedule:job:add', '', 'A', 0, 1, 0, 3, '2022-11-22 11:41:10', '2022-11-22 11:41:10');
INSERT INTO `eb_system_menu` VALUES (579, 573, '定时任务编辑', '', 'platform:schedule:job:update', '', 'A', 0, 1, 0, 3, '2022-11-22 11:41:27', '2022-11-22 11:41:27');
INSERT INTO `eb_system_menu` VALUES (580, 573, '暂停定时任务', '', 'platform:schedule:job:suspend', '', 'A', 0, 1, 0, 3, '2022-11-22 11:41:44', '2022-11-22 11:41:44');
INSERT INTO `eb_system_menu` VALUES (581, 573, '启动定时任务', '', 'platform:schedule:job:start', '', 'A', 0, 1, 0, 3, '2022-11-22 11:42:03', '2022-11-22 11:42:03');
INSERT INTO `eb_system_menu` VALUES (582, 573, '删除定时任务', '', 'platform:schedule:job:delete', '', 'A', 0, 1, 0, 3, '2022-11-22 11:42:16', '2022-11-22 11:42:16');
INSERT INTO `eb_system_menu` VALUES (583, 573, '立即执行定时任务（一次）', '', 'platform:schedule:job:trig', '', 'A', 0, 1, 0, 3, '2022-11-22 11:42:33', '2022-11-22 11:42:33');
INSERT INTO `eb_system_menu` VALUES (584, 1, '首页数据', '', 'platform:statistics:home:index', '', 'A', 0, 1, 0, 3, '2022-11-25 19:44:02', '2022-11-25 19:44:02');
INSERT INTO `eb_system_menu` VALUES (585, 1, '经营数据', '', 'platform:statistics:home:operating:data', '', 'A', 0, 1, 0, 3, '2022-11-25 19:44:22', '2022-11-25 19:44:22');
INSERT INTO `eb_system_menu` VALUES (586, 1, '用户渠道数据', '', 'platform:statistics:home:user:channel', '', 'A', 0, 1, 0, 3, '2022-11-25 19:44:41', '2022-11-25 19:44:41');
INSERT INTO `eb_system_menu` VALUES (587, 171, '首页数据', '', 'merchant:statistics:home:index', '', 'A', 1, 1, 0, 4, '2022-11-25 19:45:41', '2022-11-25 19:45:41');
INSERT INTO `eb_system_menu` VALUES (588, 171, '经营数据', '', 'merchant:statistics:home:operating:data', '', 'A', 1, 1, 0, 4, '2022-11-25 19:45:57', '2022-11-25 19:45:57');
INSERT INTO `eb_system_menu` VALUES (589, 171, '商品支付排行榜', '', 'merchant:statistics:home:product:pay:ranking', '', 'A', 1, 1, 0, 4, '2022-11-25 19:46:12', '2022-11-25 19:46:12');
INSERT INTO `eb_system_menu` VALUES (590, 171, '商品浏览量排行榜', '', 'merchant:statistics:home:product:pageview:ranking', '', 'A', 1, 1, 0, 4, '2022-11-25 19:46:29', '2022-11-25 19:46:29');
INSERT INTO `eb_system_menu` VALUES (592, 48, '用户详情', '', 'platform:user:detail', '', 'A', 0, 1, 0, 3, '2022-11-30 09:29:03', '2022-11-30 09:29:03');
INSERT INTO `eb_system_menu` VALUES (593, 188, '用户详情', '', 'merchant:user:detail', '', 'A', 1, 1, 0, 4, '2022-11-30 09:29:54', '2022-11-30 09:29:54');
INSERT INTO `eb_system_menu` VALUES (594, 25, '获取版权信息', '', 'platform:copyright:get:info', '', 'A', 0, 1, 0, 3, '2022-11-30 15:02:50', '2022-11-30 15:02:50');
INSERT INTO `eb_system_menu` VALUES (595, 25, '编辑公司版权信息', '', 'platform:copyright:update:company:info', '', 'A', 0, 1, 0, 3, '2022-11-30 15:43:22', '2022-11-30 15:43:22');
INSERT INTO `eb_system_menu` VALUES (596, 179, '获取商户版权信息', '', 'merchant:copyright:get:company:info', '', 'A', 1, 1, 0, 4, '2022-11-30 21:07:06', '2022-11-30 21:07:06');
INSERT INTO `eb_system_menu` VALUES (597, 399, '关键字回复', '', '', '/operation/application/publicAccount/wxReply/keyword', 'C', 0, 1, 0, 3, '2022-12-12 17:36:43', '2023-07-19 15:54:55');
INSERT INTO `eb_system_menu` VALUES (598, 73, '秒杀', '', '', '/marketing/seckill', 'M', 930, 1, 0, 3, '2022-12-19 11:10:20', '2024-04-18 15:30:18');
INSERT INTO `eb_system_menu` VALUES (599, 598, '秒杀配置', '', '', '/marketing/seckill/config', 'C', 0, 1, 0, 3, '2022-12-19 14:56:06', '2022-12-19 14:56:06');
INSERT INTO `eb_system_menu` VALUES (600, 598, '秒杀活动', '', '', '/marketing/seckill/seckillActivity', 'C', 0, 1, 0, 3, '2022-12-19 14:56:31', '2022-12-19 14:56:46');
INSERT INTO `eb_system_menu` VALUES (601, 598, '秒杀商品', '', '', '/marketing/seckill/list', 'C', 0, 1, 0, 3, '2022-12-19 14:57:38', '2022-12-22 16:57:44');
INSERT INTO `eb_system_menu` VALUES (602, 599, '新增秒杀时段', '', 'platform:seckill:time:interval:add', '', 'A', 0, 1, 0, 3, '2022-12-20 14:20:47', '2022-12-20 14:20:47');
INSERT INTO `eb_system_menu` VALUES (603, 599, '编辑秒杀时段', '', 'platform:seckill:time:interval:update', '', 'A', 0, 1, 0, 3, '2022-12-20 14:21:04', '2022-12-20 14:21:04');
INSERT INTO `eb_system_menu` VALUES (604, 599, '删除秒杀时段', '', 'platform:seckill:time:interval:delete', '', 'A', 0, 1, 0, 3, '2022-12-20 14:21:19', '2022-12-20 14:21:19');
INSERT INTO `eb_system_menu` VALUES (605, 599, '秒杀时段列表', '', 'platform:seckill:time:interval:list', '', 'A', 0, 1, 0, 3, '2022-12-20 14:21:34', '2022-12-20 14:21:34');
INSERT INTO `eb_system_menu` VALUES (606, 11, '可用分类商户列表', '', 'platform:merchant:use:category:list', '', 'A', 0, 1, 0, 3, '2022-12-21 15:31:20', '2022-12-21 15:31:20');
INSERT INTO `eb_system_menu` VALUES (607, 2, '商品搜索分页列表（活动）', '', 'platform:product:activity:search:page', '', 'A', 0, 1, 0, 3, '2022-12-21 15:31:45', '2022-12-21 15:31:45');
INSERT INTO `eb_system_menu` VALUES (608, 600, '新增秒杀活动', '', 'platform:seckill:activity:add', '', 'A', 0, 1, 0, 3, '2022-12-21 15:32:09', '2022-12-21 15:32:09');
INSERT INTO `eb_system_menu` VALUES (609, 600, '秒杀活动分页列表', '', 'platform:seckill:activity:page', '', 'A', 0, 1, 0, 3, '2022-12-21 17:27:03', '2022-12-21 17:27:03');
INSERT INTO `eb_system_menu` VALUES (610, 600, '秒杀活动详情', '', 'platform:seckill:activity:detail', '', 'A', 0, 1, 0, 3, '2022-12-21 17:27:19', '2022-12-21 17:27:19');
INSERT INTO `eb_system_menu` VALUES (611, 600, '编辑秒杀活动', '', 'platform:seckill:activity:update', '', 'A', 0, 1, 0, 3, '2022-12-21 18:09:52', '2022-12-21 18:09:52');
INSERT INTO `eb_system_menu` VALUES (612, 600, '删除秒杀活动', '', 'platform:seckill:activity:delete', '', 'A', 0, 1, 0, 3, '2022-12-21 18:10:07', '2022-12-21 18:10:07');
INSERT INTO `eb_system_menu` VALUES (613, 599, '秒杀时段开关', '', 'platform:seckill:time:interval:switch', '', 'A', 0, 1, 0, 3, '2022-12-22 09:43:01', '2022-12-22 09:43:01');
INSERT INTO `eb_system_menu` VALUES (614, 601, '秒杀商品列表', '', 'platform:seckill:product:list', '', 'A', 0, 1, 0, 3, '2022-12-22 16:58:05', '2022-12-22 16:58:05');
INSERT INTO `eb_system_menu` VALUES (615, 601, '秒杀商品设置活动价', '', 'platform:seckill:product:price', '', 'A', 0, 1, 0, 3, '2022-12-22 16:58:21', '2022-12-22 16:58:21');
INSERT INTO `eb_system_menu` VALUES (616, 601, '秒杀商品下架', '', 'platform:seckill:product:down', '', 'A', 0, 1, 0, 3, '2022-12-22 16:58:35', '2022-12-22 16:58:35');
INSERT INTO `eb_system_menu` VALUES (617, 601, '秒杀商品删除', '', 'platform:seckill:product:delete', '', 'A', 0, 1, 0, 3, '2022-12-22 16:58:49', '2022-12-22 16:58:49');
INSERT INTO `eb_system_menu` VALUES (618, 601, '平台秒杀商品添加', '', 'platform:seckill:product:add', '', 'A', 0, 1, 0, 3, '2022-12-23 17:22:55', '2022-12-23 17:22:55');
INSERT INTO `eb_system_menu` VALUES (619, 0, '营销', 's-shop', '', '/marketing', 'M', 4900, 1, 0, 4, '2022-12-23 17:26:58', '2023-07-01 11:06:18');
INSERT INTO `eb_system_menu` VALUES (620, 619, '秒杀', '', '', '/marketing/seckill', 'C', 8888, 1, 0, 4, '2022-12-23 17:27:27', '2023-07-01 11:02:00');
INSERT INTO `eb_system_menu` VALUES (621, 620, '秒杀活动', '', '', '/marketing/seckill/seckillActivity', 'C', 1, 1, 0, 4, '2022-12-23 17:27:44', '2022-12-27 10:30:05');
INSERT INTO `eb_system_menu` VALUES (622, 620, '秒杀商品', '', '', '/marketing/seckill/list', 'C', 1, 1, 0, 4, '2022-12-23 17:27:59', '2022-12-27 10:30:19');
INSERT INTO `eb_system_menu` VALUES (623, 621, '秒杀活动分页列表', '', 'merchant:seckill:activity:page', '', 'A', 1, 1, 0, 4, '2022-12-23 17:28:19', '2022-12-27 12:05:06');
INSERT INTO `eb_system_menu` VALUES (624, 621, '秒杀活动详情', '', 'merchant:seckill:activity:detail', '', 'A', 1, 1, 0, 4, '2022-12-23 17:28:33', '2022-12-23 17:28:33');
INSERT INTO `eb_system_menu` VALUES (625, 622, '秒杀商品列表', '', 'merchant:seckill:product:list', '', 'A', 1, 1, 0, 4, '2022-12-23 17:28:50', '2022-12-23 17:28:50');
INSERT INTO `eb_system_menu` VALUES (626, 622, '秒杀商品撤回审核', '', 'merchant:seckill:product:withdraw', '', 'A', 1, 1, 0, 4, '2022-12-23 17:29:04', '2022-12-23 17:29:04');
INSERT INTO `eb_system_menu` VALUES (627, 622, '秒杀商品设置活动价', '', 'merchant:seckill:product:price', '', 'A', 1, 1, 0, 4, '2022-12-23 17:29:24', '2022-12-23 17:29:24');
INSERT INTO `eb_system_menu` VALUES (628, 622, '秒杀商品上架', '', 'merchant:seckill:product:up', '', 'A', 1, 1, 0, 4, '2022-12-23 17:29:48', '2022-12-23 17:29:48');
INSERT INTO `eb_system_menu` VALUES (629, 622, '秒杀商品下架', '', 'merchant:seckill:product:down', '', 'A', 1, 1, 0, 4, '2022-12-23 17:30:02', '2022-12-23 17:30:02');
INSERT INTO `eb_system_menu` VALUES (630, 622, '秒杀商品删除', '', 'merchant:seckill:product:delete', '', 'A', 1, 1, 0, 4, '2022-12-23 17:30:18', '2022-12-23 17:30:18');
INSERT INTO `eb_system_menu` VALUES (631, 622, '秒杀商品添加', '', 'merchant:seckill:product:add', '', 'A', 1, 1, 0, 4, '2022-12-23 17:30:32', '2022-12-23 17:30:32');
INSERT INTO `eb_system_menu` VALUES (632, 600, '秒杀活动开关', '', 'platform:seckill:activity:switch', '', 'A', 0, 1, 0, 3, '2022-12-24 15:51:37', '2022-12-24 15:51:37');
INSERT INTO `eb_system_menu` VALUES (633, 172, '商品搜索分页列表（活动）', '', 'merchant:product:activity:search:page', '', 'A', 1, 1, 0, 4, '2022-12-27 12:10:38', '2022-12-27 12:10:38');
INSERT INTO `eb_system_menu` VALUES (634, 73, '氛围图', '', '', '/marketing/atmosphere/list', 'M', 500, 1, 0, 3, '2022-12-28 11:46:07', '2024-04-18 15:31:00');
INSERT INTO `eb_system_menu` VALUES (635, 73, '活动边框', '', '', '/marketing/border/list', 'M', 300, 1, 0, 3, '2022-12-28 12:09:56', '2024-04-18 15:31:05');
INSERT INTO `eb_system_menu` VALUES (636, 601, '秒杀商品审核', '', 'platform:seckill:product:audit', '', 'A', 0, 1, 0, 3, '2022-12-29 17:09:23', '2022-12-29 17:09:23');
INSERT INTO `eb_system_menu` VALUES (637, 43, '商品id集合列表', '', 'platform:product:list:ids', '', 'A', 0, 1, 0, 3, '2023-01-12 11:52:44', '2023-01-12 11:52:44');
INSERT INTO `eb_system_menu` VALUES (638, 0, '控制台', 'menu', '', '/dashboard', 'M', 99999, 0, 0, 3, '2023-01-15 17:37:14', '2023-02-02 09:52:48');
INSERT INTO `eb_system_menu` VALUES (639, 23, '页面底部导航', '', 'platform:page:layout:bottom:navigation', '', 'A', 0, 1, 0, 3, '2023-02-04 15:44:31', '2023-02-04 15:44:31');
INSERT INTO `eb_system_menu` VALUES (640, 23, '底部导航保存', '', 'platform:page:layout:bottom:navigation:save', '', 'A', 0, 1, 0, 3, '2023-02-04 15:44:49', '2023-02-04 15:44:49');
INSERT INTO `eb_system_menu` VALUES (641, 634, '分页列表（同边框）', '', 'platform:activitystyle:list', '', 'A', 0, 1, 0, 3, '2023-02-22 18:21:47', '2023-02-22 18:23:24');
INSERT INTO `eb_system_menu` VALUES (642, 634, '新增氛围图（同边框）', '', 'platform:activitystyle:save', 'platform:activitystyle:save', 'A', 0, 1, 0, 3, '2023-02-22 18:23:05', '2023-02-22 18:23:05');
INSERT INTO `eb_system_menu` VALUES (643, 634, '修改氛围图（同边框）', '', 'platform:activitystyle:edite', '', 'A', 0, 1, 0, 3, '2023-02-22 18:23:54', '2023-02-22 18:23:54');
INSERT INTO `eb_system_menu` VALUES (644, 634, '修改氛围图状态（同边框）', '', 'platform:activitystyle:updatestatus', '', 'A', 0, 1, 0, 3, '2023-02-22 18:25:33', '2023-02-22 18:25:51');
INSERT INTO `eb_system_menu` VALUES (645, 634, '删除氛围图（同边框）', '', 'platform:activitystyle:delete', '', 'A', 0, 1, 0, 3, '2023-02-22 18:26:40', '2024-04-07 11:52:51');
INSERT INTO `eb_system_menu` VALUES (646, 10, '通过表单名称加载表单', '', 'platform:system:form:name:info', '', 'A', 0, 1, 0, 3, '2023-03-04 11:17:17', '2023-03-04 11:17:17');
INSERT INTO `eb_system_menu` VALUES (647, 179, '商户通过表单名称加载表单', '', 'merchant:system:form:name:info', '', 'A', 1, 1, 0, 4, '2023-03-04 14:26:00', '2023-03-04 14:26:00');
INSERT INTO `eb_system_menu` VALUES (649, 364, '等级配置', '', '', '/user/level/config', 'C', 0, 1, 0, 3, '2023-03-06 10:33:31', '2023-03-06 14:59:23');
INSERT INTO `eb_system_menu` VALUES (650, 364, '等级说明', '', '', '/user/level/description', 'C', 0, 1, 0, 3, '2023-03-06 10:33:47', '2023-03-06 14:59:36');
INSERT INTO `eb_system_menu` VALUES (651, 649, '获取用户等级配置', '', 'platform:system:user:level:config', '', 'A', 0, 1, 0, 3, '2023-03-06 10:35:02', '2023-03-06 10:35:02');
INSERT INTO `eb_system_menu` VALUES (652, 649, '编辑用户等级配置', '', 'platform:system:user:level:config:update', '', 'A', 0, 1, 0, 3, '2023-03-06 10:35:23', '2023-03-06 10:35:23');
INSERT INTO `eb_system_menu` VALUES (653, 650, '获取用户等级规则', '', 'platform:system:user:level:rule', '', 'A', 0, 1, 0, 3, '2023-03-06 10:35:46', '2023-03-06 10:35:46');
INSERT INTO `eb_system_menu` VALUES (654, 650, '编辑用户等级规则', '', 'platform:system:user:level:rule:update', '', 'A', 0, 1, 0, 3, '2023-03-06 10:36:04', '2023-03-06 10:36:04');
INSERT INTO `eb_system_menu` VALUES (655, 364, '等级列表', '', '', '/user/level/list', 'C', 3, 1, 0, 3, '2023-03-06 14:54:41', '2023-03-06 14:58:56');
INSERT INTO `eb_system_menu` VALUES (656, 657, '社区分类', '', '', '/marketing/community/classification', 'C', 0, 1, 0, 3, '2023-03-07 14:21:15', '2023-07-19 15:00:18');
INSERT INTO `eb_system_menu` VALUES (657, 73, '种草社区', '', '', '/marketing/community', 'M', 750, 1, 0, 3, '2023-03-07 14:23:41', '2024-04-18 15:30:50');
INSERT INTO `eb_system_menu` VALUES (658, 657, '社区话题', '', '', '/marketing/community/topics', 'C', 0, 1, 0, 3, '2023-03-07 14:27:51', '2023-07-19 15:00:34');
INSERT INTO `eb_system_menu` VALUES (659, 657, '社区内容', '', '', '/marketing/community/content', 'C', 0, 1, 0, 3, '2023-03-07 14:30:28', '2023-07-19 15:00:40');
INSERT INTO `eb_system_menu` VALUES (660, 657, '社区评论', '', '', '/marketing/community/comments', 'C', 0, 1, 0, 3, '2023-03-07 14:31:57', '2023-07-19 15:00:46');
INSERT INTO `eb_system_menu` VALUES (661, 657, '社区配置', '', '', '/marketing/community/config', 'C', 0, 1, 0, 3, '2023-03-07 14:32:54', '2023-07-19 15:00:58');
INSERT INTO `eb_system_menu` VALUES (662, 656, '社区分类分页列表', '', 'platform:community:category:page:list', '', 'A', 0, 1, 0, 3, '2023-03-20 11:43:21', '2023-03-20 11:43:21');
INSERT INTO `eb_system_menu` VALUES (663, 656, '添加社区分类', '', 'platform:community:category:add', '', 'A', 0, 1, 0, 3, '2023-03-20 11:43:40', '2023-03-20 11:43:40');
INSERT INTO `eb_system_menu` VALUES (664, 656, '编辑社区分类', '', 'platform:community:category:update', '', 'A', 0, 1, 0, 3, '2023-03-20 11:43:55', '2023-03-20 11:43:55');
INSERT INTO `eb_system_menu` VALUES (665, 656, '删除社区分类', '', 'platform:community:category:delete', '', 'A', 0, 1, 0, 3, '2023-03-20 11:44:10', '2023-03-20 11:44:10');
INSERT INTO `eb_system_menu` VALUES (666, 656, '社区分类显示开关', '', 'platform:community:category:show:switch', '', 'A', 0, 1, 0, 3, '2023-03-20 11:44:24', '2023-03-20 11:44:24');
INSERT INTO `eb_system_menu` VALUES (667, 661, '获取社区配置', '', 'platform:community:get:config', '', 'A', 0, 1, 0, 3, '2023-03-20 11:44:46', '2023-03-20 11:44:46');
INSERT INTO `eb_system_menu` VALUES (668, 661, '更新社区配置', '', 'platform:community:update:config', '', 'A', 0, 1, 0, 3, '2023-03-20 11:45:02', '2023-03-20 11:45:02');
INSERT INTO `eb_system_menu` VALUES (669, 658, '社区话题分页列表', '', 'platform:community:topic:page:list', '', 'A', 0, 1, 0, 3, '2023-03-20 11:45:33', '2023-03-20 11:45:33');
INSERT INTO `eb_system_menu` VALUES (670, 658, '添加社区话题', '', 'platform:community:topic:add', '', 'A', 0, 1, 0, 3, '2023-03-20 11:45:48', '2023-03-20 11:45:48');
INSERT INTO `eb_system_menu` VALUES (671, 658, '编辑社区话题', '', 'platform:community:topic:update', '', 'A', 0, 1, 0, 3, '2023-03-20 11:46:01', '2023-03-20 11:46:01');
INSERT INTO `eb_system_menu` VALUES (672, 658, '删除社区话题', '', 'platform:community:topic:delete', '', 'A', 0, 1, 0, 3, '2023-03-20 11:46:15', '2023-03-20 11:46:15');
INSERT INTO `eb_system_menu` VALUES (673, 658, '社区话题开启/关闭推荐', '', 'platform:community:topic:recommend:switch', '', 'A', 0, 1, 0, 3, '2023-03-20 11:46:30', '2023-03-20 11:46:30');
INSERT INTO `eb_system_menu` VALUES (674, 660, '社区评论分页列表', '', 'platform:community:reply:page:list', '', 'A', 0, 1, 0, 3, '2023-03-20 11:46:51', '2023-03-20 11:46:51');
INSERT INTO `eb_system_menu` VALUES (675, 660, '社区评论审核', '', 'platform:community:reply:audit', '', 'A', 0, 1, 0, 3, '2023-03-20 11:47:10', '2023-03-20 11:47:10');
INSERT INTO `eb_system_menu` VALUES (676, 660, '社区评论删除', '', 'platform:community:reply:delete', '', 'A', 0, 1, 0, 3, '2023-03-20 11:47:24', '2023-03-20 11:47:24');
INSERT INTO `eb_system_menu` VALUES (677, 660, '社区评论-文章分页列表', '', 'platform:community:reply:note:page:list', '', 'A', 0, 1, 0, 3, '2023-03-20 11:47:51', '2023-03-20 11:47:51');
INSERT INTO `eb_system_menu` VALUES (678, 659, '社区笔记分页列表', '', 'platform:community:note:page:list', '', 'A', 0, 1, 0, 3, '2023-03-20 11:48:13', '2023-03-20 11:48:13');
INSERT INTO `eb_system_menu` VALUES (679, 659, '社区笔记详情', '', 'platform:community:note:detail', '', 'A', 0, 1, 0, 3, '2023-03-20 11:48:30', '2023-03-20 11:48:30');
INSERT INTO `eb_system_menu` VALUES (680, 659, '社区笔记审核', '', 'platform:community:note:audit', '', 'A', 0, 1, 0, 3, '2023-03-20 11:48:43', '2023-03-20 11:48:43');
INSERT INTO `eb_system_menu` VALUES (681, 659, '社区笔记强制下架', '', 'platform:community:note:forced:down', '', 'A', 0, 1, 0, 3, '2023-03-20 11:48:57', '2023-03-20 11:48:57');
INSERT INTO `eb_system_menu` VALUES (682, 659, '社区笔记删除', '', 'platform:community:note:delete', '', 'A', 0, 1, 0, 3, '2023-03-20 11:49:13', '2023-03-20 11:49:13');
INSERT INTO `eb_system_menu` VALUES (683, 659, '社区笔记分类批量修改', '', 'platform:community:note:category:batch:update', '', 'A', 0, 1, 0, 3, '2023-03-20 11:49:28', '2023-03-20 11:49:28');
INSERT INTO `eb_system_menu` VALUES (684, 659, '社区笔记推荐星级编辑', '', 'platform:community:note:star:update', '', 'A', 0, 1, 0, 3, '2023-03-20 11:49:42', '2023-03-20 11:49:42');
INSERT INTO `eb_system_menu` VALUES (685, 659, '社区笔记评论强制关闭开关', '', 'platform:community:note:repley:force:off:switch', '', 'A', 0, 1, 0, 3, '2023-03-20 11:49:56', '2023-03-20 11:49:56');
INSERT INTO `eb_system_menu` VALUES (686, 5, '充值记录', '', '', '/finance/charge', 'C', 0, 1, 0, 3, '2023-03-24 17:52:17', '2024-04-18 15:32:14');
INSERT INTO `eb_system_menu` VALUES (687, 73, '直播', '', '', '/marketing/broadcast', 'M', 830, 1, 0, 3, '2023-03-29 14:30:22', '2024-04-18 15:30:36');
INSERT INTO `eb_system_menu` VALUES (688, 687, '直播间', '', '', '/marketing/broadcast/list', 'M', 0, 1, 0, 3, '2023-03-29 14:30:44', '2023-03-29 16:52:58');
INSERT INTO `eb_system_menu` VALUES (689, 687, '直播间商品', '', '', '/marketing/broadcast/product', 'M', 0, 1, 0, 3, '2023-03-29 14:30:57', '2023-03-29 16:53:27');
INSERT INTO `eb_system_menu` VALUES (700, 689, '直播商品列表', '', 'platform:mp:live:goods:list', '', 'A', 0, 1, 0, 3, '2023-03-29 14:38:33', '2023-03-29 14:45:51');
INSERT INTO `eb_system_menu` VALUES (701, 689, '直播间商品审核', '', 'platform:mp:live:goods:review', '', 'A', 0, 1, 0, 3, '2023-03-29 14:39:02', '2023-03-29 14:46:41');
INSERT INTO `eb_system_menu` VALUES (702, 689, '直播间商品删除', '', 'platform:mp:live:goods:delete', '', 'A', 0, 1, 0, 3, '2023-03-29 14:39:23', '2023-03-29 14:47:40');
INSERT INTO `eb_system_menu` VALUES (703, 689, '直播间商品排序', '', 'platform:mp:live:goods:sort', '', 'A', 0, 1, 0, 3, '2023-03-29 14:39:39', '2023-03-29 14:47:46');
INSERT INTO `eb_system_menu` VALUES (704, 689, '直播间商品详情', '', 'platform:mp:live:goods:info', '', 'A', 0, 1, 0, 3, '2023-03-29 14:39:55', '2023-03-29 14:47:53');
INSERT INTO `eb_system_menu` VALUES (705, 619, '小程序直播', '', '', '/marketing/broadcast', 'M', 7777, 1, 0, 4, '2023-03-29 14:41:38', '2023-07-01 11:02:11');
INSERT INTO `eb_system_menu` VALUES (706, 714, '直播间商品列表', '', 'merchant:mp:live:goods:list', '', 'A', 1, 1, 0, 4, '2023-03-29 14:49:19', '2023-03-29 18:23:11');
INSERT INTO `eb_system_menu` VALUES (707, 714, '新增直播商品并提审', '', 'merchant:mp:live:goods:save', '', 'A', 1, 1, 0, 4, '2023-03-29 14:49:42', '2023-03-29 18:23:22');
INSERT INTO `eb_system_menu` VALUES (708, 714, '直播商品删除', '', 'merchant:mp:live:goods:delete', '', 'A', 1, 1, 0, 4, '2023-03-29 14:50:00', '2023-03-29 18:23:29');
INSERT INTO `eb_system_menu` VALUES (709, 714, '直播商品修改', '', 'merchant:mp:live:goods:edit', '', 'A', 1, 1, 0, 4, '2023-03-29 14:50:33', '2023-03-29 18:23:38');
INSERT INTO `eb_system_menu` VALUES (710, 714, '直播商品详情', '', 'merchant:mp:live:goods:info', '', 'A', 1, 1, 0, 4, '2023-03-29 14:50:50', '2023-03-29 18:23:44');
INSERT INTO `eb_system_menu` VALUES (713, 705, '直播间管理', '', '', '/marketing/broadcast/room', 'C', 1, 1, 0, 4, '2023-03-29 18:22:08', '2023-03-30 17:15:11');
INSERT INTO `eb_system_menu` VALUES (714, 705, '直播商品管理', '', '', '/marketing/broadcast/product', 'C', 1, 1, 0, 4, '2023-03-29 18:22:47', '2023-03-29 18:22:47');
INSERT INTO `eb_system_menu` VALUES (715, 705, '直播助手', '', '', '/marketing/broadcast/assistant', 'C', 1, 1, 0, 4, '2023-03-29 18:24:35', '2023-03-29 18:24:43');
INSERT INTO `eb_system_menu` VALUES (716, 715, '小助手列表', '', 'merchant:mp:live:assistant:list', '', 'A', 1, 1, 0, 4, '2023-03-29 18:38:36', '2023-03-29 18:38:36');
INSERT INTO `eb_system_menu` VALUES (717, 715, '小助手新增', '', 'merchant:mp:live:assistant:save', '', 'A', 1, 1, 0, 4, '2023-03-29 18:38:54', '2023-03-29 18:38:54');
INSERT INTO `eb_system_menu` VALUES (718, 715, '小助手删除', '', 'merchant:mp:live:assistant:delete', '', 'A', 1, 1, 0, 4, '2023-03-29 18:39:09', '2023-03-29 18:39:09');
INSERT INTO `eb_system_menu` VALUES (719, 715, '小助手修改', '', 'merchant:mp:live:assistant:edit', '', 'A', 1, 1, 0, 4, '2023-03-29 18:39:31', '2023-03-29 18:39:31');
INSERT INTO `eb_system_menu` VALUES (720, 715, '小助手详情', '', 'merchant:mp:live:assistant:info', '', 'A', 1, 1, 0, 4, '2023-03-29 18:39:47', '2023-03-29 18:39:47');
INSERT INTO `eb_system_menu` VALUES (721, 688, '直播间审核', '', 'platform:mp:live:room:review', '', 'A', 0, 1, 0, 3, '2023-03-30 16:01:58', '2023-03-30 16:01:58');
INSERT INTO `eb_system_menu` VALUES (722, 688, '直播间商品排序', '', 'platform:mp:live:room:goodssort', '', 'A', 0, 1, 0, 3, '2023-03-30 16:02:23', '2023-03-30 16:02:23');
INSERT INTO `eb_system_menu` VALUES (723, 688, '删除直播间', '', 'platform:mp:live:room:delete', '', 'A', 0, 1, 0, 3, '2023-03-30 16:02:39', '2023-03-30 16:02:39');
INSERT INTO `eb_system_menu` VALUES (724, 688, '获取直播间分享二维码', '', 'platform:mp:live:room:getsharecode', '', 'A', 0, 1, 0, 3, '2023-03-30 16:03:05', '2023-03-30 16:03:05');
INSERT INTO `eb_system_menu` VALUES (734, 688, '关闭评论', '', 'platform:mp:live:room:updatecomment', '', 'A', 0, 1, 0, 3, '2023-03-30 16:07:15', '2023-03-30 16:07:15');
INSERT INTO `eb_system_menu` VALUES (735, 688, '开启收录', '', 'platform:mp:live:room:isfeedspubic', '', 'A', 0, 1, 0, 3, '2023-03-30 16:07:31', '2023-03-30 16:07:58');
INSERT INTO `eb_system_menu` VALUES (737, 688, '开启回放', '', 'platform:mp:live:room:updatereplay', '', 'A', 0, 1, 0, 3, '2023-03-30 16:08:48', '2023-03-30 16:08:48');
INSERT INTO `eb_system_menu` VALUES (739, 705, '微信素材', '', '', '', 'M', 1, 0, 0, 4, '2023-03-30 16:10:00', '2023-03-30 16:12:13');
INSERT INTO `eb_system_menu` VALUES (740, 739, '微信素材上传', '', 'merchant:mp:media:upload', '', 'A', 1, 1, 0, 4, '2023-03-30 16:10:20', '2023-03-30 16:10:20');
INSERT INTO `eb_system_menu` VALUES (741, 739, '微信素材获取', '', 'merchant:mp:media:get', '', 'A', 1, 1, 0, 4, '2023-03-30 16:10:38', '2023-03-30 16:10:38');
INSERT INTO `eb_system_menu` VALUES (742, 713, '创建直播间', '', 'merchant:mp:live:room:create', '', 'A', 1, 0, 0, 4, '2023-03-30 16:14:47', '2023-07-12 15:26:30');
INSERT INTO `eb_system_menu` VALUES (743, 713, '上传直播间', '', 'merchant:mp:live:room:delete', '', 'A', 1, 1, 0, 4, '2023-03-30 16:15:06', '2023-03-30 16:15:06');
INSERT INTO `eb_system_menu` VALUES (744, 713, '编辑直播间', '', 'merchant:mp:live:room:edit', '', 'A', 1, 1, 0, 4, '2023-03-30 16:15:41', '2023-03-30 16:15:41');
INSERT INTO `eb_system_menu` VALUES (745, 713, '导入商品', '', 'merchant:mp:live:room:addgoods', '', 'A', 1, 1, 0, 4, '2023-03-30 16:16:14', '2023-03-30 16:16:14');
INSERT INTO `eb_system_menu` VALUES (746, 713, '获取直播间分享二维码', '', 'merchant:mp:live:room:getsharecode', '', 'A', 1, 1, 0, 4, '2023-03-30 16:16:36', '2023-03-30 16:16:36');
INSERT INTO `eb_system_menu` VALUES (751, 713, '删除直播间商品', '', 'merchant:mp:live:room:deletegoods', '', 'A', 1, 1, 0, 4, '2023-03-30 16:18:25', '2023-03-30 16:18:25');
INSERT INTO `eb_system_menu` VALUES (753, 713, '商品上下架', '', 'merchant:mp:live:room:goodsonsale', '', 'A', 1, 1, 0, 4, '2023-03-30 16:18:58', '2023-03-30 16:18:58');
INSERT INTO `eb_system_menu` VALUES (754, 713, '商品排序', '', 'merchant:mp:live:room:goodssort', '', 'A', 1, 1, 0, 4, '2023-03-30 16:19:15', '2023-03-30 16:19:15');
INSERT INTO `eb_system_menu` VALUES (758, 713, '管理直播间小助手', '', 'merchant:mp:live:room:mangerass', '', 'A', 1, 1, 0, 4, '2023-03-30 16:20:36', '2023-04-15 11:47:08');
INSERT INTO `eb_system_menu` VALUES (759, 713, '禁言管理', '', 'merchant:mp:live:room:updatecomment', '', 'A', 1, 1, 0, 4, '2023-03-30 16:21:00', '2023-03-30 16:21:00');
INSERT INTO `eb_system_menu` VALUES (760, 713, '收录', '', 'merchant:mp:live:room:isfeedspublic', '', 'A', 1, 1, 0, 4, '2023-03-30 16:22:45', '2023-03-30 16:22:45');
INSERT INTO `eb_system_menu` VALUES (761, 713, '客服管理', '', 'merchant:mp:live:room:closekf', '', 'A', 1, 1, 0, 4, '2023-03-30 16:23:33', '2023-05-04 11:56:46');
INSERT INTO `eb_system_menu` VALUES (762, 713, '开启回放', '', 'merchant:mp:live:room:updatereplay', '', 'A', 1, 1, 0, 4, '2023-03-30 16:23:52', '2023-03-30 16:23:52');
INSERT INTO `eb_system_menu` VALUES (763, 713, '下载讲解视频', '', 'merchant:mp:live:room:getvideo', '', 'A', 1, 1, 0, 4, '2023-03-30 16:24:25', '2023-03-30 16:24:25');
INSERT INTO `eb_system_menu` VALUES (764, 713, '直播间列表', '', 'merchant:mp:live:room:list', '', 'A', 1, 1, 0, 4, '2023-03-30 17:56:21', '2023-03-30 17:56:21');
INSERT INTO `eb_system_menu` VALUES (765, 688, '直播间列表', '', 'platform:mp:live:room:list', '', 'A', 0, 1, 0, 3, '2023-03-30 18:42:33', '2023-03-30 18:42:33');
INSERT INTO `eb_system_menu` VALUES (766, 739, '系统素材换媒体id', '', 'merchant:mp:media:uploadlocal', '', 'A', 1, 1, 0, 4, '2023-03-30 19:07:23', '2023-03-30 19:07:23');
INSERT INTO `eb_system_menu` VALUES (767, 713, '直播间商品列表', '', 'merchant:mp:live:room:goodslist', '', 'A', 1, 1, 0, 4, '2023-03-31 11:46:06', '2023-03-31 11:46:06');
INSERT INTO `eb_system_menu` VALUES (768, 688, '获取直播间商品列表', '', 'platform:mp:live:room:goodslist', '', 'A', 0, 1, 0, 3, '2023-03-31 11:48:28', '2023-03-31 11:48:28');
INSERT INTO `eb_system_menu` VALUES (769, 739, '永久素材获取', '', 'merchant:mp:media:preserveget', '', 'A', 1, 1, 0, 4, '2023-03-31 14:52:22', '2023-03-31 14:52:22');
INSERT INTO `eb_system_menu` VALUES (770, 739, '永久素材上传', '', 'merchant:mp:media:preserveupload', '', 'A', 1, 1, 0, 4, '2023-03-31 14:52:41', '2023-03-31 14:52:41');
INSERT INTO `eb_system_menu` VALUES (771, 713, '直播间详情', '', 'merchant:mp:live:room:info', '', 'A', 1, 1, 0, 4, '2023-04-03 14:45:14', '2023-04-03 14:45:14');
INSERT INTO `eb_system_menu` VALUES (772, 688, '直播间显示在商城', '', 'platform:mp:live:room:showstore', '', 'A', 0, 1, 0, 3, '2023-04-13 09:53:23', '2023-04-13 09:53:23');
INSERT INTO `eb_system_menu` VALUES (773, 713, '获取直播间小助手', '', 'merchant:mp:live:room:getass', '', 'A', 1, 1, 0, 4, '2023-04-18 16:26:39', '2023-04-18 16:26:39');
INSERT INTO `eb_system_menu` VALUES (774, 714, '商品排序', '商品排序', 'merchant:mp:live:goods:sort', '', 'A', 1, 1, 0, 4, '2023-04-19 12:25:51', '2023-04-19 12:25:51');
INSERT INTO `eb_system_menu` VALUES (775, 15, '页面装修', '', '', '/page/design/devise', 'C', 9999, 1, 0, 3, '2023-05-12 14:12:28', '2024-04-18 15:31:30');
INSERT INTO `eb_system_menu` VALUES (776, 73, '优惠券管理', '', '', '/marketing/PlatformCoupon', 'M', 999, 1, 0, 3, '2023-05-16 14:53:32', '2024-04-18 15:30:11');
INSERT INTO `eb_system_menu` VALUES (777, 776, '优惠券列表', '', 'platform:coupon:page:list', '/marketing/PlatformCoupon/list', 'C', 2, 1, 0, 3, '2023-05-16 14:54:11', '2023-05-16 14:54:11');
INSERT INTO `eb_system_menu` VALUES (778, 776, '领取记录', '', 'platform:coupon:user:page:list', '/marketing/PlatformCoupon/couponRecord', 'C', 0, 1, 0, 3, '2023-05-16 14:55:55', '2023-05-17 12:19:11');
INSERT INTO `eb_system_menu` VALUES (779, 776, '添加优惠券', '', 'platform:coupon:add', '', 'A', 0, 1, 0, 3, '2023-05-16 17:43:39', '2023-09-12 16:36:25');
INSERT INTO `eb_system_menu` VALUES (780, 776, '删除优惠券', '', 'platform:coupon:delete', '', 'A', 0, 1, 0, 3, '2023-05-16 17:44:19', '2023-05-16 17:44:19');
INSERT INTO `eb_system_menu` VALUES (781, 776, '优惠券开关', '', 'platform:coupon:switch', '', 'A', 0, 1, 0, 3, '2023-05-16 17:44:39', '2023-05-16 17:44:39');
INSERT INTO `eb_system_menu` VALUES (782, 776, '优惠券详情', '', 'platform:coupon:detail', '', 'A', 0, 1, 0, 3, '2023-05-16 17:45:01', '2023-05-16 17:45:01');
INSERT INTO `eb_system_menu` VALUES (783, 776, '批量发送优惠券', '', 'platform:coupon:batch:send', '', 'A', 0, 1, 0, 3, '2023-05-16 17:45:28', '2023-05-16 17:45:28');
INSERT INTO `eb_system_menu` VALUES (784, 776, '可发送优惠券列表', '', 'platform:coupon:can:send:list', '', 'A', 0, 1, 0, 3, '2023-05-16 17:45:59', '2023-05-16 17:45:59');
INSERT INTO `eb_system_menu` VALUES (785, 776, '优惠券编辑', '', 'platform:coupon:update', '', 'A', 0, 1, 0, 3, '2023-05-23 09:50:44', '2023-05-23 09:50:44');
INSERT INTO `eb_system_menu` VALUES (786, 776, '用户有礼', '', 'platform:marketing:activity:new:people:present:config', '/marketing/PlatformCoupon/newGift', 'C', 0, 1, 0, 3, '2023-05-27 09:28:36', '2023-06-10 15:05:19');
INSERT INTO `eb_system_menu` VALUES (787, 776, '编辑新人礼', '', 'platform:marketing:activity:new:people:present:edit', '', 'A', 0, 1, 0, 3, '2023-05-27 09:32:39', '2023-05-27 09:32:39');
INSERT INTO `eb_system_menu` VALUES (788, 776, '获取生日礼', '', 'platform:marketing:activity:birthday:present:config', '', 'A', 0, 1, 0, 3, '2023-05-27 09:33:13', '2023-05-27 09:33:13');
INSERT INTO `eb_system_menu` VALUES (789, 776, '编辑生日礼', '', 'platform:marketing:activity:birthday:present:edit', '', 'A', 0, 1, 0, 3, '2023-05-27 09:33:45', '2023-05-27 09:33:45');
INSERT INTO `eb_system_menu` VALUES (790, 775, 'diyAPI', '', '', '', 'M', 0, 0, 0, 3, '2023-05-31 17:34:35', '2023-05-31 17:34:35');
INSERT INTO `eb_system_menu` VALUES (791, 775, 'diyLinkAPI', '', '', '', 'M', 0, 0, 0, 3, '2023-05-31 17:34:59', '2023-05-31 17:34:59');
INSERT INTO `eb_system_menu` VALUES (792, 790, 'diy列表', '', 'platform:pagediy:list', '', 'A', 0, 1, 0, 3, '2023-05-31 17:35:15', '2023-05-31 17:35:15');
INSERT INTO `eb_system_menu` VALUES (793, 790, 'diy设置首页', '', 'platform:pagediy:setdefault', '', 'A', 0, 1, 0, 3, '2023-05-31 17:35:53', '2023-05-31 17:35:53');
INSERT INTO `eb_system_menu` VALUES (794, 790, 'diy新增', '', 'platform:pagediy:save', '', 'A', 0, 1, 0, 3, '2023-05-31 17:36:15', '2023-05-31 17:36:15');
INSERT INTO `eb_system_menu` VALUES (795, 790, 'diy删除', '', 'platform:pagediy:delete', '', 'A', 0, 1, 0, 3, '2023-05-31 17:36:36', '2023-05-31 17:36:36');
INSERT INTO `eb_system_menu` VALUES (796, 790, 'diy修改', '', 'platform:pagediy:update', '', 'A', 0, 1, 0, 3, '2023-05-31 17:36:55', '2023-05-31 17:36:55');
INSERT INTO `eb_system_menu` VALUES (797, 790, 'diy详情', '', 'platform:pagediy:info', '', 'A', 0, 1, 0, 3, '2023-05-31 17:37:16', '2023-05-31 17:37:16');
INSERT INTO `eb_system_menu` VALUES (798, 791, '微页面路径你被奥', '', 'platform:pagelink:list', '', 'A', 0, 1, 0, 3, '2023-05-31 17:40:49', '2023-05-31 17:40:49');
INSERT INTO `eb_system_menu` VALUES (799, 791, '微页面路径新增', '', 'platform:pagelink:save', '', 'A', 0, 1, 0, 3, '2023-05-31 17:41:13', '2023-05-31 17:41:13');
INSERT INTO `eb_system_menu` VALUES (800, 791, '微页面删除', '', 'platform:pagelink:delete', '', 'A', 0, 1, 0, 3, '2023-05-31 17:43:08', '2023-05-31 17:43:08');
INSERT INTO `eb_system_menu` VALUES (801, 791, '微页面编辑', '', 'platform:pagelink:update', '', 'A', 0, 1, 0, 3, '2023-05-31 17:43:27', '2023-05-31 17:43:27');
INSERT INTO `eb_system_menu` VALUES (802, 791, '微页面详情', '', 'platform:pagelink:info', '', 'A', 0, 1, 0, 3, '2023-05-31 17:43:50', '2023-05-31 17:43:50');
INSERT INTO `eb_system_menu` VALUES (803, 5, '流水管理', '', '', '/finance/journalAccount', 'M', 0, 1, 0, 3, '2023-06-10 12:11:41', '2024-04-18 15:32:19');
INSERT INTO `eb_system_menu` VALUES (804, 803, '流水汇总', '', '', '/finance/journalAccount/summaryCapitalFlow', 'C', 0, 1, 0, 3, '2023-06-10 12:15:17', '2023-06-10 12:15:17');
INSERT INTO `eb_system_menu` VALUES (805, 804, '流水汇总分页列表', '', 'platform:finance:summary:financial:statements', '', 'A', 0, 1, 0, 3, '2023-06-10 15:06:19', '2023-06-10 15:06:41');
INSERT INTO `eb_system_menu` VALUES (806, 61, '优惠券协议 详情', '', 'platform:system:agreement:coupon:agreement:info', '', 'A', 0, 1, 0, 3, '2023-06-10 15:23:35', '2023-06-10 15:23:59');
INSERT INTO `eb_system_menu` VALUES (807, 61, '优惠券协议保存', '', 'platform:system:agreement:coupon:agreement:save', '', 'A', 0, 1, 0, 3, '2023-06-10 15:24:30', '2023-06-10 15:24:30');
INSERT INTO `eb_system_menu` VALUES (808, 790, '生成微信小程序码', '', 'platform:wechat:mini:genqrcode', '', 'A', 0, 1, 0, 3, '2023-06-27 12:23:13', '2023-06-27 12:23:13');
INSERT INTO `eb_system_menu` VALUES (809, 44, '导出订单Excel', '', 'platform:export:order:excel', '', 'A', 0, 1, 0, 3, '2023-06-28 15:32:45', '2023-06-28 15:32:45');
INSERT INTO `eb_system_menu` VALUES (810, 186, '商户导出订单Excel', '', 'merchant:export:order:excel', '', 'A', 1, 1, 0, 4, '2023-06-28 17:53:54', '2023-06-28 17:53:54');
INSERT INTO `eb_system_menu` VALUES (811, 790, '获取已经设置的首页', '', 'platform:pagediy:getdefault', '', 'A', 0, 1, 0, 3, '2023-06-29 15:13:24', '2023-06-29 15:13:24');
INSERT INTO `eb_system_menu` VALUES (813, 790, 'diy模版名称修改', '', 'platform:pagediy:updatename', '', 'A', 0, 1, 0, 3, '2023-07-18 09:50:21', '2023-07-18 09:50:21');
INSERT INTO `eb_system_menu` VALUES (814, 2, '商品标签', '', '', '/product/tag', 'C', 0, 1, 0, 3, '2023-09-08 09:39:19', '2024-04-18 15:29:08');
INSERT INTO `eb_system_menu` VALUES (815, 172, '商品标签', '', '', '/product/label', 'C', 9, 0, 0, 4, '2023-09-08 09:45:30', '2023-12-22 17:53:56');
INSERT INTO `eb_system_menu` VALUES (816, 1037, '小票打印', '', '', '/operation/printing/printreceipt', 'C', 1, 1, 0, 4, '2023-09-20 17:46:34', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (817, 176, '小票打印权限', '', '', '', 'M', 1, 0, 1, 4, '2023-09-20 17:47:03', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (818, 816, '小票打印机列表', '', 'merchant:admin:print:list', '', 'A', 1, 1, 0, 4, '2023-09-20 17:47:39', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (819, 816, '小票打印机新增', '', 'merchant:admin:print:save', '', 'A', 1, 1, 0, 4, '2023-09-20 17:48:28', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (820, 816, '小票打印机删除', '', 'merchant:admin:print:delete', '', 'A', 1, 1, 0, 4, '2023-09-20 17:48:56', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (821, 816, '小票打印机编辑', '', 'merchant:admin:print:edit', '', 'A', 1, 1, 0, 4, '2023-09-20 17:49:19', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (822, 816, '小票打印机详情', '', 'merchant:admin:print:info', '', 'A', 1, 1, 0, 4, '2023-09-20 17:49:47', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (823, 6, 'PC商城设置', '', '', '/operation/pcConfig', 'M', 9779, 1, 0, 3, '2023-09-25 14:49:03', '2024-04-18 15:32:36');
INSERT INTO `eb_system_menu` VALUES (824, 823, '基础设置', '', '', '/operation/pcConfig/basicSettings', 'M', 0, 1, 0, 3, '2023-09-25 14:55:56', '2024-04-11 17:12:11');
INSERT INTO `eb_system_menu` VALUES (825, 823, '首页设置', '', '', '/operation/pcConfig/homeSettings', 'M', 0, 1, 0, 3, '2023-09-25 14:56:52', '2024-04-11 17:12:24');
INSERT INTO `eb_system_menu` VALUES (826, 823, '底部授权', '', '', '/operation/pcConfig/empower', 'M', 0, 1, 0, 3, '2023-09-25 14:57:30', '2024-04-11 17:12:30');
INSERT INTO `eb_system_menu` VALUES (827, 824, '获取PC商城基础配置', '', 'platform:pc:shopping:base:config:get', '', 'A', 0, 1, 0, 3, '2023-09-26 14:50:27', '2023-09-26 14:50:27');
INSERT INTO `eb_system_menu` VALUES (828, 824, '编辑PC商城基础配置', '', 'platform:pc:shopping:base:config:edit', '', 'A', 0, 1, 0, 3, '2023-09-26 14:50:43', '2023-09-26 14:50:43');
INSERT INTO `eb_system_menu` VALUES (829, 825, '获取PC商城首页banner', '', 'platform:pc:shopping:home:banner:get', '', 'A', 0, 1, 0, 3, '2023-09-26 14:51:01', '2023-09-26 14:51:01');
INSERT INTO `eb_system_menu` VALUES (830, 825, '保存PC商城首页banner', '', 'platform:pc:shopping:home:banner:save', '', 'A', 0, 1, 0, 3, '2023-09-26 14:51:21', '2023-09-26 14:51:21');
INSERT INTO `eb_system_menu` VALUES (831, 825, '获取PC商城首页推荐', '', 'platform:pc:shopping:home:recommended:list', '', 'A', 0, 1, 0, 3, '2023-09-26 14:51:45', '2023-09-26 14:51:45');
INSERT INTO `eb_system_menu` VALUES (832, 825, '添加PC商城首页推荐板块', '', 'platform:pc:shopping:home:recommended:add', '', 'A', 0, 1, 0, 3, '2023-09-26 14:52:01', '2023-09-26 14:52:01');
INSERT INTO `eb_system_menu` VALUES (833, 825, '编辑PC商城首页推荐板块', '', 'platform:pc:shopping:home:recommended:edit', '', 'A', 0, 1, 0, 3, '2023-09-26 14:52:17', '2023-09-26 14:52:17');
INSERT INTO `eb_system_menu` VALUES (834, 825, '删除PC商城首页推荐板块', '', 'platform:pc:shopping:home:recommended:delete', '', 'A', 0, 1, 0, 3, '2023-09-26 14:52:32', '2023-09-26 14:52:32');
INSERT INTO `eb_system_menu` VALUES (835, 825, 'PC商城首页推荐板块开关', '', 'platform:pc:shopping:home:recommended:switch', '', 'A', 0, 1, 0, 3, '2023-09-26 14:52:52', '2023-09-26 14:52:52');
INSERT INTO `eb_system_menu` VALUES (836, 826, '获取PC商城经营理念配置', '', 'platform:pc:shopping:philosophy:get', '', 'A', 0, 1, 0, 3, '2023-09-26 14:53:14', '2023-09-26 14:53:14');
INSERT INTO `eb_system_menu` VALUES (837, 826, '保存PC商城经营理念配置', '', 'platform:pc:shopping:philosophy:save', '', 'A', 0, 1, 0, 3, '2023-09-26 14:53:30', '2023-09-26 14:53:30');
INSERT INTO `eb_system_menu` VALUES (838, 826, '获取PC商城底部授权配置', '', 'platform:pc:shopping:bottom:authorize:get', '', 'A', 0, 1, 0, 3, '2023-09-26 14:53:46', '2023-09-26 14:53:46');
INSERT INTO `eb_system_menu` VALUES (839, 826, '保存PC商城底部授权配置', '', 'platform:pc:shopping:bottom:authorize:save', '', 'A', 0, 1, 0, 3, '2023-09-26 14:53:59', '2023-09-26 14:54:41');
INSERT INTO `eb_system_menu` VALUES (840, 816, '小票打印列表状态更新', '', 'merchant:admin:print:update:status', '', 'A', 1, 1, 0, 4, '2023-09-27 15:25:10', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (841, 186, '小票打印', '', 'merchant:order:print', '', 'A', 1, 1, 0, 4, '2023-09-27 17:39:09', '2023-09-27 17:39:09');
INSERT INTO `eb_system_menu` VALUES (842, 814, '商品标签列表', '', 'platform:product:tag:list', '', 'A', 0, 1, 0, 3, '2023-10-12 11:58:19', '2023-10-12 11:58:19');
INSERT INTO `eb_system_menu` VALUES (843, 814, '商品标签新增', '', 'platform:product:tag:save', '', 'A', 0, 1, 0, 3, '2023-10-12 11:58:48', '2023-10-12 11:58:48');
INSERT INTO `eb_system_menu` VALUES (844, 814, '商品标签删除', '', 'platform:product:tag:delete', '', 'A', 0, 1, 0, 3, '2023-10-12 11:59:09', '2023-10-12 11:59:09');
INSERT INTO `eb_system_menu` VALUES (845, 814, '商品标签修改', '', 'platform:product:tag:update', '', 'A', 0, 1, 0, 3, '2023-10-12 11:59:31', '2023-10-12 11:59:31');
INSERT INTO `eb_system_menu` VALUES (846, 814, '商品标签详情', '', 'platform:product:tag:info', '', 'A', 0, 1, 0, 3, '2023-10-12 11:59:55', '2023-10-12 11:59:55');
INSERT INTO `eb_system_menu` VALUES (847, 826, '获取PC商城快捷入口配置', '', 'platform:pc:shopping:quick:entry:get', '', 'A', 0, 1, 0, 3, '2023-10-19 16:29:47', '2023-12-06 10:53:03');
INSERT INTO `eb_system_menu` VALUES (848, 826, '保存PC商城快捷入口配置', '', 'platform:pc:shopping:quick:entry:save', '', 'A', 0, 1, 0, 3, '2023-10-19 16:30:04', '2023-12-06 10:54:37');
INSERT INTO `eb_system_menu` VALUES (849, 193, '获取商户PC商城设置', '', 'merchant:config:pc:shopping:get', '', 'A', 1, 1, 0, 4, '2023-10-23 14:56:20', '2023-11-06 17:41:29');
INSERT INTO `eb_system_menu` VALUES (850, 193, '编辑商户PC商城设置', '', 'merchant:config:pc:shopping:save', '', 'A', 1, 1, 0, 4, '2023-10-23 14:56:37', '2023-11-06 17:41:40');
INSERT INTO `eb_system_menu` VALUES (851, 826, '获取PC商城底部二维码配置', '', 'platform:pc:shopping:bottom:qrcode:get', '', 'A', 0, 1, 0, 3, '2023-10-23 16:51:04', '2023-12-06 10:54:47');
INSERT INTO `eb_system_menu` VALUES (852, 826, '保存PC商城底部二维码配置', '', 'platform:pc:shopping:bottom:qrcode:save', '', 'A', 0, 1, 0, 3, '2023-10-23 16:51:21', '2023-12-06 10:54:55');
INSERT INTO `eb_system_menu` VALUES (853, 826, '获取PC商城友情链接配置', '', 'platform:pc:shopping:friendly:link:get', '', 'A', 0, 1, 0, 3, '2023-10-23 17:00:20', '2023-12-06 10:55:10');
INSERT INTO `eb_system_menu` VALUES (854, 826, '保存PC商城友情链接配置', '', 'platform:pc:shopping:friendly:link:save', '', 'A', 0, 1, 0, 3, '2023-10-23 17:00:50', '2023-12-06 10:55:03');
INSERT INTO `eb_system_menu` VALUES (855, 825, '获取PC商城首页广告', '', 'platform:pc:shopping:home:advertisement:get', '', 'A', 0, 1, 0, 3, '2023-10-23 17:50:46', '2023-10-23 17:50:46');
INSERT INTO `eb_system_menu` VALUES (856, 825, '编辑PC商城首页广告', '', 'platform:pc:shopping:home:advertisement:edit', '', 'A', 0, 1, 0, 3, '2023-10-23 17:51:06', '2023-10-23 17:51:06');
INSERT INTO `eb_system_menu` VALUES (857, 814, '商品标签状态', '', 'platform:product:tag:status', '', 'A', 0, 1, 0, 3, '2023-11-02 17:22:39', '2023-11-02 17:22:39');
INSERT INTO `eb_system_menu` VALUES (858, 176, '商家地址管理', '', '', '/operation/businessAddress', 'M', 1, 1, 0, 4, '2023-12-07 14:07:35', '2023-12-07 14:10:16');
INSERT INTO `eb_system_menu` VALUES (859, 858, '商户地址列表', '', 'merchant:address:list', '', 'A', 1, 1, 0, 4, '2023-12-07 16:26:35', '2023-12-07 16:26:35');
INSERT INTO `eb_system_menu` VALUES (860, 858, '新增商户地址', '', 'merchant:address:add', '', 'A', 1, 1, 0, 4, '2023-12-07 16:27:07', '2023-12-07 16:27:07');
INSERT INTO `eb_system_menu` VALUES (861, 858, '删除商户地址', '', 'merchant:address:delete', '', 'A', 1, 1, 0, 4, '2023-12-07 16:27:23', '2023-12-07 16:27:23');
INSERT INTO `eb_system_menu` VALUES (862, 858, '修改商户地址', '', 'merchant:address:update', '', 'A', 1, 1, 0, 4, '2023-12-07 16:27:43', '2023-12-07 16:27:43');
INSERT INTO `eb_system_menu` VALUES (863, 858, '设置商户默认地址', '', 'merchant:address:set:default', '', 'A', 1, 1, 0, 4, '2023-12-07 16:27:59', '2023-12-07 16:27:59');
INSERT INTO `eb_system_menu` VALUES (864, 858, '设置商户地址开启状态', '', 'merchant:address:update:show', '', 'A', 1, 1, 0, 4, '2023-12-07 16:28:17', '2023-12-07 16:28:17');
INSERT INTO `eb_system_menu` VALUES (865, 187, '退款单审核', '', 'merchant:refund:order:audit', '', 'A', 1, 1, 0, 4, '2023-12-12 17:23:38', '2023-12-12 17:23:38');
INSERT INTO `eb_system_menu` VALUES (866, 187, '退款单收到退货', '', 'merchant:refund:order:receiving', '', 'A', 1, 1, 0, 4, '2023-12-12 17:23:56', '2023-12-12 17:23:56');
INSERT INTO `eb_system_menu` VALUES (867, 187, '退款单拒绝收货', '', 'merchant:refund:order:receiving:reject', '', 'A', 1, 1, 0, 4, '2023-12-12 17:24:16', '2023-12-12 17:24:16');
INSERT INTO `eb_system_menu` VALUES (868, 44, '平台强制退款', '', 'platform:refund:order:compulsory:refund', '', 'A', 0, 1, 0, 3, '2023-12-19 11:31:35', '2023-12-19 11:31:35');
INSERT INTO `eb_system_menu` VALUES (869, 71, '发货管理', '', '', '/operation/application/publicRoutine/deliveryManagement', 'M', 0, 1, 0, 3, '2023-12-27 17:15:15', '2023-12-27 17:33:57');
INSERT INTO `eb_system_menu` VALUES (870, 8, '修改登录用户密码', '', 'platform:login:admin:update:password', '', 'A', 0, 1, 0, 3, '2023-12-28 16:35:39', '2023-12-28 16:35:39');
INSERT INTO `eb_system_menu` VALUES (871, 180, '修改登录用户密码', '', 'merchant:login:admin:update:password', '', 'A', 1, 1, 0, 4, '2023-12-28 16:36:12', '2023-12-28 16:36:12');
INSERT INTO `eb_system_menu` VALUES (872, 20, '修改后台管理员密码', '', 'platform:admin:update:password', '', 'A', 0, 1, 0, 3, '2023-12-28 16:37:12', '2023-12-29 10:52:09');
INSERT INTO `eb_system_menu` VALUES (873, 219, '修改后台管理员密码', '', 'merchant:admin:update:password', '', 'A', 1, 1, 0, 4, '2023-12-28 16:37:52', '2023-12-29 10:42:19');
INSERT INTO `eb_system_menu` VALUES (874, 825, '获取PC商城首页导航配置', '', 'platform:pc:shopping:home:navigation:get', '', 'A', 0, 1, 0, 3, '2023-12-28 16:55:45', '2023-12-28 16:55:45');
INSERT INTO `eb_system_menu` VALUES (875, 825, '保存PC商城首页导航配置', '', 'platform:pc:shopping:home:navigation:save', '', 'A', 0, 1, 0, 3, '2023-12-28 16:56:06', '2023-12-28 16:56:06');
INSERT INTO `eb_system_menu` VALUES (876, 219, '商户后台管理员短信开关', '', 'merchant:admin:update:receive:sms', '', 'A', 1, 1, 0, 4, '2024-01-02 10:37:42', '2024-01-02 10:37:42');
INSERT INTO `eb_system_menu` VALUES (877, 869, '获取微信小程序发货开关', '', 'platform:wechat:mini:shipping:switch:get', '', 'A', 0, 1, 0, 3, '2024-01-03 16:40:31', '2024-01-03 16:40:31');
INSERT INTO `eb_system_menu` VALUES (878, 869, '更新微信小程序发货开关', '', 'platform:wechat:mini:shipping:switch:update', '', 'A', 0, 1, 0, 3, '2024-01-03 16:40:46', '2024-01-03 16:40:46');
INSERT INTO `eb_system_menu` VALUES (879, 22, '获取主题色', '', 'platform:system:config:change:color:get', '', 'A', 0, 1, 0, 3, '2024-01-04 10:22:48', '2024-01-04 10:22:48');
INSERT INTO `eb_system_menu` VALUES (880, 22, '保存主题色', '', 'platform:system:config:change:color:save', '', 'A', 0, 1, 0, 3, '2024-01-04 10:23:07', '2024-01-04 10:23:07');
INSERT INTO `eb_system_menu` VALUES (881, 71, '链接生成器', '', '', '/operation/application/publicRoutine/linkGenerator', 'M', 0, 1, 0, 3, '2024-01-29 12:16:42', '2024-01-29 12:16:42');
INSERT INTO `eb_system_menu` VALUES (882, 881, '生成微信小程序加密URLLink', '', 'platform:wechat:mini:generate:url:link', '', 'A', 0, 1, 0, 3, '2024-02-21 17:33:54', '2024-02-21 17:33:54');
INSERT INTO `eb_system_menu` VALUES (883, 881, '微信小程序url链接分页列表', '', 'platform:wechat:mini:url:link:page:list', '', 'A', 0, 1, 0, 3, '2024-02-21 17:34:15', '2024-02-21 17:34:15');
INSERT INTO `eb_system_menu` VALUES (884, 881, '删除微信小程序加密URLLink', '', 'platform:wechat:mini:delete:url:link', '', 'A', 0, 1, 0, 3, '2024-02-21 17:34:32', '2024-02-21 17:34:32');
INSERT INTO `eb_system_menu` VALUES (885, 329, '一号通配置', '', '', '/operation/onePass/onePassConfig', 'M', 1, 1, 0, 3, '2024-02-22 10:36:48', '2024-02-22 10:48:29');
INSERT INTO `eb_system_menu` VALUES (886, 329, '一号通', '', '', '/operation/onePass/home', 'M', 0, 1, 0, 3, '2024-02-22 10:45:41', '2024-02-22 10:46:14');
INSERT INTO `eb_system_menu` VALUES (887, 885, '一号通应用信息保存', '', 'platform:one:pass:app:save', '', 'A', 0, 1, 0, 3, '2024-02-22 10:55:37', '2024-02-22 10:55:37');
INSERT INTO `eb_system_menu` VALUES (888, 885, '一号通应用信息获取', '', 'platform:one:pass:app:get', '', 'A', 0, 1, 0, 3, '2024-02-22 10:55:56', '2024-02-22 10:55:56');
INSERT INTO `eb_system_menu` VALUES (889, 172, '卡密管理', '', '', '/product/cdkey', 'C', 11, 1, 0, 4, '2024-02-24 15:32:12', '2024-04-16 09:46:19');
INSERT INTO `eb_system_menu` VALUES (890, 889, '卡密库分页列表', '', 'merchant:cdkey:library:page:list', '', 'A', 1, 1, 0, 4, '2024-02-24 15:33:45', '2024-02-24 15:33:45');
INSERT INTO `eb_system_menu` VALUES (891, 889, '新增卡密库', '', 'merchant:cdkey:library:add', '', 'A', 1, 1, 0, 4, '2024-02-24 15:34:01', '2024-02-24 15:34:01');
INSERT INTO `eb_system_menu` VALUES (892, 889, '删除卡密库', '', 'merchant:cdkey:library:delete', '', 'A', 1, 1, 0, 4, '2024-02-24 15:34:23', '2024-02-24 15:34:23');
INSERT INTO `eb_system_menu` VALUES (893, 889, '修改卡密库', '', 'merchant:cdkey:library:update', '', 'A', 1, 1, 0, 4, '2024-02-24 15:34:38', '2024-02-24 15:34:38');
INSERT INTO `eb_system_menu` VALUES (894, 889, '未关联卡密库列表', '', 'merchant:cdkey:library:unrelated:list', '', 'A', 1, 1, 0, 4, '2024-02-24 15:34:57', '2024-02-24 15:34:57');
INSERT INTO `eb_system_menu` VALUES (895, 889, '卡密分页列表', '', 'merchant:card:secret:page:list', '', 'A', 1, 1, 0, 4, '2024-02-24 15:35:19', '2024-02-24 15:35:19');
INSERT INTO `eb_system_menu` VALUES (896, 889, '新增卡密', '', 'merchant:card:secret:add', '', 'A', 1, 1, 0, 4, '2024-02-24 15:35:35', '2024-02-24 15:35:35');
INSERT INTO `eb_system_menu` VALUES (897, 889, '删除卡密', '', 'merchant:card:secret:delete', '', 'A', 1, 1, 0, 4, '2024-02-24 15:35:50', '2024-02-24 15:35:50');
INSERT INTO `eb_system_menu` VALUES (898, 889, '修改卡密', '', 'merchant:cdkey:library:update', '', 'A', 1, 1, 0, 4, '2024-02-24 15:36:06', '2024-02-24 15:36:06');
INSERT INTO `eb_system_menu` VALUES (899, 889, '导入卡密', '', 'merchant:cdkey:library:import:excel', '', 'A', 1, 1, 0, 4, '2024-02-24 15:36:22', '2024-02-24 15:36:22');
INSERT INTO `eb_system_menu` VALUES (900, 889, '批量删除卡密', '', 'merchant:card:secret:batch:delete', '', 'A', 1, 1, 0, 4, '2024-02-28 17:22:05', '2024-02-28 17:22:05');
INSERT INTO `eb_system_menu` VALUES (901, 182, '设置运费模板', '', 'merchant:product:set:freight:template', '', 'A', 1, 1, 0, 4, '2024-03-06 10:16:26', '2024-03-06 10:16:26');
INSERT INTO `eb_system_menu` VALUES (902, 182, '设置佣金', '', 'merchant:product:set:brokerage', '', 'A', 1, 1, 0, 4, '2024-03-06 10:16:42', '2024-03-06 10:16:42');
INSERT INTO `eb_system_menu` VALUES (903, 182, '添加回馈券', '', 'merchant:product:add:feedback:coupons', '', 'A', 1, 1, 0, 4, '2024-03-06 10:16:57', '2024-03-06 10:16:57');
INSERT INTO `eb_system_menu` VALUES (904, 182, '批量上架商品', '', 'merchant:product:batch:up', '', 'A', 1, 1, 0, 4, '2024-03-06 10:17:13', '2024-03-06 10:17:13');
INSERT INTO `eb_system_menu` VALUES (905, 182, '批量商品下架', '', 'merchant:product:batch:down', '', 'A', 1, 1, 0, 4, '2024-03-06 10:17:29', '2024-03-06 10:17:29');
INSERT INTO `eb_system_menu` VALUES (906, 182, '批量设置运费模板', '', 'merchant:product:batch:set:freight:template', '', 'A', 1, 1, 0, 4, '2024-03-06 10:17:46', '2024-03-06 10:17:46');
INSERT INTO `eb_system_menu` VALUES (907, 182, '批量设置佣金', '', 'merchant:product:batch:set:brokerage', '', 'A', 1, 1, 0, 4, '2024-03-06 10:18:01', '2024-03-06 10:18:01');
INSERT INTO `eb_system_menu` VALUES (908, 182, '批量添加回馈券', '', 'merchant:product:batch:add:feedback:coupons', '', 'A', 1, 1, 0, 4, '2024-03-06 10:18:15', '2024-03-06 10:18:15');
INSERT INTO `eb_system_menu` VALUES (909, 182, '批量加入回收站', '', 'merchant:product:batch:recycle', '', 'A', 1, 1, 0, 4, '2024-03-06 10:18:31', '2024-03-06 10:18:31');
INSERT INTO `eb_system_menu` VALUES (910, 182, '批量删除商品', '', 'merchant:product:batch:delete', '', 'A', 1, 1, 0, 4, '2024-03-06 10:18:47', '2024-03-06 10:18:47');
INSERT INTO `eb_system_menu` VALUES (911, 182, '批量恢复回收站商品', '', 'merchant:product:batch:restore', '', 'A', 1, 1, 0, 4, '2024-03-06 10:19:03', '2024-03-06 10:19:03');
INSERT INTO `eb_system_menu` VALUES (912, 182, '批量提审商品', '', 'merchant:product:batch:submit:audit', '', 'A', 1, 1, 0, 4, '2024-03-06 10:19:20', '2024-03-06 10:19:20');
INSERT INTO `eb_system_menu` VALUES (913, 43, '批量设置虚拟销量', '', 'platform:product:batch:set:virtual:sales', '', 'A', 0, 1, 0, 3, '2024-03-08 10:18:46', '2024-03-08 10:18:46');
INSERT INTO `eb_system_menu` VALUES (914, 43, '批量商品审核', '', 'platform:product:batch:audit', '', 'A', 0, 1, 0, 3, '2024-03-08 10:19:11', '2024-03-08 10:19:11');
INSERT INTO `eb_system_menu` VALUES (915, 186, '商户直接退款', '', 'merchant:order:direct:refund', '', 'A', 1, 1, 0, 4, '2024-03-22 14:30:42', '2024-03-22 14:30:42');
INSERT INTO `eb_system_menu` VALUES (916, 4, '付费会员', '', '', '/user/paidMembers', 'C', 0, 1, 0, 3, '2024-05-08 09:44:55', '2024-05-13 16:33:01');
INSERT INTO `eb_system_menu` VALUES (917, 916, '会员配置', '', '', '/user/paidMembers/configure', 'C', 0, 1, 0, 3, '2024-05-08 09:46:48', '2024-05-13 16:33:08');
INSERT INTO `eb_system_menu` VALUES (918, 916, '购买记录', '', '', '/user/paidMembers/order', 'C', 0, 1, 0, 3, '2024-05-08 09:52:23', '2024-06-12 10:14:41');
INSERT INTO `eb_system_menu` VALUES (919, 176, '物流管理', '', '', '/operation/logisticsManagement', 'C', 9, 1, 0, 4, '2024-05-08 10:58:14', '2024-05-08 11:14:37');
INSERT INTO `eb_system_menu` VALUES (920, 919, '商户关联物流公司', '', 'merchant:express:relate', '', 'A', 1, 1, 0, 4, '2024-05-08 10:59:02', '2024-05-08 10:59:02');
INSERT INTO `eb_system_menu` VALUES (921, 919, '商户物流公司分页列表', '', 'merchant:express:search:page', '', 'A', 1, 1, 0, 4, '2024-05-08 10:59:31', '2024-05-08 10:59:31');
INSERT INTO `eb_system_menu` VALUES (922, 919, '商户物流公司开关', '', 'merchant:express:open:switch', '', 'A', 1, 1, 0, 4, '2024-05-08 10:59:47', '2024-05-08 10:59:47');
INSERT INTO `eb_system_menu` VALUES (923, 919, '商户物流公司默认开关', '', 'merchant:express:default:switch', '', 'A', 1, 1, 0, 4, '2024-05-08 11:00:04', '2024-05-08 11:00:04');
INSERT INTO `eb_system_menu` VALUES (924, 919, '商户物流公司删除', '', 'merchant:express:delete', '', 'A', 1, 1, 0, 4, '2024-05-08 11:00:18', '2024-05-08 11:00:18');
INSERT INTO `eb_system_menu` VALUES (925, 917, '付费会员基础配置信息获取', '', 'platform:paid:member:base:config:get', '', 'A', 0, 1, 0, 3, '2024-05-14 11:41:17', '2024-05-14 11:41:17');
INSERT INTO `eb_system_menu` VALUES (926, 917, '编辑付费会员基础配置', '', 'platform:paid:member:base:config:edit', '', 'A', 0, 1, 0, 3, '2024-05-14 11:41:38', '2024-05-14 11:41:38');
INSERT INTO `eb_system_menu` VALUES (927, 917, '获取付费会员会员权益', '', 'platform:paid:member:benefits:list', '', 'A', 0, 1, 0, 3, '2024-05-14 11:43:38', '2024-05-14 11:43:38');
INSERT INTO `eb_system_menu` VALUES (928, 917, '编辑付费会员会员权益', '', 'platform:paid:member:benefits:edit', '', 'A', 0, 1, 0, 3, '2024-05-14 11:44:29', '2024-05-14 11:44:29');
INSERT INTO `eb_system_menu` VALUES (929, 917, '付费会员会员权益开关', '', 'platform:paid:member:benefits:switch', '', 'A', 0, 1, 0, 3, '2024-05-14 11:44:49', '2024-05-14 11:44:49');
INSERT INTO `eb_system_menu` VALUES (930, 917, '编辑付费会员会员权益说明', '', 'platform:paid:member:benefits:statement:edit', '', 'A', 0, 1, 0, 3, '2024-05-14 11:58:44', '2024-05-14 11:58:44');
INSERT INTO `eb_system_menu` VALUES (931, 917, '付费会员卡列表', '', 'platform:paid:member:card:list', '', 'A', 0, 1, 0, 3, '2024-05-14 11:59:06', '2024-05-14 11:59:06');
INSERT INTO `eb_system_menu` VALUES (932, 917, '添加付费会员卡', '', 'platform:paid:member:card:add', '', 'A', 0, 1, 0, 3, '2024-05-14 11:59:40', '2024-05-14 11:59:40');
INSERT INTO `eb_system_menu` VALUES (933, 917, '编辑付费会员卡', '', 'platform:paid:member:card:edit', '', 'A', 0, 1, 0, 3, '2024-05-14 12:00:00', '2024-05-14 12:01:10');
INSERT INTO `eb_system_menu` VALUES (934, 917, '付费会员卡开关', '', 'platform:paid:member:card:switch', '', 'A', 0, 1, 0, 3, '2024-05-14 12:01:43', '2024-05-14 12:02:12');
INSERT INTO `eb_system_menu` VALUES (935, 917, '删除付费会员卡', '', 'platform:paid:member:card:delete', '', 'A', 0, 1, 0, 3, '2024-05-14 12:02:42', '2024-05-14 12:02:42');
INSERT INTO `eb_system_menu` VALUES (936, 918, '付费会员订单分页列表', '', 'platform:paid:member:order:page:list', '', 'A', 0, 1, 0, 3, '2024-05-14 12:03:36', '2024-05-14 12:03:36');
INSERT INTO `eb_system_menu` VALUES (937, 918, '付费会员订单详情', '', 'platform:paid:member:order:info', '', 'A', 0, 1, 0, 3, '2024-05-14 12:03:55', '2024-05-14 12:03:55');
INSERT INTO `eb_system_menu` VALUES (938, 48, '赠送用户付费会员', '', 'platform:user:gift:paid:member', '', 'A', 0, 1, 0, 3, '2024-05-14 12:04:57', '2024-05-14 12:04:57');
INSERT INTO `eb_system_menu` VALUES (939, 61, '付费会员协议保存', '', 'platform:system:agreement:paid:member:save', '', 'A', 0, 1, 0, 3, '2024-05-17 14:46:08', '2024-05-17 14:46:08');
INSERT INTO `eb_system_menu` VALUES (940, 61, '付费会员协议详情', '', 'platform:system:agreement:paid:member:info', '', 'A', 0, 1, 0, 3, '2024-05-17 14:46:38', '2024-05-17 14:46:38');
INSERT INTO `eb_system_menu` VALUES (941, 0, '员工', 's-custom', '', '/staff', 'M', 4000, 1, 0, 4, '2024-05-24 10:56:01', '2024-05-24 10:58:55');
INSERT INTO `eb_system_menu` VALUES (942, 941, '员工管理', '', '', '/staff/index', 'C', 10, 1, 0, 4, '2024-05-24 10:58:32', '2024-05-24 11:26:11');
INSERT INTO `eb_system_menu` VALUES (943, 179, '获取商户端商户商品审核开关信息', '', 'merchant:product:audit:switch:info', '', 'A', 1, 1, 0, 4, '2024-05-30 17:47:46', '2024-05-30 17:47:46');
INSERT INTO `eb_system_menu` VALUES (944, 36, '越权登录商户', '', 'platform:merchant:ultra:vires:login', '', 'A', 0, 1, 0, 3, '2024-07-02 11:19:24', '2024-07-02 11:19:24');
INSERT INTO `eb_system_menu` VALUES (945, 186, '修改发货单配送信息', '', 'merchant:order:invoice:update', '', 'A', 1, 1, 0, 4, '2024-07-09 10:03:57', '2024-07-09 10:03:57');
INSERT INTO `eb_system_menu` VALUES (947, 73, '拼团', '', '', '/marketing/group', 'M', 920, 1, 0, 3, '2024-07-26 10:41:52', '2024-07-26 10:41:52');
INSERT INTO `eb_system_menu` VALUES (948, 947, '拼团活动', '', '', '/marketing/group/activity', 'C', 0, 1, 0, 3, '2024-07-26 10:42:50', '2024-07-26 10:45:38');
INSERT INTO `eb_system_menu` VALUES (949, 947, '拼团记录', '', '', '/marketing/group/list', 'C', 0, 1, 0, 3, '2024-07-26 10:43:21', '2024-07-26 10:45:47');
INSERT INTO `eb_system_menu` VALUES (950, 619, '拼团', '', '', '/marketing/group', 'M', 8000, 1, 0, 4, '2024-07-27 09:42:49', '2024-07-27 09:42:58');
INSERT INTO `eb_system_menu` VALUES (951, 950, '拼团活动', '', '', '/marketing/group/activity', 'C', 1, 1, 0, 4, '2024-07-27 09:45:49', '2024-07-27 09:45:49');
INSERT INTO `eb_system_menu` VALUES (952, 950, '开团记录', '', '', '/marketing/group/list', 'C', 1, 1, 0, 4, '2024-07-27 09:46:14', '2024-07-27 09:46:14');
INSERT INTO `eb_system_menu` VALUES (953, 178, '系统表单', '', '', '/maintain/systemForm', 'M', 1, 1, 0, 4, '2024-08-06 10:17:49', '2024-08-06 10:36:22');
INSERT INTO `eb_system_menu` VALUES (954, 953, '系统表单分页列表', '', 'merchant:system:form:page', '', 'A', 1, 1, 0, 4, '2024-08-06 10:53:24', '2024-08-06 10:53:24');
INSERT INTO `eb_system_menu` VALUES (955, 953, '系统表单详情', '', 'merchant:system:form:detail', '', 'A', 1, 1, 0, 4, '2024-08-06 10:53:44', '2024-08-06 10:53:44');
INSERT INTO `eb_system_menu` VALUES (956, 953, '添加系统表单', '', 'merchant:system:form:add', '', 'A', 1, 1, 0, 4, '2024-08-06 10:54:01', '2024-08-06 10:54:01');
INSERT INTO `eb_system_menu` VALUES (957, 953, '修改系统表单', '', 'merchant:system:form:update', '', 'A', 1, 1, 0, 4, '2024-08-06 10:54:19', '2024-08-06 10:54:19');
INSERT INTO `eb_system_menu` VALUES (958, 953, '删除系统表单', '', 'merchant:system:form:delete', '', 'A', 1, 1, 0, 4, '2024-08-06 10:54:36', '2024-08-06 10:54:36');
INSERT INTO `eb_system_menu` VALUES (959, 73, '积分商城', '', '', '/marketing/pointsMall', 'M', 881, 1, 0, 3, '2024-08-12 16:58:25', '2024-09-14 17:02:19');
INSERT INTO `eb_system_menu` VALUES (960, 959, '商品管理', '', '', '/marketing/pointsMall/productManage', 'C', 0, 1, 0, 3, '2024-08-12 17:07:57', '2024-08-12 17:09:20');
INSERT INTO `eb_system_menu` VALUES (961, 959, '积分区间', '', '', '/marketing/pointsMall/section', 'C', 0, 1, 0, 3, '2024-08-12 17:20:37', '2024-08-12 17:20:37');
INSERT INTO `eb_system_menu` VALUES (962, 959, '积分订单', '', '', '/marketing/pointsMall/order', 'C', 0, 1, 0, 3, '2024-08-12 17:21:09', '2024-08-12 17:21:09');
INSERT INTO `eb_system_menu` VALUES (963, 951, '分页列表', '', 'merchant:groupbuy:activity:list', '', 'A', 1, 1, 0, 4, '2024-08-13 16:38:26', '2024-08-13 16:38:26');
INSERT INTO `eb_system_menu` VALUES (964, 951, '活动新增', '', 'merchant:groupbuy:activity:add', '', 'A', 1, 1, 0, 4, '2024-08-13 16:38:53', '2024-08-13 16:38:53');
INSERT INTO `eb_system_menu` VALUES (965, 951, '活动删除', '', 'merchant:groupbuy:activity:delete', '', 'A', 1, 1, 0, 4, '2024-08-13 16:39:16', '2024-08-13 16:39:16');
INSERT INTO `eb_system_menu` VALUES (966, 951, '活动修改', '', 'merchant:groupbuy:activity:update', '', 'A', 1, 1, 0, 4, '2024-08-13 16:39:41', '2024-08-13 16:39:41');
INSERT INTO `eb_system_menu` VALUES (967, 951, '活动详情', '', 'merchant:groupbuy:activity:info', '', 'A', 1, 1, 0, 4, '2024-08-13 16:40:01', '2024-08-13 16:40:01');
INSERT INTO `eb_system_menu` VALUES (968, 951, '拼团记录列表', '', 'merchant:groupbuy:activity:list:count', '', 'A', 1, 1, 0, 4, '2024-08-14 16:52:07', '2024-09-27 17:37:23');
INSERT INTO `eb_system_menu` VALUES (969, 951, '状态修改', '', 'merchant:groupbuy:activity:change:status', '', 'A', 1, 1, 0, 4, '2024-08-20 11:03:17', '2024-08-20 11:03:17');
INSERT INTO `eb_system_menu` VALUES (970, 951, '审核状态撤回', '', 'merchant:groupbuy:activity:rollback', '', 'A', 1, 1, 0, 4, '2024-08-20 12:13:42', '2024-08-20 12:13:42');
INSERT INTO `eb_system_menu` VALUES (971, 960, '积分商品分页列表', '', 'platform:integral:product:page', '', 'A', 0, 1, 0, 3, '2024-08-20 16:28:03', '2024-08-20 16:28:03');
INSERT INTO `eb_system_menu` VALUES (972, 960, '新增积分商品', '', 'platform:integral:product:save', '', 'A', 0, 1, 0, 3, '2024-08-20 16:28:31', '2024-08-20 16:28:31');
INSERT INTO `eb_system_menu` VALUES (973, 960, '修改积分商品', '', 'platform:integral:product:update', '', 'A', 0, 1, 0, 3, '2024-08-20 16:30:11', '2024-08-20 16:30:11');
INSERT INTO `eb_system_menu` VALUES (974, 960, '积分商品详情', '', 'platform:integral:product:detail', '', 'A', 0, 1, 0, 3, '2024-08-20 16:30:33', '2024-08-20 16:30:33');
INSERT INTO `eb_system_menu` VALUES (975, 960, '积分商品表头数量', '', 'platform:integral:product:tabs:headers', '', 'A', 0, 1, 0, 3, '2024-08-20 16:32:09', '2024-08-20 16:32:09');
INSERT INTO `eb_system_menu` VALUES (976, 960, '删除积分商品', '', 'platform:integral:product:delete', '', 'A', 0, 1, 0, 3, '2024-08-20 16:38:40', '2024-08-20 16:38:40');
INSERT INTO `eb_system_menu` VALUES (977, 961, '积分区间分页列表', '', 'platform:integral:interval:page', '', 'A', 0, 1, 0, 3, '2024-08-20 16:39:02', '2024-08-20 16:39:02');
INSERT INTO `eb_system_menu` VALUES (978, 961, '删除积分区间', '', 'platform:integral:interval:delete', '', 'A', 0, 1, 0, 3, '2024-08-20 16:40:14', '2024-08-20 16:42:39');
INSERT INTO `eb_system_menu` VALUES (979, 961, '新增积分区间', '', 'platform:integral:interval:save', '', 'A', 0, 1, 0, 3, '2024-08-20 16:40:19', '2024-08-20 16:41:23');
INSERT INTO `eb_system_menu` VALUES (980, 961, '修改积分区间', '', 'platform:integral:interval:update', '', 'A', 0, 1, 0, 3, '2024-08-20 16:40:26', '2024-08-20 16:42:15');
INSERT INTO `eb_system_menu` VALUES (985, 948, '分页列表', '', 'platform:groupbuy:activity:list', '', 'A', 0, 1, 0, 3, '2024-08-21 15:43:16', '2024-08-21 15:44:48');
INSERT INTO `eb_system_menu` VALUES (986, 948, '活动进程数量', '', 'platform:groupbuy:activity:list:count', '', 'A', 0, 1, 0, 3, '2024-08-21 15:43:52', '2024-08-21 15:44:56');
INSERT INTO `eb_system_menu` VALUES (987, 948, '活动详情', '', 'platform:groupbuy:activity:info', '', 'A', 0, 1, 0, 3, '2024-08-21 15:45:56', '2024-08-21 15:45:56');
INSERT INTO `eb_system_menu` VALUES (988, 948, '活动审核', '', 'platform:groupbuy:activity:review:pass', '', 'A', 0, 1, 0, 3, '2024-08-21 15:46:18', '2024-08-21 15:46:18');
INSERT INTO `eb_system_menu` VALUES (989, 948, '活动审核拒绝', '', 'platform:groupbuy:activity:review:refuse', '', 'A', 0, 1, 0, 3, '2024-08-21 15:46:50', '2024-08-21 15:46:50');
INSERT INTO `eb_system_menu` VALUES (990, 948, '强制关闭', '', 'platform:groupbuy:activity:review:close', '', 'A', 0, 1, 0, 3, '2024-08-21 15:47:13', '2024-08-21 15:47:13');
INSERT INTO `eb_system_menu` VALUES (991, 962, '积分订单分页列表', '', 'platform:integral:order:page:list', '', 'A', 0, 1, 0, 3, '2024-08-27 16:24:30', '2024-08-27 16:24:30');
INSERT INTO `eb_system_menu` VALUES (992, 962, '积分订单各状态数量', '', 'platform:integral:order:status:num', '', 'A', 0, 1, 0, 3, '2024-08-27 16:24:48', '2024-08-27 16:24:48');
INSERT INTO `eb_system_menu` VALUES (993, 962, '积分订单备注', '', 'platform:integral:order:mark', '', 'A', 0, 1, 0, 3, '2024-08-27 16:25:05', '2024-08-27 16:25:05');
INSERT INTO `eb_system_menu` VALUES (994, 962, '积分订单详情', '', 'platform:integral:order:detail', '', 'A', 0, 1, 0, 3, '2024-08-27 16:25:21', '2024-08-27 16:25:21');
INSERT INTO `eb_system_menu` VALUES (995, 962, '积分订单发货', '', 'platform:integral:order:send', '', 'A', 0, 1, 0, 3, '2024-08-27 16:25:36', '2024-08-27 16:25:36');
INSERT INTO `eb_system_menu` VALUES (996, 962, '获取订单发货单列表', '', 'platform:integral:order:invoice:list', '', 'A', 0, 1, 0, 3, '2024-09-02 17:22:21', '2024-09-02 17:22:21');
INSERT INTO `eb_system_menu` VALUES (997, 962, '订单物流详情', '', 'platform:integral:order:logistics:info', '', 'A', 0, 1, 0, 3, '2024-09-02 17:22:37', '2024-09-02 17:22:37');
INSERT INTO `eb_system_menu` VALUES (998, 962, '修改发货单配送信息', '', 'platform:integral:order:invoice:update', '', 'A', 0, 1, 0, 3, '2024-09-02 17:22:53', '2024-09-02 17:22:53');
INSERT INTO `eb_system_menu` VALUES (999, 960, '积分商品上/下架', '', 'platform:integral:product:update:show', '', 'A', 0, 1, 0, 3, '2024-09-03 17:01:04', '2024-09-03 17:01:04');
INSERT INTO `eb_system_menu` VALUES (1000, 961, '修改积分区间状态', '', 'platform:integral:interval:update:status', '', 'A', 0, 1, 0, 3, '2024-09-05 15:36:56', '2024-09-05 15:36:56');
INSERT INTO `eb_system_menu` VALUES (1001, 2, '商品搜索分页列表（营销）', '', 'platform:product:marketing:search:page', '', 'A', 0, 1, 0, 3, '2024-09-20 11:33:40', '2024-09-20 11:33:40');
INSERT INTO `eb_system_menu` VALUES (1002, 172, '商品搜索分页列表（营销）', '', 'merchant:product:marketing:search:page', '', 'A', 1, 1, 0, 4, '2024-09-20 11:34:25', '2024-09-20 11:34:25');
INSERT INTO `eb_system_menu` VALUES (1003, 949, '拼团开团记录列表', '', 'platform:groupbuy:record:list', '', 'A', 0, 1, 0, 3, '2024-09-27 17:35:01', '2024-09-27 17:35:01');
INSERT INTO `eb_system_menu` VALUES (1004, 949, '拼团开团记录列表表头数量', '', 'platform:groupbuy:record:list:count', '', 'A', 0, 1, 0, 3, '2024-09-27 17:35:23', '2024-09-27 17:35:23');
INSERT INTO `eb_system_menu` VALUES (1005, 949, '拼团开团记录详情', '', 'platform:groupbuy:record:info', '', 'A', 0, 1, 0, 3, '2024-09-27 17:35:49', '2024-09-27 17:35:49');
INSERT INTO `eb_system_menu` VALUES (1006, 952, '拼团开团记录列表', '', 'merchant:groupbuy:record:list', '', 'A', 1, 1, 0, 4, '2024-09-27 17:38:07', '2024-09-27 17:38:07');
INSERT INTO `eb_system_menu` VALUES (1007, 952, '拼团开团记录列表表头数量', '', 'merchant:groupbuy:record:list:count', '', 'A', 1, 1, 0, 4, '2024-09-27 17:38:34', '2024-09-27 17:38:34');
INSERT INTO `eb_system_menu` VALUES (1008, 952, '拼团开团记录详情', '', 'merchant:groupbuy:record:info', '', 'A', 1, 1, 0, 4, '2024-09-27 17:38:55', '2024-09-27 17:38:55');
INSERT INTO `eb_system_menu` VALUES (1009, 0, '装修', 's-operation', '', '/design', 'M', 4901, 1, 0, 4, '2024-10-29 09:43:01', '2024-10-29 09:43:01');
INSERT INTO `eb_system_menu` VALUES (1010, 1009, '店铺装修', '', '', '/design/index', 'C', 1, 1, 0, 4, '2024-10-29 09:43:35', '2024-10-29 14:29:17');
INSERT INTO `eb_system_menu` VALUES (1011, 1010, '商户装修模板分页列表', '', 'merchant:page:diy:page', '', 'A', 1, 1, 0, 4, '2024-11-04 14:16:25', '2024-11-04 14:16:25');
INSERT INTO `eb_system_menu` VALUES (1012, 1010, '设置商户装修默认首页', '', 'merchant:page:diy:set:default', '', 'A', 1, 1, 0, 4, '2024-11-04 14:16:44', '2024-11-04 14:16:44');
INSERT INTO `eb_system_menu` VALUES (1013, 1010, '获取商户装修默认首页ID', '', 'merchant:page:diy:get:default:id', '', 'A', 1, 1, 0, 4, '2024-11-04 14:16:59', '2024-11-04 14:16:59');
INSERT INTO `eb_system_menu` VALUES (1014, 1010, '新增商户装修模板', '', 'merchant:page:diy:save', '', 'A', 1, 1, 0, 4, '2024-11-04 14:17:15', '2024-11-04 14:17:15');
INSERT INTO `eb_system_menu` VALUES (1015, 1010, '删除商户装修模板', '', 'merchant:page:diy:delete', '', 'A', 1, 1, 0, 4, '2024-11-04 14:17:29', '2024-11-04 14:17:29');
INSERT INTO `eb_system_menu` VALUES (1016, 1010, '编辑商户装修模板', '', 'merchant:page:diy:update', '', 'A', 1, 1, 0, 4, '2024-11-04 14:17:44', '2024-11-04 14:17:44');
INSERT INTO `eb_system_menu` VALUES (1017, 1010, '编辑商户装修模板名称', '', 'merchant:page:diy:update:name', '', 'A', 1, 1, 0, 4, '2024-11-04 14:17:58', '2024-11-04 14:17:58');
INSERT INTO `eb_system_menu` VALUES (1018, 1010, '商户装修模板详情', '', 'merchant:page:diy:info', '', 'A', 1, 1, 0, 4, '2024-11-04 14:18:20', '2024-11-04 14:18:20');
INSERT INTO `eb_system_menu` VALUES (1019, 176, '配送员管理', '', '', '/operation/deliveryPersonnel', 'C', 7, 1, 0, 4, '2024-11-05 09:41:08', '2024-11-05 11:02:46');
INSERT INTO `eb_system_menu` VALUES (1020, 1019, '商户配送人员分页列表', '', 'merchant:delivery:personnel:page', '', 'A', 1, 1, 0, 4, '2024-11-05 11:04:26', '2024-11-05 11:04:26');
INSERT INTO `eb_system_menu` VALUES (1021, 1019, '新增商户配送人员', '', 'merchant:delivery:personnel:save', '', 'A', 1, 1, 0, 4, '2024-11-05 11:04:41', '2024-11-05 11:04:41');
INSERT INTO `eb_system_menu` VALUES (1022, 1019, '删除商户配送人员', '', 'merchant:delivery:personnel:delete', '', 'A', 1, 1, 0, 4, '2024-11-05 11:04:54', '2024-11-05 11:04:54');
INSERT INTO `eb_system_menu` VALUES (1023, 1019, '编辑商户配送人员', '', 'merchant:delivery:personnel:edit', '', 'A', 1, 1, 0, 4, '2024-11-05 11:05:06', '2024-11-05 11:05:06');
INSERT INTO `eb_system_menu` VALUES (1024, 15, '开屏广告', '', '', '/page/design/advertisement', 'C', 0, 1, 0, 3, '2024-11-12 10:01:53', '2024-11-12 10:01:53');
INSERT INTO `eb_system_menu` VALUES (1025, 1024, '获取开屏广告配置', '', 'platform:page:layout:splash:ad:get', '', 'A', 0, 1, 0, 3, '2024-11-12 10:04:35', '2024-11-12 10:04:35');
INSERT INTO `eb_system_menu` VALUES (1026, 1024, '编辑开屏广告配置', '', 'platform:page:layout:splash:ad:save', '', 'A', 0, 1, 0, 3, '2024-11-12 10:04:50', '2024-11-12 10:04:50');
INSERT INTO `eb_system_menu` VALUES (1027, 1034, '获取秒杀样式', '', 'platform:system:config:seckill:style:get', '', 'A', 0, 1, 0, 3, '2025-02-17 15:57:36', '2025-02-18 15:11:34');
INSERT INTO `eb_system_menu` VALUES (1028, 1034, '保存秒杀样式', '', 'platform:system:config:seckill:style:save', '', 'A', 0, 1, 0, 3, '2025-02-17 15:58:14', '2025-02-18 15:11:46');
INSERT INTO `eb_system_menu` VALUES (1029, 48, '用户余额记录', '', 'platform:user:balance:record', '', 'A', 0, 1, 0, 3, '2025-02-17 15:58:51', '2025-02-17 15:58:51');
INSERT INTO `eb_system_menu` VALUES (1030, 48, '用户积分记录', '', 'platform:user:integral:record', '', 'A', 0, 1, 0, 3, '2025-02-17 15:59:03', '2025-02-17 15:59:03');
INSERT INTO `eb_system_menu` VALUES (1031, 48, '用户佣金记录', '', 'platform:user:brokerage:record', '', 'A', 0, 1, 0, 3, '2025-02-17 15:59:16', '2025-02-17 15:59:16');
INSERT INTO `eb_system_menu` VALUES (1032, 48, '用户经验记录', '', 'platform:user:experience:record', '', 'A', 0, 1, 0, 3, '2025-02-17 15:59:29', '2025-02-17 15:59:29');
INSERT INTO `eb_system_menu` VALUES (1033, 48, '用户签到记录', '', 'platform:user:sign:record', '', 'A', 0, 1, 0, 3, '2025-02-17 15:59:41', '2025-02-17 15:59:41');
INSERT INTO `eb_system_menu` VALUES (1034, 598, '样式配置', '', '', '/marketing/seckill/styleConfig', 'C', 0, 1, 0, 3, '2025-02-18 15:10:31', '2025-02-18 15:11:11');
INSERT INTO `eb_system_menu` VALUES (1035, 816, '获取打印内容配置', '', 'merchant:print:get:content', '', 'A', 1, 1, 0, 4, '2025-02-19 15:49:54', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (1036, 816, '保存打印内容配置', '', 'merchant:print:save:content', '', 'A', 1, 1, 0, 4, '2025-02-19 15:50:09', '2025-05-12 14:16:15');
INSERT INTO `eb_system_menu` VALUES (1037, 176, '打印配置', '', '', '/operation/printing', 'C', 1, 1, 0, 4, '2025-02-20 18:11:20', '2025-02-21 10:19:13');
INSERT INTO `eb_system_menu` VALUES (1038, 1037, '电子面单', '', '', '/operation/printing/electrsheet', 'C', 1, 1, 0, 4, '2025-02-20 18:19:36', '2025-02-21 10:01:40');
INSERT INTO `eb_system_menu` VALUES (1039, 20, '后台管理员短信开关', '', 'platform:admin:update:receive:sms', '', 'A', 0, 1, 0, 3, '2025-02-22 15:19:38', '2025-02-22 15:19:38');
INSERT INTO `eb_system_menu` VALUES (1040, 919, '物流信息维护-月结账号配置', '', 'merchant:express:update', '', 'A', 1, 1, 0, 4, '2025-02-24 14:45:37', '2025-02-24 14:45:37');
INSERT INTO `eb_system_menu` VALUES (1041, 6, '系统管理', '', '', '/operation/system', 'M', 1, 1, 1, 3, '2025-05-14 09:59:57', '2025-05-14 10:57:28');
INSERT INTO `eb_system_menu` VALUES (1042, 1074, '字典管理', '', '', '/foundation/dict', 'M', 1, 1, 0, 3, '2025-05-14 10:02:25', '2025-06-24 17:44:50');
INSERT INTO `eb_system_menu` VALUES (1043, 0, '配置', 's-opportunity', '', '/foundation', 'M', 100, 1, 1, 3, '2025-05-20 09:10:15', '2025-05-20 09:24:51');
INSERT INTO `eb_system_menu` VALUES (1044, 1074, '小管家配置', '', '', '/foundation/housekeeperMgmt', 'C', 30, 1, 0, 3, '2025-05-20 09:15:49', '2025-06-24 17:40:30');
INSERT INTO `eb_system_menu` VALUES (1045, 1074, '基础数据维护', '', '', '/foundation/data', 'C', 20, 1, 0, 3, '2025-05-21 03:20:59', '2025-06-24 17:40:20');
INSERT INTO `eb_system_menu` VALUES (1046, 1043, '旅游攻略维护', '', '', '/business/travelGuideMgmt', 'C', 50, 1, 1, 3, '2025-05-22 07:20:24', '2025-05-22 07:20:24');
INSERT INTO `eb_system_menu` VALUES (1047, 0, '旅游', 'picture-outline-round', '', '/business', 'M', 50, 1, 1, 3, '2025-05-22 07:24:06', '2025-05-22 07:24:06');
INSERT INTO `eb_system_menu` VALUES (1048, 1047, '旅游攻略', '', '', '/business/travelGuideMgmt', 'C', 100, 1, 1, 3, '2025-05-22 07:24:45', '2025-05-22 07:24:45');
INSERT INTO `eb_system_menu` VALUES (1049, 1063, '旅游攻略', '', '', '/business/travelGuideMgmt', 'C', 100, 1, 0, 3, '2025-05-22 07:30:38', '2025-06-21 13:22:49');
INSERT INTO `eb_system_menu` VALUES (1050, 1074, '公告管理', '', '', '/business/noticeMgmt', 'C', 130, 1, 0, 3, '2025-05-23 01:58:47', '2025-06-24 17:41:03');
INSERT INTO `eb_system_menu` VALUES (1051, 1069, '资讯管理', '', '', '/business/informationMgmt', 'C', 120, 1, 0, 3, '2025-05-23 01:59:24', '2025-06-24 17:33:13');
INSERT INTO `eb_system_menu` VALUES (1052, 1072, '投诉与建议', '', '', '/business/complaintsMgmt', 'C', 130, 1, 0, 3, '2025-05-23 02:00:00', '2025-06-24 17:38:44');
INSERT INTO `eb_system_menu` VALUES (1053, 1062, '预警管理', '', '', '/business/earlyWarningMgmt', 'C', 140, 1, 0, 3, '2025-05-23 02:00:31', '2025-06-16 12:00:44');
INSERT INTO `eb_system_menu` VALUES (1054, 1070, '龙宫币配置管理', '', '', '/business/integralConfigMgmt', 'C', 160, 1, 0, 3, '2025-05-26 06:33:42', '2025-06-24 17:34:32');
INSERT INTO `eb_system_menu` VALUES (1055, 1062, '应急救援管理', '', '', '/business/emergencyRescueMgmt', 'C', 150, 1, 0, 3, '2025-05-27 07:14:24', '2025-06-16 12:00:16');
INSERT INTO `eb_system_menu` VALUES (1056, 1070, '龙宫币随机分配记录', '', '', '/business/allotRecord', 'C', 110, 1, 0, 3, '2025-05-28 09:16:06', '2025-06-24 17:34:43');
INSERT INTO `eb_system_menu` VALUES (1057, 1070, '龙宫币获取记录', '', '', '/business/receiveRecord', 'C', 12, 1, 0, 3, '2025-05-29 07:49:05', '2025-06-24 17:34:54');
INSERT INTO `eb_system_menu` VALUES (1058, 1073, '问卷调查', '', '', '/business/questionnaire', 'C', 100, 1, 0, 3, '2025-05-30 07:45:45', '2025-06-24 17:38:55');
INSERT INTO `eb_system_menu` VALUES (1059, 0, '微信小程序', 'sell', '', '/wx', 'M', 200, 0, 0, 3, '2025-06-03 08:48:44', '2025-06-03 08:48:44');
INSERT INTO `eb_system_menu` VALUES (1060, 1059, '投诉建议回复', '', 'wx:reply', '', 'A', 10, 1, 0, 3, '2025-06-03 08:50:44', '2025-06-03 09:00:35');
INSERT INTO `eb_system_menu` VALUES (1061, 1070, '景区热点配置', '', '', '/business/hotspot', 'C', 170, 1, 0, 3, '2025-06-06 02:42:01', '2025-06-24 17:34:20');
INSERT INTO `eb_system_menu` VALUES (1062, 0, '应急管理', 's-promotion', '', '/meet', 'M', 240, 1, 0, 3, '2025-06-16 11:54:30', '2025-06-24 17:43:44');
INSERT INTO `eb_system_menu` VALUES (1063, 0, '攻略管理', 'help', '', '/guideConfig', 'M', 250, 1, 0, 3, '2025-06-16 11:55:10', '2025-06-24 17:41:48');
INSERT INTO `eb_system_menu` VALUES (1064, 0, 'vr配置', 'video-camera-solid', '', '/vrConfig', 'M', 140, 1, 1, 3, '2025-06-16 11:56:10', '2025-06-16 12:13:07');
INSERT INTO `eb_system_menu` VALUES (1065, 0, '我的配置', 'user-solid', '', '/myConfig', 'M', 130, 1, 1, 3, '2025-06-16 11:56:29', '2025-06-16 12:13:28');
INSERT INTO `eb_system_menu` VALUES (1066, 1049, '编辑', '', 'platform:admin:update', '', 'A', 0, 1, 0, 3, '2025-06-16 16:14:54', '2025-06-16 16:14:54');
INSERT INTO `eb_system_menu` VALUES (1067, 1070, '龙宫币核销记录', '', '', '/business/verificationRecord', 'C', 10, 1, 0, 3, '2025-06-20 14:24:43', '2025-06-24 17:35:04');
INSERT INTO `eb_system_menu` VALUES (1068, 1063, '二级目录', '', '', '/top', 'M', 0, 1, 1, 3, '2025-06-21 13:20:26', '2025-06-21 13:21:19');
INSERT INTO `eb_system_menu` VALUES (1069, 0, '资讯管理', 'star-off', '', '/information', 'M', 300, 1, 0, 3, '2025-06-24 17:24:53', '2025-06-24 17:25:17');
INSERT INTO `eb_system_menu` VALUES (1070, 0, '热点管理', 's-help', '', '/hotPoint', 'M', 290, 1, 0, 3, '2025-06-24 17:27:01', '2025-06-24 17:27:01');
INSERT INTO `eb_system_menu` VALUES (1071, 0, '人员管理', 'user-solid', '', '/person', 'M', 280, 1, 1, 3, '2025-06-24 17:27:51', '2025-06-24 17:27:51');
INSERT INTO `eb_system_menu` VALUES (1072, 0, '投诉管理', 's-promotion', '', '/complain', 'M', 270, 1, 0, 3, '2025-06-24 17:29:09', '2025-06-24 17:29:09');
INSERT INTO `eb_system_menu` VALUES (1073, 0, '问卷调查', 's-marketing', '', '/questionnaire', 'M', 260, 1, 0, 3, '2025-06-24 17:30:34', '2025-06-24 17:30:34');
INSERT INTO `eb_system_menu` VALUES (1074, 0, '系统管理', 'setting', '', '/systemMegt', 'M', 210, 1, 0, 3, '2025-06-24 17:31:10', '2025-06-24 17:41:36');
INSERT INTO `eb_system_menu` VALUES (1075, 0, '应急管理', 'message-solid', '', '/meet', 'M', 240, 1, 1, 3, '2025-06-24 17:43:02', '2025-06-24 17:43:02');
INSERT INTO `eb_system_menu` VALUES (1076, 2, '酒店', 'potato-strips', '', '', 'M', 0, 1, 1, 3, '2025-07-18 04:08:25', '2025-07-18 04:08:25');
INSERT INTO `eb_system_menu` VALUES (1077, 0, '酒店管理', 'hotel', '', '/hotel', 'M', 5000, 1, 0, 4, '2025-07-18 04:48:34', '2025-07-18 04:48:34');
INSERT INTO `eb_system_menu` VALUES (1078, 1077, '房型管理', 'bed', '', '/hotel/room', 'C', 10, 1, 0, 4, '2025-07-18 04:48:34', '2025-07-18 04:48:34');
INSERT INTO `eb_system_menu` VALUES (1079, 1077, '价格策略', 'money', '', '/hotel/price', 'C', 20, 1, 0, 4, '2025-07-18 04:48:34', '2025-07-18 04:48:34');
INSERT INTO `eb_system_menu` VALUES (1080, 1077, '取消规则', 'document-remove', '', '/hotel/cancel', 'C', 30, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1081, 1078, '房型列表', '', 'merchant:hotel:room:list', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1082, 1078, '新增房型', '', 'merchant:hotel:room:save', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1083, 1078, '修改房型', '', 'merchant:hotel:room:update', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1084, 1078, '删除房型', '', 'merchant:hotel:room:delete', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1085, 1078, '房型详情', '', 'merchant:hotel:room:info', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1086, 1078, '修改房型状态', '', 'merchant:hotel:room:status', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1087, 1078, '修改房型库存', '', 'merchant:hotel:room:stock', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1088, 1079, '价格策略列表', '', 'merchant:hotel:price:list', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1089, 1079, '新增价格策略', '', 'merchant:hotel:price:save', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1090, 1079, '修改价格策略', '', 'merchant:hotel:price:update', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1091, 1079, '删除价格策略', '', 'merchant:hotel:price:delete', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1092, 1079, '价格策略详情', '', 'merchant:hotel:price:info', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1093, 1079, '修改价格策略状态', '', 'merchant:hotel:price:status', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:35', '2025-07-18 04:48:35');
INSERT INTO `eb_system_menu` VALUES (1094, 1079, '价格日历', '', 'merchant:hotel:price:calendar', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1095, 1080, '取消规则列表', '', 'merchant:hotel:cancel:list', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1096, 1080, '新增取消规则', '', 'merchant:hotel:cancel:save', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1097, 1080, '修改取消规则', '', 'merchant:hotel:cancel:update', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1098, 1080, '删除取消规则', '', 'merchant:hotel:cancel:delete', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1099, 1080, '取消规则详情', '', 'merchant:hotel:cancel:info', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');
INSERT INTO `eb_system_menu` VALUES (1100, 1080, '修改取消规则状态', '', 'merchant:hotel:cancel:status', '', 'A', 1, 1, 0, 4, '2025-07-18 04:48:36', '2025-07-18 04:48:36');

SET FOREIGN_KEY_CHECKS = 1;
