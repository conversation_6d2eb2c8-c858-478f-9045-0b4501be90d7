/*
 Navicat Premium Data Transfer

 Source Server         : wzblcy.cn
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : wzblcy.cn:10179
 Source Schema         : java_mer_trip

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 18/07/2025 13:28:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for eb_system_admin
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_admin`;
CREATE TABLE `eb_system_admin`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员账号',
  `pwd` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员密码',
  `real_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员姓名',
  `header_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员头像',
  `roles` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员角色(menus_id)',
  `last_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理员最后一次登录ip',
  `login_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '登录次数',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '管理员级别',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '管理员状态 1有效，0无效',
  `is_del` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除：1-删除',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `is_sms` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '是否接收短信',
  `type` int(11) NOT NULL DEFAULT 1 COMMENT '管理员类型：1= 平台超管, 2=商户超管, 3=系统管理员，4=商户管理员',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id，0-平台',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '后台管理员添加时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '后台管理员最后一次登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `account`(`account`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of eb_system_admin
-- ----------------------------
INSERT INTO `eb_system_admin` VALUES (1, 'admin', 'L8qdg72wbeQ=', '超级管理员', '', '1', '127.0.0.1', 896, 0, 1, 0, '0', 0, 1, 0, '2022-07-22 15:18:49', '2025-07-17 15:24:40');
INSERT INTO `eb_system_admin` VALUES (2, 'demo', '7iIl3H5zCinwrYbrxAR7cQ==', '体验者', '', '4', '127.0.0.1', 7951, 1, 1, 0, '***********', 0, 3, 0, '2022-11-15 14:48:08', '2025-07-18 12:49:29');
INSERT INTO `eb_system_admin` VALUES (5, '***********', 'w9LTrURn7xU=', '大粽子的杂货店', '', '2', '127.0.0.1', 391, 1, 1, 0, '***********', 0, 2, 3, '2022-10-12 18:08:37', '2025-07-18 13:06:12');
INSERT INTO `eb_system_admin` VALUES (9, 'test', 'qG2HZBgj7J4=', '哈哈', '', '11', NULL, 0, 1, 0, 0, '***********', 0, 3, 0, '2025-05-30 08:33:55', '2025-06-18 16:12:17');
INSERT INTO `eb_system_admin` VALUES (15, 'zlj001', 'uT/iq/xDpjc=', '左', '', '4', NULL, 0, 1, 0, 0, '18213073387', 0, 3, 0, '2025-06-03 04:03:49', '2025-06-03 05:51:20');
INSERT INTO `eb_system_admin` VALUES (16, '18088328989', 'LRo91YL7M5MyjnFSIiQ67w==', '李迎春', '', '4', '106.61.168.16', 18, 1, 1, 0, '18088328989', 0, 3, 0, '2025-06-16 11:45:02', '2025-06-21 14:51:57');
INSERT INTO `eb_system_admin` VALUES (17, '15331762148', 'OP0+0raMDHk=', '测试', '', '4', NULL, 0, 1, 1, 1, '15331762148', 0, 3, 0, '2025-06-23 14:07:22', '2025-06-23 14:10:26');
INSERT INTO `eb_system_admin` VALUES (18, '15331762148', 'OP0+0raMDHk=', 'AH', '', '4', '116.54.59.105', 3, 1, 1, 0, '15331762148', 0, 3, 0, '2025-06-23 14:11:02', '2025-06-24 17:46:40');
INSERT INTO `eb_system_admin` VALUES (19, '18287971758', 'oPw8czBIZ/w=', 'tm', '', '4', '112.115.247.113', 6, 1, 1, 0, '18287971758', 0, 3, 0, '2025-07-04 14:44:04', '2025-07-10 12:54:31');
INSERT INTO `eb_system_admin` VALUES (20, '13149109621', 'g+3XJlSA7Ww=', '金梭酒店', '', '2', '127.0.0.1', 2, 1, 1, 0, '13149109621', 0, 2, 4, '2025-07-17 07:26:16', '2025-07-17 15:35:37');

SET FOREIGN_KEY_CHECKS = 1;
