/*
 金梭酒店项目示例数据插入脚本

 用于测试和演示酒店功能
 包含完整的房型、价格策略、取消规则示例数据

 创建时间: 2025-01-17
 版本: v1.0
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 插入酒店房间基础信息示例数据
-- ----------------------------

-- 金梭大酒店（假设商户ID=1）的房型数据
INSERT INTO `eb_hotel_room` (`mer_id`, `room_name`, `room_type`, `room_facilities`, `room_area`, `room_floor`, `bed_type`, `max_guests`, `base_price`, `total_rooms`, `room_images`, `room_description`, `max_booking_days`, `status`, `sort`, `is_del`, `create_id`, `create_time`) VALUES
(1, '豪华大床房', '大床房', '["免费WiFi", "空调", "电视", "独立卫浴", "吹风机", "拖鞋", "洗漱用品"]', 35.00, '3-8楼', '大床(1.8m)', 2, 100.00, 10, '["room1_1.jpg", "room1_2.jpg", "room1_3.jpg"]', '宽敞舒适的豪华大床房，配备现代化设施，为您提供舒适的住宿体验。', 30, 1, 1, 0, 1, NOW()),

(1, '标准双人间', '双人间', '["免费WiFi", "空调", "电视", "独立卫浴", "吹风机", "拖鞋", "洗漱用品"]', 28.00, '2-6楼', '双人床(1.2m*2)', 2, 80.00, 15, '["room2_1.jpg", "room2_2.jpg", "room2_3.jpg"]', '经济实惠的标准双人间，设施齐全，性价比高。', 30, 1, 2, 0, 1, NOW()),

(1, '商务套房', '套房', '["免费WiFi", "空调", "电视", "独立卫浴", "吹风机", "拖鞋", "洗漱用品", "办公桌", "沙发", "冰箱", "保险箱"]', 50.00, '8-10楼', '大床(2.0m)', 3, 200.00, 5, '["room3_1.jpg", "room3_2.jpg", "room3_3.jpg"]', '高端商务套房，配备办公区域和会客区，适合商务人士入住。', 30, 1, 3, 0, 1, NOW()),

(1, '家庭房', '家庭房', '["免费WiFi", "空调", "电视", "独立卫浴", "吹风机", "拖鞋", "洗漱用品", "儿童床", "儿童洗漱用品"]', 45.00, '2-5楼', '大床+儿童床', 4, 150.00, 8, '["room4_1.jpg", "room4_2.jpg", "room4_3.jpg"]', '温馨的家庭房，配备儿童设施，适合家庭出行。', 30, 1, 4, 0, 1, NOW());

-- ----------------------------
-- 2. 插入房型价格策略示例数据
-- ----------------------------

-- 豪华大床房价格策略（房间ID=1）
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(1, 1, '基础价格(工作日)', 1, '1,2,3,4,5', 100.00, 1, 1, 0, 1, NOW()),
(1, 1, '周末价格', 2, '6,7', 150.00, 2, 1, 0, 1, NOW()),
(1, 1, '节假日价格', 3, '', 300.00, 3, 1, 0, 1, NOW());

-- 标准双人间价格策略（房间ID=2）
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(1, 2, '基础价格(工作日)', 1, '1,2,3,4,5', 80.00, 1, 1, 0, 1, NOW()),
(1, 2, '周末价格', 2, '6,7', 120.00, 2, 1, 0, 1, NOW()),
(1, 2, '节假日价格', 3, '', 250.00, 3, 1, 0, 1, NOW());

-- 商务套房价格策略（房间ID=3）
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(1, 3, '基础价格(工作日)', 1, '1,2,3,4,5', 200.00, 1, 1, 0, 1, NOW()),
(1, 3, '周末价格', 2, '6,7', 280.00, 2, 1, 0, 1, NOW()),
(1, 3, '节假日价格', 3, '', 500.00, 3, 1, 0, 1, NOW());

-- 家庭房价格策略（房间ID=4）
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(1, 4, '基础价格(工作日)', 1, '1,2,3,4,5', 150.00, 1, 1, 0, 1, NOW()),
(1, 4, '周末价格', 2, '6,7', 200.00, 2, 1, 0, 1, NOW()),
(1, 4, '节假日价格', 3, '', 400.00, 3, 1, 0, 1, NOW());

-- ----------------------------
-- 3. 插入取消规则示例数据
-- ----------------------------

-- 金梭大酒店取消规则（商户ID=1）
-- 按照用户需求：48小时以上免费，24-48小时扣5%，12-24小时扣10%，2小时内不可取消，其他时间(2-12小时)全额退款
INSERT INTO `eb_hotel_cancel_rule` (`mer_id`, `rule_name`, `advance_hours`, `penalty_type`, `penalty_value`, `min_advance_hours`, `description`, `sort`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(1, '48小时前取消', 48, 1, 0.00, 2, '入住前48小时以上取消，全额退款', 1, 1, 0, 1, NOW()),
(1, '24-48小时取消', 24, 1, 5.00, 2, '入住前24-48小时取消，扣除5%手续费', 2, 1, 0, 1, NOW()),
(1, '12-24小时取消', 12, 1, 10.00, 2, '入住前12-24小时取消，扣除10%手续费', 3, 1, 0, 1, NOW()),
(1, '2小时内不可取消', 0, 1, 100.00, 0, '入住前2小时内不可取消订单', 4, 1, 0, 1, NOW());

-- ----------------------------
-- 4. 插入其他酒店示例数据（可选）
-- ----------------------------

-- 假设商户ID=2为另一家酒店
INSERT INTO `eb_hotel_room` (`mer_id`, `room_name`, `room_type`, `room_facilities`, `room_area`, `room_floor`, `bed_type`, `max_guests`, `base_price`, `total_rooms`, `room_images`, `room_description`, `max_booking_days`, `status`, `sort`, `is_del`, `create_id`, `create_time`) VALUES
(2, '精品大床房', '大床房', '["免费WiFi", "空调", "电视", "独立卫浴", "智能马桶", "投影仪"]', 40.00, '5-12楼', '大床(2.0m)', 2, 120.00, 12, '["room5_1.jpg", "room5_2.jpg"]', '现代化精品大床房，配备智能设施。', 60, 1, 1, 0, 1, NOW());

-- 对应的价格策略
INSERT INTO `eb_hotel_room_price_strategy` (`mer_id`, `room_id`, `strategy_name`, `strategy_type`, `week_days`, `price_value`, `priority`, `status`, `is_del`, `create_id`, `create_time`) VALUES
(2, 5, '基础价格(工作日)', 1, '1,2,3,4,5', 120.00, 1, 1, 0, 1, NOW()),
(2, 5, '周末价格', 2, '6,7', 180.00, 2, 1, 0, 1, NOW()),
(2, 5, '节假日价格', 3, '', 350.00, 3, 1, 0, 1, NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 数据说明
-- ----------------------------
/*
示例数据包含：

1. 金梭大酒店（商户ID=1）：
   - 4种房型：豪华大床房、标准双人间、商务套房、家庭房
   - 每种房型都有完整的价格策略：工作日、周末、节假日
   - 统一的取消规则：48小时前免费取消，之后按时间段扣费

2. 价格策略说明：
   - strategy_type: 1=基础价格(工作日), 2=周末价格, 3=节假日价格
   - priority: 优先级，数字越大优先级越高
   - week_days: 适用的星期几（1=周一, 7=周日）

3. 取消规则说明：
   - penalty_type: 1=按比例扣费, 2=固定金额扣费
   - penalty_value: 扣费值（比例时为百分比，如5.00表示5%）
   - advance_hours: 提前取消的小时数
   - min_advance_hours: 最小提前取消时间

4. 房间设施JSON格式：
   - 使用JSON数组存储房间设施列表
   - 便于前端展示和后续扩展

5. 房间图片JSON格式：
   - 使用JSON数组存储图片文件名
   - 实际使用时需要配合文件上传功能
*/
