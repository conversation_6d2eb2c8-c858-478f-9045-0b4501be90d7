/*
 调试商户商品显示问题的SQL查询脚本
 
 用于排查为什么商户端看不到商品数据
 
 创建时间: 2025-01-17
*/

-- 1. 检查商户信息
SELECT id, name, phone, status, is_del 
FROM eb_merchant 
WHERE id = 1;

-- 2. 检查商户分类信息
SELECT id, mer_id, name, is_show, is_del 
FROM eb_merchant_product_category 
WHERE mer_id = 1 
ORDER BY id;

-- 3. 检查商品和分类的关联关系
SELECT 
    p.id,
    p.name as product_name,
    p.mer_id,
    p.cate_id,
    p.category_id,
    p.is_show,
    p.is_del,
    mpc.name as merchant_category_name,
    mpc.is_show as category_is_show,
    mpc.is_del as category_is_del
FROM eb_product p
LEFT JOIN eb_merchant_product_category mpc ON p.cate_id = mpc.id
WHERE p.mer_id = 1 AND p.name LIKE '%金梭大酒店%'
LIMIT 5;

-- 4. 检查是否有孤立的商品（分类不存在）
SELECT 
    p.id,
    p.name,
    p.cate_id,
    'Category Not Found' as issue
FROM eb_product p
LEFT JOIN eb_merchant_product_category mpc ON p.cate_id = mpc.id
WHERE p.mer_id = 1 
  AND p.name LIKE '%金梭大酒店%'
  AND mpc.id IS NULL;

-- 5. 检查分类ID=59的详细信息
SELECT * FROM eb_merchant_product_category WHERE id = 59;

-- 6. 检查商户ID=1的所有分类
SELECT 
    id,
    name,
    pid,
    is_show,
    is_del,
    create_time
FROM eb_merchant_product_category 
WHERE mer_id = 1 
ORDER BY id;

-- 7. 检查商户ID=1的所有商品数量
SELECT 
    COUNT(*) as total_products,
    SUM(CASE WHEN is_show = 1 AND is_del = 0 THEN 1 ELSE 0 END) as visible_products,
    SUM(CASE WHEN name LIKE '%金梭大酒店%' THEN 1 ELSE 0 END) as hotel_products
FROM eb_product 
WHERE mer_id = 1;

-- 8. 检查可能的权限或状态问题
SELECT 
    p.id,
    p.name,
    p.is_show,
    p.is_del,
    p.status,
    p.audit_status,
    mpc.is_show as category_show,
    mpc.is_del as category_del
FROM eb_product p
JOIN eb_merchant_product_category mpc ON p.cate_id = mpc.id
WHERE p.mer_id = 1 AND p.name LIKE '%金梭大酒店%'
  AND (p.is_show = 0 OR p.is_del = 1 OR mpc.is_show = 0 OR mpc.is_del = 1)
LIMIT 5;
