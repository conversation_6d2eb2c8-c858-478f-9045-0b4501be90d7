/*
 金梭酒店项目数据库表结构验证和测试脚本
 
 用于验证表结构设计的正确性和完整性
 包含各种查询测试和数据完整性检查
 
 创建时间: 2025-01-17
 版本: v1.0
*/

-- ----------------------------
-- 1. 表结构验证查询
-- ----------------------------

-- 查看酒店房间表结构
DESCRIBE eb_hotel_room;

-- 查看价格策略表结构
DESCRIBE eb_hotel_room_price_strategy;

-- 查看取消规则表结构
DESCRIBE eb_hotel_cancel_rule;

-- ----------------------------
-- 2. 索引验证查询
-- ----------------------------

-- 查看酒店房间表索引
SHOW INDEX FROM eb_hotel_room;

-- 查看价格策略表索引
SHOW INDEX FROM eb_hotel_room_price_strategy;

-- 查看取消规则表索引
SHOW INDEX FROM eb_hotel_cancel_rule;

-- ----------------------------
-- 3. 数据完整性测试查询
-- ----------------------------

-- 测试1: 查询指定商户的所有房型
SELECT 
    hr.id,
    hr.room_name,
    hr.room_type,
    hr.max_guests,
    hr.base_price,
    hr.total_rooms,
    hr.status
FROM eb_hotel_room hr 
WHERE hr.mer_id = 1 AND hr.is_del = 0 
ORDER BY hr.sort;

-- 测试2: 查询房型的价格策略（按优先级排序）
SELECT 
    ps.id,
    ps.strategy_name,
    ps.strategy_type,
    ps.price_value,
    ps.priority,
    ps.week_days
FROM eb_hotel_room_price_strategy ps 
WHERE ps.mer_id = 1 AND ps.room_id = 1 AND ps.is_del = 0 
ORDER BY ps.priority DESC;

-- 测试3: 查询酒店的取消规则（按提前时间排序）
SELECT 
    cr.id,
    cr.rule_name,
    cr.advance_hours,
    cr.penalty_type,
    cr.penalty_value,
    cr.description
FROM eb_hotel_cancel_rule cr 
WHERE cr.mer_id = 1 AND cr.is_del = 0 
ORDER BY cr.advance_hours DESC;

-- 测试4: 联合查询 - 房型及其价格策略
SELECT 
    hr.room_name,
    hr.room_type,
    hr.base_price,
    ps.strategy_name,
    ps.price_value,
    ps.priority
FROM eb_hotel_room hr
LEFT JOIN eb_hotel_room_price_strategy ps ON hr.id = ps.room_id
WHERE hr.mer_id = 1 AND hr.is_del = 0 AND ps.is_del = 0
ORDER BY hr.sort, ps.priority DESC;

-- ----------------------------
-- 4. 业务逻辑测试查询
-- ----------------------------

-- 测试5: 模拟价格计算 - 获取指定房型在工作日的价格
SELECT 
    hr.room_name,
    ps.strategy_name,
    ps.price_value as calculated_price
FROM eb_hotel_room hr
JOIN eb_hotel_room_price_strategy ps ON hr.id = ps.room_id
WHERE hr.mer_id = 1 
  AND hr.id = 1 
  AND ps.strategy_type = 1  -- 基础价格(工作日)
  AND hr.is_del = 0 
  AND ps.is_del = 0;

-- 测试6: 模拟价格计算 - 获取指定房型在周末的价格
SELECT 
    hr.room_name,
    ps.strategy_name,
    ps.price_value as calculated_price
FROM eb_hotel_room hr
JOIN eb_hotel_room_price_strategy ps ON hr.id = ps.room_id
WHERE hr.mer_id = 1 
  AND hr.id = 1 
  AND ps.strategy_type = 2  -- 周末价格
  AND hr.is_del = 0 
  AND ps.is_del = 0;

-- 测试7: 模拟价格计算 - 获取指定房型在节假日的价格
SELECT 
    hr.room_name,
    ps.strategy_name,
    ps.price_value as calculated_price
FROM eb_hotel_room hr
JOIN eb_hotel_room_price_strategy ps ON hr.id = ps.room_id
WHERE hr.mer_id = 1 
  AND hr.id = 1 
  AND ps.strategy_type = 3  -- 节假日价格
  AND hr.is_del = 0 
  AND ps.is_del = 0;

-- 测试8: 模拟取消费用计算 - 获取24小时内取消的规则
SELECT 
    cr.rule_name,
    cr.advance_hours,
    cr.penalty_type,
    cr.penalty_value,
    CASE 
        WHEN cr.penalty_type = 1 THEN CONCAT(cr.penalty_value, '%')
        ELSE CONCAT(cr.penalty_value, '元')
    END as penalty_description
FROM eb_hotel_cancel_rule cr
WHERE cr.mer_id = 1 
  AND cr.advance_hours <= 24 
  AND cr.advance_hours > 0
  AND cr.is_del = 0
ORDER BY cr.advance_hours DESC
LIMIT 1;

-- ----------------------------
-- 5. 性能测试查询
-- ----------------------------

-- 测试9: 复杂联合查询 - 获取酒店完整信息
SELECT 
    hr.id as room_id,
    hr.room_name,
    hr.room_type,
    hr.max_guests,
    hr.total_rooms,
    GROUP_CONCAT(
        CONCAT(ps.strategy_name, ':', ps.price_value) 
        ORDER BY ps.priority DESC 
        SEPARATOR '; '
    ) as price_strategies,
    COUNT(cr.id) as cancel_rules_count
FROM eb_hotel_room hr
LEFT JOIN eb_hotel_room_price_strategy ps ON hr.id = ps.room_id AND ps.is_del = 0
LEFT JOIN eb_hotel_cancel_rule cr ON hr.mer_id = cr.mer_id AND cr.is_del = 0
WHERE hr.mer_id = 1 AND hr.is_del = 0
GROUP BY hr.id, hr.room_name, hr.room_type, hr.max_guests, hr.total_rooms
ORDER BY hr.sort;

-- 测试10: 数据统计查询
SELECT 
    '房型数量' as metric,
    COUNT(*) as value
FROM eb_hotel_room 
WHERE mer_id = 1 AND is_del = 0

UNION ALL

SELECT 
    '价格策略数量' as metric,
    COUNT(*) as value
FROM eb_hotel_room_price_strategy 
WHERE mer_id = 1 AND is_del = 0

UNION ALL

SELECT 
    '取消规则数量' as metric,
    COUNT(*) as value
FROM eb_hotel_cancel_rule 
WHERE mer_id = 1 AND is_del = 0;

-- ----------------------------
-- 6. 数据完整性约束测试
-- ----------------------------

-- 测试11: 检查是否存在无效的外键关联
SELECT 
    ps.id,
    ps.room_id,
    ps.strategy_name
FROM eb_hotel_room_price_strategy ps
LEFT JOIN eb_hotel_room hr ON ps.room_id = hr.id
WHERE hr.id IS NULL AND ps.is_del = 0;

-- 测试12: 检查价格策略的优先级设置是否合理
SELECT 
    room_id,
    strategy_type,
    priority,
    COUNT(*) as count
FROM eb_hotel_room_price_strategy 
WHERE is_del = 0
GROUP BY room_id, strategy_type, priority
HAVING COUNT(*) > 1;

-- 测试13: 检查取消规则的时间设置是否合理
SELECT 
    mer_id,
    advance_hours,
    COUNT(*) as count
FROM eb_hotel_cancel_rule 
WHERE is_del = 0
GROUP BY mer_id, advance_hours
HAVING COUNT(*) > 1;

-- ----------------------------
-- 测试结果说明
-- ----------------------------
/*
测试脚本说明：

1. 表结构验证：
   - 验证所有表和字段是否正确创建
   - 检查索引是否正确建立

2. 数据完整性测试：
   - 验证基础的CRUD操作
   - 测试表间关联查询

3. 业务逻辑测试：
   - 模拟价格计算逻辑
   - 模拟取消费用计算逻辑

4. 性能测试：
   - 测试复杂查询的执行效率
   - 验证索引的有效性

5. 数据约束测试：
   - 检查外键关联的完整性
   - 验证业务规则的一致性

预期结果：
- 所有查询都应该正常执行
- 不应该有数据完整性错误
- 复杂查询应该有合理的执行时间
- 业务逻辑查询应该返回正确的结果
*/
