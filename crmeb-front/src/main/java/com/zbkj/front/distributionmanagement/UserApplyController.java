package com.zbkj.front.distributionmanagement;

import com.zbkj.common.model.user.User;
import com.zbkj.common.request.distributionmanagement.UserApplyRequest;
import com.zbkj.common.response.distributionmanagement.UserApplyResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.UserBrokerageRecordService;
import com.zbkj.service.service.UserService;
import com.zbkj.service.service.distributionmanagement.UserApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 用户申请推广员控制器，用户申请推广员表
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@RestController
@RequestMapping("api/front/retail/store/apply")
@Api(tags = "推广员申请")
public class UserApplyController {

    @Autowired
    private UserApplyService userApplyService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;

    /**
     * 申请成为推广员
     * @param request 申请参数
     * @return 申请结果
     */
    @ApiOperation(value = "申请成为推广员")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    public CommonResult<Boolean> apply(@RequestBody @Validated UserApplyRequest request) {
        return CommonResult.success(userApplyService.apply(request));
    }

    /**
     * 查询申请状态
     * @return 申请状态
     */
    @ApiOperation(value = "查询申请状态")
    @RequestMapping(value = "/status", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getApplyStatus() {
        return CommonResult.success(userApplyService.getApplyStatus());
    }
    
    /**
     * 我的推广
     * 根据用户是否是分销员返回不同数据
     * 如果是分销员(isPromoter=true)，返回分销所需数据
     * 如果不是分销员(isPromoter=false)，查询是否已申请
     *   - 已申请：返回申请状态
     *   - 未申请：返回isPromoter=false
     * @return 我的推广数据
     */
    @ApiOperation(value = "我的推广")
    @RequestMapping(value = "/my/promotion", method = RequestMethod.GET)
    public CommonResult<UserApplyResponse> getMyPromotion() {
        // 获取当前用户
        User user = userService.getInfo();
        UserApplyResponse response = new UserApplyResponse();
        
        // 获取佣金相关数据（无论是否是分销员都需要）
        BigDecimal freezePrice = userBrokerageRecordService.getFreezePrice(user.getId());
        BigDecimal settledCommissionPrice = userBrokerageRecordService.getSettledCommission(user.getId());
        
        response.setFreezePrice(freezePrice);
        response.setSettledCommissionPrice(settledCommissionPrice);
        response.setBrokeragePrice(user.getBrokeragePrice());
        
        // 如果是分销员
        if (user.getIsPromoter()) {
            response.setIsPromoter(true);
            response.setStatus(1); // 已是分销员
            return CommonResult.success(response);
        }
        
        // 如果不是分销员，查询申请状态
        response.setIsPromoter(false);
        Map<String, Object> applyStatusMap = userApplyService.getApplyStatus();
        response.setStatus((Integer) applyStatusMap.get("status"));
        response.setApplyInfo((com.zbkj.common.model.distributionmanagement.UserApply) applyStatusMap.get("applyInfo"));
        
        return CommonResult.success(response);
    }
} 