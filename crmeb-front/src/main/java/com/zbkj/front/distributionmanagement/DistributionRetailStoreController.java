package com.zbkj.front.distributionmanagement;

import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.distributionmanagement.DistributionRetailStoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 分销推广控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/distributionmanagement/retail/store")
@Api(tags = "分销推广管理")
public class DistributionRetailStoreController {
    
    @Autowired
    private DistributionRetailStoreService distributionRetailStoreService;
    
    /**
     * 生成商品推广链接
     */
    @ApiOperation(value = "生成商品推广链接")
    @RequestMapping(value = "/generate/product/link/{productId}", method = RequestMethod.GET)
    @ApiImplicitParam(name = "productId", value = "商品ID", required = true, dataType = "Integer")
    public CommonResult<String> generateProductSpreadLink(@PathVariable Integer productId) {
        return CommonResult.success(distributionRetailStoreService.generateProductSpreadLink(productId));
    }
}
