package com.zbkj.front.controller;

import com.zbkj.common.request.BaseUploadRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.common.utils.MinioUtil;
import com.zbkj.common.utils.SsrfFileTypeFilter;
import com.zbkj.common.vo.FileResultVo;
import com.zbkj.service.service.UploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.AntPathMatcher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;


/**
 * 上传文件 前端控制器 -- 用户端
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@RestController
@RequestMapping("api/front/upload")
@Api(tags = "用户上传文件")
public class UserUploadController {
    @Value(value = "${minio.bucketName}")
    private String bucketName;

    @Autowired
    private UploadService uploadService;

    @ApiOperation(value = "图片上传")
    @RequestMapping(value = "/image", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "model", value = "模块 用户user,商品product,微信wechat,news文章"),
            @ApiImplicitParam(name = "pid", value = "分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列 ", allowableValues = "range[0,1,2,3,4,5,6,7,8]")
    })
    public CommonResult<FileResultVo> image(MultipartFile multipart, @RequestParam(value = "model") String model,
                                            @RequestParam(value = "pid") Integer pid) {

        return CommonResult.success(uploadService.imageUpload(multipart, model, pid, null));
    }

    @ApiOperation(value = "文件上传")
    @RequestMapping(value = "/file", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "model", value = "模块 用户user,商品product,微信wechat,news文章"),
            @ApiImplicitParam(name = "pid", value = "分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列 ", allowableValues = "range[0,1,2,3,4,5,6,7,8]")
    })
    public CommonResult<FileResultVo> file(MultipartFile multipart, @RequestParam(value = "model") String model,
                                           @RequestParam(value = "pid") Integer pid) {
        return CommonResult.success(uploadService.fileUpload(multipart, model, pid, null));
    }

    @ApiOperation(value = "base64图片上传")
    @RequestMapping(value = "/base64", method = RequestMethod.POST)
    public CommonResult<FileResultVo> base64Upload(@RequestBody @Validated BaseUploadRequest request) {
        return CommonResult.success(uploadService.base64Upload(request.getBase64Url(), request.getModel(), request.getPid()));
    }

    /**
     * 预览图片&下载文件
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping(value = "/static/**")
    public void view(HttpServletRequest request, HttpServletResponse response) {
        String filePath = extractPathFromPattern(request);
        if (StringUtils.isEmpty(filePath)) {
            return;
        }
        int index = filePath.lastIndexOf("/");
        String fileName = "";
        if (index != -1) {
            fileName = filePath.substring(index + 1);
        } else {
            fileName = filePath.substring(0, filePath.length() - 1);
        }
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            filePath = filePath.replace("..", "").replace("../", "");
            if (filePath.endsWith(",")) {
                filePath = filePath.substring(0, filePath.length() - 1);
            }
            SsrfFileTypeFilter.checkDownloadFileType(filePath);
            inputStream = MinioUtil.getMinioFile(bucketName, filePath);
            if (inputStream == null) {
                response.setStatus(404);
                log.error("文件[" + filePath + "]不存在..");
                return;
            }
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);
            outputStream = response.getOutputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            log.error("预览文件失败" + e.getMessage());
            response.setStatus(404);
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }


    }

    /**
     * 把指定URL后的字符串全部截断当成参数
     * 这么做是为了防止URL中包含中文或者特殊字符（/等）时，匹配不了的问题
     *
     * @param request
     * @return
     */
    private static String extractPathFromPattern(final HttpServletRequest request) {
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        return new AntPathMatcher().extractPathWithinPattern(bestMatchPattern, path);
    }
}



