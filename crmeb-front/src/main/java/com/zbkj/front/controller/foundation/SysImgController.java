package com.zbkj.front.controller.foundation;

import com.zbkj.common.result.CommonResult;
import com.zbkj.front.service.SysImgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 图片 控制器
 * </p>
 *
 * <AUTHOR>
 * @Date 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("api/front/foundation/sys/img")
@Api(tags = "foundation:图片管理")
public class SysImgController {

    @Autowired
    private SysImgService sysImgService;

    @ApiOperation(value = "初始化小程序获取图片")
    @RequestMapping(value = "/getInfo", method = RequestMethod.GET)
    public CommonResult<Map<String, String>> getDictItems() {
        return CommonResult.success(sysImgService.getImgUrl());
    }

}
