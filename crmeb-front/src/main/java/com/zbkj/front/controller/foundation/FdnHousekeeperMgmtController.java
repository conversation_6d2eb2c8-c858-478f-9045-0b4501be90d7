package com.zbkj.front.controller.foundation;

import com.zbkj.common.model.foundation.FdnHousekeeperMgmt;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.foundation.FdnHousekeeperMgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小管家管理 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/foundation/housekeeper/mgmt")
@Api(tags = "foundation:小管家管理") //配合swagger使用
public class FdnHousekeeperMgmtController {

    @Autowired
    private FdnHousekeeperMgmtService fdnHousekeeperMgmtService;

    /**
     * 查询小管家管理信息
     *
     * <AUTHOR>
     * @since 2025-05-20
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<FdnHousekeeperMgmt> info() {
        return CommonResult.success(fdnHousekeeperMgmtService.getLatest());
    }
}




