package com.zbkj.front.controller.business;

import com.zbkj.common.model.business.BizInformationMgmt;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizInformationMgmtSearchRequest;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.business.BizInformationMgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 资讯管理 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/business/information")
@Api(tags = "business:资讯管理") //配合swagger使用
public class BizInformationMgmtController {

    @Autowired
    private BizInformationMgmtService bizInformationMgmtService;

    /**
     * 分页显示资讯管理
     *
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     * <AUTHOR>
     * @since 2025-05-22
     */
    @ApiOperation(value = "分页列表") //配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<BizInformationMgmt>> getList(@Validated BizInformationMgmtSearchRequest request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<BizInformationMgmt> bizInformationMgmtCommonPage = CommonPage.restPage(bizInformationMgmtService.getList(request, pageParamRequest));
        return CommonResult.success(bizInformationMgmtCommonPage);
    }

    /**
     * 查询资讯管理信息
     *
     * @param id Integer
     * <AUTHOR>
     * @since 2025-05-22
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult<BizInformationMgmt> info(@RequestParam(value = "id") String id) {
        BizInformationMgmt bizInformationMgmt = bizInformationMgmtService.getById(id);
        return CommonResult.success(bizInformationMgmt);
    }
}



