package com.zbkj.front.service.impl;

import com.zbkj.common.model.system.SystemAttachment;
import com.zbkj.common.response.foundation.SysDictItemResponse;
import com.zbkj.common.response.foundation.SysDictResponse;
import com.zbkj.front.service.SysImgService;
import com.zbkj.service.service.SystemAttachmentService;
import com.zbkj.service.service.foundation.ISysDictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图片管理
 *
 * <AUTHOR>
 * @Date 2025/05/28
 * @Version 1.0
 */
@Slf4j
@Service
public class SysImgServiceImpl implements SysImgService {

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private SystemAttachmentService sysAttachmentService;

    @Override
    public Map<String, String> getImgUrl() {
        // 获取小程序图片字典
        SysDictResponse wxImg = sysDictService.getDictItems("wxImg");
        if (Objects.isNull(wxImg) || CollectionUtils.isEmpty(wxImg.getItemList())) {
            log.error("SysImgServiceImpl#getImgUrl,获取图片字典失败");
            throw new RuntimeException("500");
        }
        List<SysDictItemResponse> itemList = wxImg.getItemList();
        Map<String, String> dictCollect = itemList.stream().collect(Collectors.toMap(SysDictItemResponse::getItemCode, SysDictItemResponse::getItemDesc));

        // 获取图片信息
        List<SystemAttachment> attList = sysAttachmentService.getList(new ArrayList<>(dictCollect.values()));
        if (CollectionUtils.isEmpty(attList)) {
            log.error("SysImgServiceImpl#getImgUrl,获取图片信息失败");
            throw new RuntimeException("500");
        }
        Map<Integer, String> attCollect = attList.stream().collect(Collectors.toMap(SystemAttachment::getPid, SystemAttachment::getSattDir));

        // 进行数据封装
        dictCollect.forEach((key, value) -> dictCollect.put(key, attCollect.get(Integer.valueOf(value))));
        // 返回结果
        return dictCollect;
    }

}
