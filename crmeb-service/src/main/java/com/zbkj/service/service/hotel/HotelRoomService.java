package com.zbkj.service.service.hotel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomRequest;
import com.zbkj.common.request.hotel.HotelRoomSearchRequest;
import com.zbkj.common.request.hotel.HotelRoomWithPricesRequest;
import com.zbkj.common.response.hotel.HotelRoomResponse;

import java.util.List;

/**
 * 酒店房间服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelRoomService extends IService<HotelRoom> {

    /**
     * 获取所有启用的酒店房间
     *
     * @return 启用的房间列表
     */
    List<HotelRoom> getAllActiveRooms();

    /**
     * 获取指定商户的启用房间
     *
     * @param merchantId 商户ID
     * @return 房间列表
     */
    List<HotelRoom> getActiveRoomsByMerchant(Integer merchantId);

    /**
     * 根据房间ID获取房间信息
     *
     * @param roomId 房间ID
     * @return 房间信息
     */
    HotelRoom getById(Integer roomId);

    /**
     * 检查房间是否存在且启用
     *
     * @param roomId 房间ID
     * @return 是否存在且启用
     */
    boolean isActiveRoom(Integer roomId);

    /**
     * 更新房间状态
     *
     * @param roomId 房间ID
     * @param status 状态：0-禁用，1-启用
     * @return 更新结果
     */
    boolean updateRoomStatus(Integer roomId, Integer status);

    /**
     * 更新房间库存
     *
     * @param roomId 房间ID
     * @param totalRooms 总房间数
     * @return 更新结果
     */
    boolean updateRoomStock(Integer roomId, Integer totalRooms);

    /**
     * 商户端分页查询房型列表
     *
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<HotelRoomResponse> getMerchantPage(HotelRoomSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 新增房型
     *
     * @param request 房型信息
     * @return 新增结果
     */
    boolean saveHotelRoom(HotelRoomRequest request);

    /**
     * 新增房型和价格策略
     *
     * @param request 房型和价格策略信息
     * @return 新增结果
     */
    boolean saveHotelRoomWithPrices(HotelRoomWithPricesRequest request);

    /**
     * 修改房型
     *
     * @param request 房型信息
     * @return 修改结果
     */
    boolean updateHotelRoom(HotelRoomRequest request);

    /**
     * 获取房型详情
     *
     * @param id 房型ID
     * @return 房型详情
     */
    HotelRoomResponse getInfo(Integer id);

    /**
     * 删除房型
     *
     * @param id 房型ID
     * @return 删除结果
     */
    boolean delete(Integer id);

    /**
     * 检查房型名称是否重复
     *
     * @param roomName 房型名称
     * @param merchantId 商户ID
     * @param excludeId 排除的房型ID(修改时使用)
     * @return 是否重复
     */
    boolean checkRoomNameExists(String roomName, Integer merchantId, Integer excludeId);
}
