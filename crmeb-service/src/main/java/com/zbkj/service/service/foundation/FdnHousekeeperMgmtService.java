package com.zbkj.service.service.foundation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.foundation.FdnHousekeeperMgmt;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.FdnHousekeeperMgmtSearchRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @description FdnHousekeeperMgmtService 接口
 * @date 2025-05-20
 */
public interface FdnHousekeeperMgmtService extends IService<FdnHousekeeperMgmt> {

    /**
     * 分页查询列表
     * @param request 请求参数
     * @param pageParamRequest 分页类参数
     * <AUTHOR>
     * @since 2025-05-20
     * @return List<FdnHousekeeperMgmt>
     */
    List<FdnHousekeeperMgmt> getList(FdnHousekeeperMgmtSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取最新一条数据
     *
     * <AUTHOR>
     * @since 2025-05-20
     * @return FdnHousekeeperMgmt
     */
    FdnHousekeeperMgmt getLatest();
}
