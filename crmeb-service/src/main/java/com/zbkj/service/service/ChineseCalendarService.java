package com.zbkj.service.service;

import com.zbkj.common.enums.DateTypeEnum;
import com.zbkj.common.vo.HolidayCalendar;

import java.time.LocalDate;
import java.util.Map;

/**
 * 中国日历服务接口 - 专门用于酒店价格计算
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public interface ChineseCalendarService {

    /**
     * 获取指定年份的全年日期类型数据（核心方法）
     * 返回该年份1月1日到12月31日每一天的日期类型
     * 用于酒店价格策略计算
     *
     * @param year 年份（如2025）
     * @return Map<LocalDate, DateTypeEnum> 日期->类型映射
     *         包含该年份所有365/366天的数据
     */
    Map<LocalDate, DateTypeEnum> getYearDateTypes(Integer year);

    /**
     * 获取指定日期的类型（单个日期查询）
     * @param date 查询日期
     * @return 日期类型枚举
     */
    DateTypeEnum getDateType(LocalDate date);

    /**
     * 获取指定年份的节假日原始数据
     * @param year 年份
     * @return 节假日日历数据
     */
    HolidayCalendar getHolidayCalendar(Integer year);

    /**
     * 刷新指定年份的缓存
     * @param year 年份
     * @return 是否刷新成功
     */
    boolean refreshCache(Integer year);

    /**
     * 预热缓存（当前年份和下一年）
     */
    void preloadCache();
}
