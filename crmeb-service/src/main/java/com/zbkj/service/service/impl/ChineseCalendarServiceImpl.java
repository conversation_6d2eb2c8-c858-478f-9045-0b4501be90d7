package com.zbkj.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.zbkj.common.constants.CalendarConstants;
import com.zbkj.common.enums.DateTypeEnum;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.common.vo.HolidayCalendar;
import com.zbkj.common.vo.HolidayInfo;
import com.zbkj.service.service.ChineseCalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 中国日历服务实现类
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class ChineseCalendarServiceImpl implements ChineseCalendarService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取指定年份的全年日期类型数据（核心方法）
     * 这是酒店价格计算的核心数据源
     */
    @Override
    public Map<LocalDate, DateTypeEnum> getYearDateTypes(Integer year) {
        log.info("开始获取{}年全年日期类型数据", year);

        // 先尝试从缓存获取
        String cacheKey = "year_date_types:" + year;
        Object cachedObj = redisUtil.get(cacheKey);
        if (cachedObj != null) {
            try {
                // 从缓存重新构建Map
                Map<LocalDate, DateTypeEnum> cachedResult = new HashMap<>();
                if (cachedObj instanceof Map) {
                    Map<?, ?> rawMap = (Map<?, ?>) cachedObj;
                    for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
                        String dateStr = entry.getKey().toString();
                        String typeStr = entry.getValue().toString();

                        LocalDate date = LocalDate.parse(dateStr);
                        DateTypeEnum dateType = DateTypeEnum.valueOf(typeStr);
                        cachedResult.put(date, dateType);
                    }
                    log.info("从缓存获取{}年日期类型数据，共{}天", year, cachedResult.size());
                    return cachedResult;
                }
            } catch (Exception e) {
                log.warn("缓存数据格式异常，重新计算：{}", e.getMessage());
                // 删除异常缓存
                redisUtil.delete(cacheKey);
            }
        }

        // 缓存未命中，重新计算
        Map<LocalDate, DateTypeEnum> yearDateTypes = new HashMap<>();

        // 1. 获取该年份的节假日数据
        HolidayCalendar holidayCalendar = getHolidayCalendar(year);
        Map<String, HolidayInfo> holidayMap = new HashMap<>();

        if (holidayCalendar != null && holidayCalendar.getDates() != null) {
            for (HolidayInfo holiday : holidayCalendar.getDates()) {
                holidayMap.put(holiday.getDate(), holiday);
            }
        }

        // 2. 遍历该年份的每一天
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            DateTypeEnum dateType = calculateDateType(currentDate, holidayMap);
            yearDateTypes.put(currentDate, dateType);
            currentDate = currentDate.plusDays(1);
        }

        // 3. 缓存结果（缓存24小时）
        // 将LocalDate转换为String来避免序列化问题
        Map<String, String> cacheMap = new HashMap<>();
        for (Map.Entry<LocalDate, DateTypeEnum> entry : yearDateTypes.entrySet()) {
            cacheMap.put(entry.getKey().toString(), entry.getValue().name());
        }
        redisUtil.set(cacheKey, cacheMap, 24L * 60 * 60); // RedisUtil使用秒为单位，转换为Long

        log.info("计算完成{}年全年日期类型数据，共{}天，已缓存", year, yearDateTypes.size());
        return yearDateTypes;
    }

    /**
     * 获取指定日期的类型
     */
    @Override
    public DateTypeEnum getDateType(LocalDate date) {
        // 先尝试从年度缓存获取
        Map<LocalDate, DateTypeEnum> yearData = getYearDateTypes(date.getYear());
        DateTypeEnum result = yearData.get(date);

        if (result != null) {
            return result;
        }

        // 如果年度缓存中没有，单独计算
        HolidayCalendar holidayCalendar = getHolidayCalendar(date.getYear());
        Map<String, HolidayInfo> holidayMap = new HashMap<>();

        if (holidayCalendar != null && holidayCalendar.getDates() != null) {
            for (HolidayInfo holiday : holidayCalendar.getDates()) {
                holidayMap.put(holiday.getDate(), holiday);
            }
        }

        return calculateDateType(date, holidayMap);
    }

    /**
     * 获取节假日日历数据
     */
    @Override
    public HolidayCalendar getHolidayCalendar(Integer year) {
        String cacheKey = CalendarConstants.CACHE_KEY_HOLIDAY_CALENDAR.replace("{}", year.toString());

        // 先从缓存获取
        HolidayCalendar cached = (HolidayCalendar) redisUtil.get(cacheKey);
        if (cached != null) {
            log.debug("从缓存获取{}年节假日数据", year);
            return cached;
        }

        try {
            // 从远程API获取
            String url = CalendarConstants.HOLIDAY_API_URL
                    .replace("{version}", CalendarConstants.DEFAULT_VERSION)
                    .replace("{year}", year.toString());

            log.info("请求节假日API: {}", url);
            String response = restTemplate.getForObject(url, String.class);
            HolidayCalendar calendar = JSON.parseObject(response, HolidayCalendar.class);

            if (calendar != null) {
                // 缓存结果
                redisUtil.set(cacheKey, calendar, (long) CalendarConstants.CACHE_EXPIRE_HOURS * 60 * 60); // 转换为秒和Long类型
                log.info("获取{}年节假日数据成功，共{}条记录", year,
                        calendar.getDates() != null ? calendar.getDates().size() : 0);
            }

            return calendar;
        } catch (Exception e) {
            log.error("获取{}年节假日数据失败", year, e);
            return null;
        }
    }

    /**
     * 刷新缓存
     */
    @Override
    public boolean refreshCache(Integer year) {
        try {
            // 删除相关缓存
            String holidayCacheKey = CalendarConstants.CACHE_KEY_HOLIDAY_CALENDAR.replace("{}", year.toString());
            String yearCacheKey = "year_date_types:" + year;

            redisUtil.delete(holidayCacheKey);
            redisUtil.delete(yearCacheKey);

            // 重新获取数据
            getYearDateTypes(year);

            log.info("刷新{}年缓存成功", year);
            return true;
        } catch (Exception e) {
            log.error("刷新{}年缓存失败", year, e);
            return false;
        }
    }

    /**
     * 预热缓存
     */
    @Override
    public void preloadCache() {
        int currentYear = LocalDate.now().getYear();

        // 预热当前年份和下一年
        getYearDateTypes(currentYear);
        getYearDateTypes(currentYear + 1);

        log.info("缓存预热完成：{}年和{}年", currentYear, currentYear + 1);
    }

    /**
     * 计算具体日期的类型
     */
    private DateTypeEnum calculateDateType(LocalDate date, Map<String, HolidayInfo> holidayMap) {
        String dateStr = date.toString();
        HolidayInfo holidayInfo = holidayMap.get(dateStr);

        // 1. 优先判断是否为法定节假日
        if (holidayInfo != null && CalendarConstants.HOLIDAY_TYPE_PUBLIC.equals(holidayInfo.getType())) {
            return DateTypeEnum.HOLIDAY;
        }

        // 2. 判断是否为调休工作日
        if (holidayInfo != null && CalendarConstants.HOLIDAY_TYPE_TRANSFER_WORKDAY.equals(holidayInfo.getType())) {
            return DateTypeEnum.TRANSFER_WORKDAY;
        }

        // 3. 判断是否为自然周末
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            return DateTypeEnum.WEEKEND;
        }

        // 4. 默认为工作日
        return DateTypeEnum.WORKDAY;
    }
}
