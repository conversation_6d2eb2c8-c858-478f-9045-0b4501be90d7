package com.zbkj.service.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zbkj.common.model.product.ProductCategory;
import com.zbkj.common.model.merchant.MerchantProductCategory;
import com.zbkj.service.service.hotel.HotelCategoryManageService;
import com.zbkj.service.service.ProductCategoryService;
import com.zbkj.service.service.MerchantProductCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 酒店分类管理服务实现类
 * 
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelCategoryManageServiceImpl implements HotelCategoryManageService {

    @Autowired
    private MerchantProductCategoryService merchantProductCategoryService;

    @Autowired
    private ProductCategoryService productCategoryService;

    /**
     * 酒店分类名称常量
     */
    private static final String HOTEL_CATEGORY_NAME = "酒店预订";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer ensureMerchantCategory(Integer merchantId) {
        log.debug("确保商户 {} 的酒店分类存在", merchantId);
        
        // 先查询是否已存在
        Integer categoryId = getMerchantHotelCategoryId(merchantId);
        
        if (categoryId != null) {
            log.debug("商户 {} 的酒店分类已存在，ID: {}", merchantId, categoryId);
            return categoryId;
        }
        
        // 不存在则创建
        categoryId = createMerchantHotelCategory(merchantId);
        log.info("为商户 {} 创建酒店分类成功，ID: {}", merchantId, categoryId);
        
        return categoryId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer ensurePlatformCategory() {
        log.debug("确保平台酒店分类存在");
        
        // 先查询是否已存在
        Integer categoryId = getPlatformHotelCategoryId();
        
        if (categoryId != null) {
            log.debug("平台酒店分类已存在，ID: {}", categoryId);
            return categoryId;
        }
        
        // 不存在则创建
        categoryId = createPlatformHotelCategory();
        log.info("创建平台酒店分类成功，ID: {}", categoryId);
        
        return categoryId;
    }

    @Override
    public Integer getMerchantHotelCategoryId(Integer merchantId) {
        LambdaQueryWrapper<MerchantProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MerchantProductCategory::getMerId, merchantId)
               .eq(MerchantProductCategory::getName, HOTEL_CATEGORY_NAME)
               .eq(MerchantProductCategory::getIsDel, false)
               .last("LIMIT 1");
        
        MerchantProductCategory category = merchantProductCategoryService.getOne(wrapper);
        return category != null ? category.getId() : null;
    }

    @Override
    public Integer getPlatformHotelCategoryId() {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getName, HOTEL_CATEGORY_NAME)
               .eq(ProductCategory::getIsDel, false)
               .last("LIMIT 1");
        
        ProductCategory category = productCategoryService.getOne(wrapper);
        return category != null ? category.getId() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createMerchantHotelCategory(Integer merchantId) {
        log.info("开始为商户 {} 创建酒店分类", merchantId);
        
        MerchantProductCategory category = new MerchantProductCategory();
        category.setMerId(merchantId);
        category.setPid(0); // 顶级分类
        category.setName(HOTEL_CATEGORY_NAME);
        category.setIcon(""); // 可以设置默认图标
        category.setSort(1); // 排序靠前
        category.setIsShow(true);
        category.setIsDel(false);
        
        boolean saved = merchantProductCategoryService.save(category);
        
        if (!saved) {
            throw new RuntimeException("创建商户酒店分类失败，商户ID: " + merchantId);
        }
        
        log.info("商户 {} 的酒店分类创建成功，ID: {}", merchantId, category.getId());
        return category.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createPlatformHotelCategory() {
        log.info("开始创建平台酒店分类");
        
        ProductCategory category = new ProductCategory();
        category.setPid(0); // 顶级分类
        category.setName(HOTEL_CATEGORY_NAME);
        category.setIcon(""); // 可以设置默认图标
        category.setLevel(1); // 一级分类
        category.setSort(1); // 排序靠前
        category.setIsShow(true);
        category.setIsDel(false);
        
        boolean saved = productCategoryService.save(category);
        
        if (!saved) {
            throw new RuntimeException("创建平台酒店分类失败");
        }
        
        log.info("平台酒店分类创建成功，ID: {}", category.getId());
        return category.getId();
    }

    @Override
    public boolean hasMerchantHotelCategory(Integer merchantId) {
        return getMerchantHotelCategoryId(merchantId) != null;
    }

    @Override
    public boolean hasPlatformHotelCategory() {
        return getPlatformHotelCategoryId() != null;
    }
}
