package com.zbkj.service.service.foundation.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.model.foundation.FdnHousekeeperMgmt;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.FdnHousekeeperMgmtSearchRequest;
import com.zbkj.service.dao.foundation.FdnHousekeeperMgmtDao;
import com.zbkj.service.service.foundation.FdnHousekeeperMgmtService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description FdnHousekeeperMgmtServiceImpl 接口实现
 * @date 2025-05-20
 */
@Service
public class FdnHousekeeperMgmtServiceImpl extends ServiceImpl<FdnHousekeeperMgmtDao, FdnHousekeeperMgmt> implements FdnHousekeeperMgmtService {

    @Resource
    private FdnHousekeeperMgmtDao dao;


    @Override
    public List<FdnHousekeeperMgmt> getList(FdnHousekeeperMgmtSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 FdnHousekeeperMgmt 类的多条件查询
        LambdaQueryWrapper<FdnHousekeeperMgmt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        FdnHousekeeperMgmt model = new FdnHousekeeperMgmt();
        BeanUtils.copyProperties(request, model);
        lambdaQueryWrapper.setEntity(model);
        return dao.selectList(lambdaQueryWrapper);
    }

    @Override
    public FdnHousekeeperMgmt getLatest() {

        // 获取所有的记录
        List<FdnHousekeeperMgmt> list = super.list();
        // 条件判断
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 有效数据仅仅只会存在一条
        return list.get(0);

    }

}

