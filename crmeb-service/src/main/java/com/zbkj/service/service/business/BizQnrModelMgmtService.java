package com.zbkj.service.service.business;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.business.BizQnrModelMgmt;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizQnrModelMgmtSearchRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @description BizQnrModelMgmtService 接口
 * @date 2025-06-04
 */
public interface BizQnrModelMgmtService extends IService<BizQnrModelMgmt> {

    /**
     * 分页查询列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页类参数
     * @return List<BizQnrModelMgmt>
     * <AUTHOR>
     * @since 2025-06-04
     */
    List<BizQnrModelMgmt> getList(BizQnrModelMgmtSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取现在可以编辑的模板
     *
     * @return 模板信息，包含标题
     */
    BizQnrModelMgmt getInfo();

    /**
     * 更新参与数量
     *
     * @param modelId 模板id
     * @return boolean
     */
    Boolean updateParticipateNumber(String modelId);

    /**
     * 更新模板信息
     *
     * @param modelId         模板id
     * @param bizQnrModelMgmt 更新的内容
     * @return 更新的结果
     */
    Boolean updateById(String modelId, BizQnrModelMgmt bizQnrModelMgmt);
}