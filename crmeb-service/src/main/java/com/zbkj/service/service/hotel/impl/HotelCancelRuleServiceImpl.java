package com.zbkj.service.service.hotel.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.admin.SystemAdmin;
import com.zbkj.common.model.hotel.HotelCancelRule;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelCancelRuleRequest;
import com.zbkj.common.request.hotel.HotelCancelRuleSearchRequest;
import com.zbkj.common.response.hotel.HotelCancelRuleResponse;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.dao.hotel.HotelCancelRuleDao;
import com.zbkj.service.service.SystemAdminService;
import com.zbkj.service.service.hotel.HotelCancelRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒店取消规则服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelCancelRuleServiceImpl extends ServiceImpl<HotelCancelRuleDao, HotelCancelRule> implements HotelCancelRuleService {

    @Autowired
    private SystemAdminService systemAdminService;

    @Override
    public List<HotelCancelRule> getByMerchantId(Integer merchantId) {
        if (merchantId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<HotelCancelRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelCancelRule::getMerId, merchantId)
               .eq(HotelCancelRule::getIsDel, 0)
               .orderByDesc(HotelCancelRule::getSort)
               .orderByDesc(HotelCancelRule::getCreateTime);

        return list(wrapper);
    }

    @Override
    public List<HotelCancelRule> getActiveRulesByMerchant(Integer merchantId) {
        if (merchantId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<HotelCancelRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelCancelRule::getMerId, merchantId)
               .eq(HotelCancelRule::getStatus, 1)
               .eq(HotelCancelRule::getIsDel, 0)
               .orderByDesc(HotelCancelRule::getSort)
               .orderByDesc(HotelCancelRule::getCreateTime);

        return list(wrapper);
    }

    @Override
    public boolean updateRuleStatus(Integer ruleId, Integer status) {
        if (ruleId == null || status == null) {
            return false;
        }

        HotelCancelRule rule = new HotelCancelRule();
        rule.setId(ruleId);
        rule.setStatus(status);
        rule.setUpdateTime(new Date());

        return updateById(rule);
    }

    @Override
    public PageInfo<HotelCancelRuleResponse> getMerchantPage(HotelCancelRuleSearchRequest request, PageParamRequest pageParamRequest) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        Page<HotelCancelRule> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<HotelCancelRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelCancelRule::getMerId, merchantId)
               .eq(HotelCancelRule::getIsDel, 0);

        // 搜索条件
        if (StrUtil.isNotBlank(request.getRuleName())) {
            wrapper.like(HotelCancelRule::getRuleName, request.getRuleName());
        }
        if (request.getPenaltyType() != null) {
            wrapper.eq(HotelCancelRule::getPenaltyType, request.getPenaltyType());
        }
        if (request.getStatus() != null) {
            wrapper.eq(HotelCancelRule::getStatus, request.getStatus());
        }

        // 排序
        wrapper.orderByDesc(HotelCancelRule::getSort)
               .orderByDesc(HotelCancelRule::getCreateTime);

        List<HotelCancelRule> list = list(wrapper);

        // 转换为响应对象
        List<HotelCancelRuleResponse> responseList = list.stream().map(this::convertToResponse).collect(Collectors.toList());

        return CommonPage.copyPageInfo(page, responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCancelRule(HotelCancelRuleRequest request) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查规则名称是否重复
        if (checkRuleNameExists(request.getRuleName(), merchantId, null)) {
            throw new CrmebException("规则名称已存在");
        }

        // 验证取消类型对应的参数
        validateCancelTypeParams(request);

        HotelCancelRule rule = new HotelCancelRule();
        BeanUtil.copyProperties(request, rule);
        rule.setMerId(merchantId);
        rule.setIsDel(0);
        rule.setCreateId(admin.getId());
        rule.setCreateTime(new Date());
        rule.setUpdateTime(new Date());

        // 设置默认值
        if (rule.getStatus() == null) {
            rule.setStatus(1);
        }

        return super.save(rule);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCancelRule(HotelCancelRuleRequest request) {
        if (request.getId() == null) {
            throw new CrmebException("规则ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查规则是否存在且属于当前商户
        HotelCancelRule existRule = getById(request.getId());
        if (existRule == null || existRule.getIsDel() == 1) {
            throw new CrmebException("取消规则不存在");
        }
        if (!existRule.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限操作此取消规则");
        }

//        // 检查规则名称是否重复
//        if (checkRuleNameExists(request.getRuleName(), merchantId, request.getId())) {
//            throw new CrmebException("规则名称已存在");
//        }

        // 验证取消类型对应的参数
        validateCancelTypeParams(request);

        HotelCancelRule rule = new HotelCancelRule();
        BeanUtil.copyProperties(request, rule);
        rule.setMerId(merchantId);
        rule.setUpdateTime(new Date());

        return updateById(rule);
    }

    @Override
    public HotelCancelRuleResponse getRuleInfo(Integer id) {
        if (id == null) {
            throw new CrmebException("规则ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelCancelRule rule = getById(id);
        if (rule == null || rule.getIsDel() == 1) {
            throw new CrmebException("取消规则不存在");
        }
        if (!rule.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限查看此取消规则");
        }

        return convertToResponse(rule);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRule(Integer id) {
        if (id == null) {
            throw new CrmebException("规则ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelCancelRule rule = getById(id);
        if (rule == null || rule.getIsDel() == 1) {
            throw new CrmebException("取消规则不存在");
        }
        if (!rule.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限删除此取消规则");
        }

        // 软删除 - 使用LambdaUpdateWrapper避免MyBatis-Plus逻辑删除冲突
        LambdaUpdateWrapper<HotelCancelRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HotelCancelRule::getId, id)
                     .set(HotelCancelRule::getIsDel, 1)
                     .set(HotelCancelRule::getUpdateTime, new Date());

        return update(updateWrapper);
    }

    @Override
    public boolean checkRuleNameExists(String ruleName, Integer merchantId, Integer excludeId) {
        LambdaQueryWrapper<HotelCancelRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelCancelRule::getRuleName, ruleName)
               .eq(HotelCancelRule::getMerId, merchantId)
               .eq(HotelCancelRule::getIsDel, 0);

        if (excludeId != null) {
            wrapper.ne(HotelCancelRule::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public BigDecimal calculateCancelFee(BigDecimal orderAmount, Integer advanceHours, Integer merchantId) {
        if (orderAmount == null || advanceHours == null || merchantId == null) {
            return BigDecimal.ZERO;
        }

        // 获取匹配的取消规则
        HotelCancelRule matchedRule = getMatchedRule(advanceHours, merchantId);
        if (matchedRule == null) {
            // 没有匹配的规则，默认免费取消
            return BigDecimal.ZERO;
        }

        // 特殊处理：检查是否为"不可取消"规则
        if (matchedRule.getPenaltyType() == 1 &&
            matchedRule.getPenaltyValue() != null &&
            matchedRule.getPenaltyValue().compareTo(new BigDecimal("100")) == 0) {
            // 返回-1表示不可取消，前端需要特殊处理
            return new BigDecimal("-1");
        }

        // 根据扣费类型计算费用
        switch (matchedRule.getPenaltyType()) {
            case 1: // 按比例扣费
                if (matchedRule.getPenaltyValue() != null) {
                    return orderAmount.multiply(matchedRule.getPenaltyValue())
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                }
                return BigDecimal.ZERO;
            case 2: // 固定金额扣费
                return matchedRule.getPenaltyValue() != null ? matchedRule.getPenaltyValue() : BigDecimal.ZERO;
            default:
                return BigDecimal.ZERO;
        }
    }

    @Override
    public HotelCancelRule getMatchedRule(Integer advanceHours, Integer merchantId) {
        if (advanceHours == null || merchantId == null) {
            return null;
        }

        // 获取该商户的所有启用规则，按提前小时数降序排序
        List<HotelCancelRule> rules = getActiveRulesByMerchant(merchantId);

        // 按提前小时数降序排序，从大到小匹配
        rules.sort((r1, r2) -> r2.getAdvanceHours().compareTo(r1.getAdvanceHours()));

        // 按照用户需求的区间匹配逻辑：
        // 1. 48小时以上：全额退款
        // 2. 24-48小时：扣5%
        // 3. 12-24小时：扣10%
        // 4. 2小时内：不可取消
        // 5. 其他时间范围(2-12小时)：全额退款
        //
        // 匹配策略：精确区间匹配，而不是简单的大于等于匹配

        // 特殊处理：2小时内不可取消
        if (advanceHours < 2) {
            // 查找"不可取消"规则(penalty_value=100的规则)
            for (HotelCancelRule rule : rules) {
                if (rule.getPenaltyType() == 1 &&
                    rule.getPenaltyValue() != null &&
                    rule.getPenaltyValue().compareTo(new BigDecimal("100")) == 0) {
                    return rule;
                }
            }
        }

        // 严格按照用户需求的精确区间匹配：
        // 1. 提前≥48小时：匹配48小时规则 → 免费取消 (penalty_value=0.00)
        // 2. 提前≥24且<48小时：匹配24小时规则 → 扣5% (penalty_value=5.00)
        // 3. 提前≥12且<24小时：匹配12小时规则 → 扣10% (penalty_value=10.00)
        // 4. 提前≥2且<12小时：无匹配规则 → 返回null → 免费取消
        // 5. 提前<2小时：匹配不可取消规则 → 不可取消 (penalty_value=100.00)

        if (advanceHours >= 48) {
            // 区间1：提前≥48小时 → 查找48小时规则（免费取消）
            for (HotelCancelRule rule : rules) {
                if (rule.getAdvanceHours() == 48 &&
                    rule.getPenaltyType() == 1 &&
                    rule.getPenaltyValue() != null &&
                    rule.getPenaltyValue().compareTo(BigDecimal.ZERO) == 0) {
                    return rule;
                }
            }
        } else if (advanceHours >= 24 && advanceHours < 48) {
            // 区间2：提前≥24且<48小时 → 查找24小时规则（扣5%）
            for (HotelCancelRule rule : rules) {
                if (rule.getAdvanceHours() == 24 &&
                    rule.getPenaltyType() == 1 &&
                    rule.getPenaltyValue() != null &&
                    rule.getPenaltyValue().compareTo(new BigDecimal("5.00")) == 0) {
                    return rule;
                }
            }
        } else if (advanceHours >= 12 && advanceHours < 24) {
            // 区间3：提前≥12且<24小时 → 查找12小时规则（扣10%）
            for (HotelCancelRule rule : rules) {
                if (rule.getAdvanceHours() == 12 &&
                    rule.getPenaltyType() == 1 &&
                    rule.getPenaltyValue() != null &&
                    rule.getPenaltyValue().compareTo(new BigDecimal("10.00")) == 0) {
                    return rule;
                }
            }
        } else if (advanceHours >= 2 && advanceHours < 12) {
            // 区间4：提前≥2且<12小时 → 无匹配规则，返回null表示免费取消
            return null;
        }

        // 区间5：提前<2小时的情况已在上面特殊处理
        // 如果到这里，说明没有找到匹配的规则，默认免费取消
        return null;
    }

    /**
     * 验证扣费类型对应的参数
     * 该方法旨在确保酒店取消规则请求中的扣费值和扣费类型符合业务逻辑和数据有效性要求
     *
     * @param request 包含取消规则信息的请求对象，包括扣费类型和扣费值等信息
     * @throws CrmebException 如果扣费值无效或扣费类型不支持，则抛出异常
     */
    private void validateCancelTypeParams(HotelCancelRuleRequest request) {
        // 检查扣费值是否为空或负数，确保扣费值的有效性
        if (request.getPenaltyValue() == null || request.getPenaltyValue().compareTo(BigDecimal.ZERO) < 0) {
            throw new CrmebException("扣费值不能为空且不能小于0");
        }

        // 根据扣费类型进行相应的参数校验
        switch (request.getPenaltyType()) {
            case 1: // 按比例扣费
                // 当按比例扣费时，确保扣费值不超过100%，以避免数据输入错误导致的逻辑错误
                if (request.getPenaltyValue().compareTo(new BigDecimal("100")) > 0) {
                    throw new CrmebException("按比例扣费时，扣费值不能超过100％");
                }
                break;
            case 2: // 固定金额扣费
                // 固定金额无特殊限制，只要大于等于0即可，因此此处无需额外校验
                break;
            default:
                // 当扣费类型非1或2时，抛出异常，表示不支持该扣费类型
                throw new CrmebException("不支持的扣费类型");
        }
    }


    /**
     * 转换为响应对象
     */
    private HotelCancelRuleResponse convertToResponse(HotelCancelRule rule) {
        HotelCancelRuleResponse response = new HotelCancelRuleResponse();
        BeanUtil.copyProperties(rule, response);

        // 设置状态描述
        response.setStatusDesc(rule.getStatus() == 1 ? "启用" : "禁用");

        // 设置取消类型描述
        response.setPenaltyTypeDesc(getPenaltyTypeDesc(rule.getPenaltyType()));

        // 设置提前取消时间描述
        response.setAdvanceTimeDesc(getAdvanceTimeDesc(rule.getAdvanceHours()));

        // 获取商户名称
        try {
            SystemAdmin merchant = systemAdminService.getById(rule.getMerId());
            if (merchant != null) {
                response.setMerchantName(merchant.getRealName());
            }
        } catch (Exception e) {
            log.warn("获取商户名称失败，商户ID: {}", rule.getMerId(), e);
        }

        return response;
    }

    /**
     * 获取扣费类型描述
     */
    private String getPenaltyTypeDesc(Integer penaltyType) {
        switch (penaltyType) {
            case 1:
                return "按比例扣费";
            case 2:
                return "固定金额扣费";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取提前取消时间描述
     */
    private String getAdvanceTimeDesc(Integer advanceHours) {
        if (advanceHours == null) {
            return "";
        }

        if (advanceHours == 0) {
            return "随时可取消";
        } else if (advanceHours < 24) {
            return "提前" + advanceHours + "小时";
        } else {
            int days = advanceHours / 24;
            int hours = advanceHours % 24;
            if (hours == 0) {
                return "提前" + days + "天";
            } else {
                return "提前" + days + "天" + hours + "小时";
            }
        }
    }
}
