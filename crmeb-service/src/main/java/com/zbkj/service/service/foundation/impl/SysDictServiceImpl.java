package com.zbkj.service.service.foundation.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.model.foundation.SysDict;
import com.zbkj.common.model.foundation.SysDictItem;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.SysDictRequest;
import com.zbkj.common.response.foundation.SysDictItemResponse;
import com.zbkj.common.response.foundation.SysDictResponse;
import com.zbkj.service.dao.foundation.SysDictDao;
import com.zbkj.service.service.foundation.ISysDictItemService;
import com.zbkj.service.service.foundation.ISysDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 字典维护实现类
 */
@Service
@Slf4j
public class SysDictServiceImpl extends ServiceImpl<SysDictDao, SysDict> implements ISysDictService {
    @Autowired
    private ISysDictItemService sysDictItemService;

    @Override
    public List<SysDict> getList(SysDictRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<SysDict> queryWrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(request.getDictCode())) {
            queryWrapper.eq(SysDict::getDictCode, request.getDictCode());
        }
        if (StrUtil.isNotBlank(request.getDictName())) {
            queryWrapper.like(SysDict::getDictName, request.getDictName());
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public SysDictResponse getDictItems(String dictCode) {
        SysDict sysDict = super.getOne(Wrappers.<SysDict>lambdaQuery().eq(SysDict::getDictCode, dictCode));
        if (sysDict != null) {
            List<SysDictItem> sysDictItems = sysDictItemService.getSysDictItems(sysDict.getId());
            List<SysDictItemResponse> itemList = new ArrayList<>();
            Map<String, String> dictMap = new HashMap<>();
            // 存在字段进行转化
            if (ObjectUtil.isNotEmpty(sysDictItems)) {
                for (SysDictItem sysDictItem : sysDictItems) {
                    SysDictItemResponse sysDictItemResponse = new SysDictItemResponse();
                    BeanUtils.copyProperties(sysDictItem, sysDictItemResponse);
                    sysDictItemResponse.setKey(sysDictItem.getId());
                    sysDictItemResponse.setValue(sysDictItem.getItemCode());
                    sysDictItemResponse.setLabel(sysDictItem.getItemName());
                    dictMap.put(sysDictItem.getItemCode(), sysDictItem.getItemName());
                    itemList.add(sysDictItemResponse);
                }
                SysDictResponse sysDictResponse = new SysDictResponse();
                sysDictResponse.setItemList(itemList);
                sysDictResponse.setDictMap(dictMap);
                return sysDictResponse;
            }
        }
        return null;
    }
}
