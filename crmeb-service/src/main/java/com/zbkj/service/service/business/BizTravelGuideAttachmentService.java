package com.zbkj.service.service.business;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.business.BizTravelGuideAttachment;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: BizTravelGuideAttachmentService
 * @Description: 攻略管理附件接口
 * @Author: zlj
 * @Date: 2025-06-10 20:35
 * @Version: 1.0
 **/
public interface BizTravelGuideAttachmentService extends IService<BizTravelGuideAttachment> {
    Map<String, String> getListByGuideId(String id);

    List<BizTravelGuideAttachment> getListById(String id);
}
