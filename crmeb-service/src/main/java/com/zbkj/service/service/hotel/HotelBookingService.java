package com.zbkj.service.service.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomListRequest;
import com.zbkj.common.request.hotel.HotelSearchRequest;
import com.zbkj.common.response.hotel.HotelListResponse;
import com.zbkj.common.response.hotel.HotelRoomListResponse;

import java.util.List;

/**
 * 酒店预订服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelBookingService {

    /**
     * 获取酒店列表
     *
     * @param request 搜索请求
     * @param pageParamRequest 分页参数
     * @return 酒店列表
     */
    PageInfo<HotelListResponse> getHotelList(HotelSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取酒店详情
     *
     * @param hotelId 酒店ID（商户ID）
     * @return 酒店详情
     */
    HotelListResponse getHotelDetail(Integer hotelId);

    /**
     * 获取房型列表
     *
     * @param request 房型列表请求
     * @return 房型列表
     */
    List<HotelRoomListResponse> getRoomList(HotelRoomListRequest request);

    /**
     * 解析商品名称获取酒店信息
     *
     * @param productName 商品名称
     * @return 酒店商品信息
     */
    HotelProductInfo parseProductName(String productName);

    /**
     * 酒店商品信息内部类
     */
    class HotelProductInfo {
        private String hotelName;
        private String roomTypeName;
        private String checkInDate;

        public HotelProductInfo() {}

        public HotelProductInfo(String hotelName, String roomTypeName, String checkInDate) {
            this.hotelName = hotelName;
            this.roomTypeName = roomTypeName;
            this.checkInDate = checkInDate;
        }

        // Getters and Setters
        public String getHotelName() {
            return hotelName;
        }

        public void setHotelName(String hotelName) {
            this.hotelName = hotelName;
        }

        public String getRoomTypeName() {
            return roomTypeName;
        }

        public void setRoomTypeName(String roomTypeName) {
            this.roomTypeName = roomTypeName;
        }

        public String getCheckInDate() {
            return checkInDate;
        }

        public void setCheckInDate(String checkInDate) {
            this.checkInDate = checkInDate;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String hotelName;
            private String roomTypeName;
            private String checkInDate;

            public Builder hotelName(String hotelName) {
                this.hotelName = hotelName;
                return this;
            }

            public Builder roomTypeName(String roomTypeName) {
                this.roomTypeName = roomTypeName;
                return this;
            }

            public Builder checkInDate(String checkInDate) {
                this.checkInDate = checkInDate;
                return this;
            }

            public HotelProductInfo build() {
                return new HotelProductInfo(hotelName, roomTypeName, checkInDate);
            }
        }
    }
}
