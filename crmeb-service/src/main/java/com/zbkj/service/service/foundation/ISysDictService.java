package com.zbkj.service.service.foundation;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zbkj.common.model.foundation.SysDict;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.foundation.SysDictRequest;
import com.zbkj.common.response.foundation.SysDictResponse;

import java.util.List;

/**
 * 字典接口
 */
public interface ISysDictService extends IService<SysDict> {

    List<SysDict> getList(SysDictRequest request, PageParamRequest pageParamRequest);

    SysDictResponse getDictItems(String dictCode);
}
