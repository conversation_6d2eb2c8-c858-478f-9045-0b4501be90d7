package com.zbkj.service.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.zbkj.common.model.business.BizNoticeMgmt;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizNoticeMgmtSearchRequest;
import com.zbkj.service.dao.business.BizNoticeMgmtDao;
import com.zbkj.service.service.business.BizNoticeMgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description BizNoticeMgmtServiceImpl 接口实现
 * @date 2025-05-22
 */
@Service
public class BizNoticeMgmtServiceImpl extends ServiceImpl<BizNoticeMgmtDao, BizNoticeMgmt> implements BizNoticeMgmtService {

    @Resource
    private BizNoticeMgmtDao dao;


    @Override
    public List<BizNoticeMgmt> getList(BizNoticeMgmtSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        //带 BizNoticeMgmt 类的多条件查询
        LambdaQueryWrapper<BizNoticeMgmt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(request.getNoticeTitle()), BizNoticeMgmt::getNoticeTitle, request.getNoticeTitle());
        lambdaQueryWrapper.orderByDesc(BizNoticeMgmt::getCreateTime);
        // 开始查询
        return dao.selectList(lambdaQueryWrapper);
    }

    @Override
    public BizNoticeMgmt infoIndex() {
        // 创建查询条件
        PageHelper.startPage(1, 1);
        LambdaQueryWrapper<BizNoticeMgmt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(BizNoticeMgmt::getCreateTime);
        // 开始查询
        List<BizNoticeMgmt> bizNoticeMgmtList = dao.selectList(lambdaQueryWrapper);
        // 获取第一条数据进行返回
        if (CollectionUtils.isEmpty(bizNoticeMgmtList)) {
            return null;
        }
        return bizNoticeMgmtList.get(0);
    }

}

