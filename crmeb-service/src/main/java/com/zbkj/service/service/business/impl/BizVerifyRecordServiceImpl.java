package com.zbkj.service.service.business.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.business.BizHotspotMgmt;
import com.zbkj.common.model.business.BizVerifyRecord;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.business.BizVerifyRecordRequest;
import com.zbkj.common.response.busines.BizVerifyRecordResponse;
import com.zbkj.service.dao.business.BizVerifyRecordDao;
import com.zbkj.service.service.business.BizHotspotMgmtService;
import com.zbkj.service.service.business.BizReceiveRecordService;
import com.zbkj.service.service.business.BizVerifyRecordService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName: BizVerifyRecordServiceImpl
 * @Description: 龙宫币核销记录实现类
 * @Author: zlj
 * @Date: 2025-06-03 17:53
 * @Version: 1.0
 **/
@Service
public class BizVerifyRecordServiceImpl extends ServiceImpl<BizVerifyRecordDao, BizVerifyRecord> implements BizVerifyRecordService {

    @Resource
    private BizVerifyRecordDao dao;
    @Autowired
    private BizHotspotMgmtService bizHotspotMgmtService;
    @Autowired
    private BizReceiveRecordService bizReceiveRecordService;

    @Override
    public List<BizVerifyRecord> getList(BizVerifyRecordRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<BizVerifyRecord> queryWrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(request.getVerifyUserId())) {
            queryWrapper.eq(BizVerifyRecord::getVerifyUserId, request.getVerifyUserId());
        }
        if (StrUtil.isNotBlank(request.getVerifyHotspotName())) {
            queryWrapper.eq(BizVerifyRecord::getVerifyHotspotName, request.getVerifyHotspotName());
        }
        if (StrUtil.isNotBlank(request.getVerifyUserName())) {
            queryWrapper.like(BizVerifyRecord::getVerifyUserName, request.getVerifyUserName());
        }
        if (StrUtil.isNotBlank(request.getVerifyUserPhone())) {
            queryWrapper.like(BizVerifyRecord::getVerifyUserPhone, request.getVerifyUserPhone());
        }
        if (StrUtil.isNotBlank(request.getVerifyActionMerchant())) {
            queryWrapper.like(BizVerifyRecord::getVerifyActionMerchant, request.getVerifyActionMerchant());
        }
        if (StrUtil.isNotBlank(request.getVerifyProductName())) {
            queryWrapper.like(BizVerifyRecord::getVerifyProductName, request.getVerifyProductName());
        }
        if (StrUtil.isNotBlank(request.getVerifyCode())) {
            queryWrapper.eq(BizVerifyRecord::getVerifyCode, request.getVerifyCode());
        }
        queryWrapper.orderByDesc(BizVerifyRecord::getVerifyState);
        List<BizVerifyRecord> bizVerifyRecords = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(bizVerifyRecords)) {
            return bizVerifyRecords;
        }
        // 获取景点信息
//        List<String> hotspotCode = bizVerifyRecords.stream().map(BizVerifyRecord::getVerifyHotspotName).collect(Collectors.toList());
//        List<BizHotspotMgmt> dataListByHotspotCode = bizHotspotMgmtService.getDataListByHotspotCode(hotspotCode);
//        Map<String, String> collect = dataListByHotspotCode.stream().collect(Collectors.toMap(BizHotspotMgmt::getHotspotCode, BizHotspotMgmt::getHotspotName));
        for (BizVerifyRecord bizVerifyRecord : bizVerifyRecords) {
            bizVerifyRecord.setHotspotName(bizVerifyRecord.getVerifyActionMerchant());
        }
        return bizVerifyRecords;
    }

    @Override
    public PageInfo<BizVerifyRecordResponse> queryAllList(BizVerifyRecordRequest request, PageParamRequest pageParamRequest) {
        Page<BizVerifyRecordResponse> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<BizVerifyRecordResponse> list = dao.queryAllList(request);
        if (CollectionUtils.isEmpty(list)) {
            return CommonPage.copyPageInfo(page, list);
        }

        // 获取景点信息
        List<String> hotspotCode = list.stream().map(BizVerifyRecordResponse::getName).collect(Collectors.toList());
        List<BizHotspotMgmt> dataListByHotspotCode = bizHotspotMgmtService.getDataListByHotspotCode(hotspotCode);
        Map<String, String> collect = dataListByHotspotCode.stream().collect(Collectors.toMap(BizHotspotMgmt::getHotspotCode, BizHotspotMgmt::getHotspotName));
        for (BizVerifyRecordResponse bizVerifyRecord : list) {
            if ("verify".equals(bizVerifyRecord.getSource())) {
                bizVerifyRecord.setHotspotName(bizVerifyRecord.getName());
            }
            if ("receive".equals(bizVerifyRecord.getSource())) {
                bizVerifyRecord.setHotspotName(collect.get(bizVerifyRecord.getName()));
            }
        }

        return CommonPage.copyPageInfo(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(BizVerifyRecord request) {
        // 参数验证
        if (Objects.isNull(request)) {
            log.error("BizVerifyRecordServiceImpl#save, 参数不能为空");
            return false;
        }

        // 改变记录表的状态
        boolean result = bizReceiveRecordService.updateByUserId(request.getVerifyUserId(), request.getVerifyCode(), request.getVerifyNumber());
        if (BooleanUtils.isFalse(result)) {
            log.error("BizVerifyRecordServiceImpl#save, 记录表更新失败");
            throw new RuntimeException("500");
        }

        // 保存到核销表
        boolean save = super.save(request);
        if (BooleanUtils.isFalse(save)) {
            log.error("BizVerifyRecordServiceImpl#save, 核销表插入失败");
            throw new RuntimeException("500");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(String id, BizVerifyRecord request) {
        // 参数验证
        if (StringUtils.isEmpty(id) || Objects.isNull(request)) {
            log.error("BizVerifyRecordServiceImpl#updateById, 参数不能为空");
            return false;
        }

        // 根据id获取记录
        BizVerifyRecord byId = super.getById(id);
        if (Objects.isNull(byId)) {
            log.error("BizVerifyRecordServiceImpl#updateById, 根据id获取记录失败");
            return false;
        }

        // 处理记录表
        boolean result = bizReceiveRecordService.cancelByUserId(byId.getVerifyUserId(), byId.getVerifyCode(), byId.getVerifyNumber());
        if (BooleanUtils.isFalse(result)) {
            log.error("BizVerifyRecordServiceImpl#updateById, 改变记录表状态失败");
            throw new RuntimeException("500");
        }

        // 删除核销表记录
        LambdaUpdateWrapper<BizVerifyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BizVerifyRecord::getId, id);
        updateWrapper.set(BizVerifyRecord::getIsDel, 1);
        boolean update = super.update(updateWrapper);
        if (BooleanUtils.isFalse(update)) {
            log.error("BizVerifyRecordServiceImpl#updateById, 删除核销表记录失败");
            throw new RuntimeException("500");
        }
        return true;
    }
}
