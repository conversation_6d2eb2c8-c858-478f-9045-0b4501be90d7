package com.zbkj.service.service.hotel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.hotel.HotelCancelRule;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelCancelRuleRequest;
import com.zbkj.common.request.hotel.HotelCancelRuleSearchRequest;
import com.zbkj.common.response.hotel.HotelCancelRuleResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * 酒店取消规则服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelCancelRuleService extends IService<HotelCancelRule> {

    /**
     * 获取指定商户的所有取消规则
     *
     * @param merchantId 商户ID
     * @return 取消规则列表
     */
    List<HotelCancelRule> getByMerchantId(Integer merchantId);

    /**
     * 获取指定商户的启用取消规则
     *
     * @param merchantId 商户ID
     * @return 启用的取消规则列表
     */
    List<HotelCancelRule> getActiveRulesByMerchant(Integer merchantId);

    /**
     * 更新取消规则状态
     *
     * @param ruleId 规则ID
     * @param status 状态：0-禁用，1-启用
     * @return 更新结果
     */
    boolean updateRuleStatus(Integer ruleId, Integer status);

    /**
     * 商户端分页查询取消规则列表
     *
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<HotelCancelRuleResponse> getMerchantPage(HotelCancelRuleSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 新增取消规则
     *
     * @param request 取消规则信息
     * @return 新增结果
     */
    boolean saveCancelRule(HotelCancelRuleRequest request);

    /**
     * 修改取消规则
     *
     * @param request 取消规则信息
     * @return 修改结果
     */
    boolean updateCancelRule(HotelCancelRuleRequest request);

    /**
     * 获取取消规则详情
     *
     * @param id 规则ID
     * @return 规则详情
     */
    HotelCancelRuleResponse getRuleInfo(Integer id);

    /**
     * 删除取消规则
     *
     * @param id 规则ID
     * @return 删除结果
     */
    boolean deleteRule(Integer id);

    /**
     * 检查规则名称是否重复
     *
     * @param ruleName 规则名称
     * @param merchantId 商户ID
     * @param excludeId 排除的规则ID(修改时使用)
     * @return 是否重复
     */
    boolean checkRuleNameExists(String ruleName, Integer merchantId, Integer excludeId);

    /**
     * 计算取消费用
     *
     * @param orderAmount 订单金额
     * @param advanceHours 提前取消小时数
     * @param merchantId 商户ID
     * @return 取消费用
     */
    BigDecimal calculateCancelFee(BigDecimal orderAmount, Integer advanceHours, Integer merchantId);

    /**
     * 获取匹配的取消规则
     *
     * @param advanceHours 提前取消小时数
     * @param merchantId 商户ID
     * @return 匹配的取消规则
     */
    HotelCancelRule getMatchedRule(Integer advanceHours, Integer merchantId);
}
