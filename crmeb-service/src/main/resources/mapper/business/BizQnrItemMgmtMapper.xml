<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.business.BizQnrItemMgmtDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zbkj.common.model.business.BizQnrItemMgmt">
        <id column="id" property="id"/>
        <result column="model_id" property="modelId"/>
        <result column="item_title" property="itemTitle"/>
        <result column="item_sub_title" property="itemSubTitle"/>
        <result column="item_parent_id" property="itemParentId"/>
        <result column="item_sort" property="itemSort"/>
        <result column="item_result_type" property="itemResultType"/>
        <result column="item_result_option" property="itemResultOption"/>
        <result column="item_is_required" property="itemIsRequired"/>
        <result column="is_del" property="isDel"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    </sql>

</mapper> 