<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.community.CommunityNotesRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zbkj.common.model.community.CommunityNotesRelation">
        <id column="id" property="id" />
        <result column="note_id" property="noteId" />
        <result column="author_id" property="authorId" />
        <result column="uid" property="uid" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, note_id, author_id, uid, `type`, create_time
    </sql>

</mapper>
