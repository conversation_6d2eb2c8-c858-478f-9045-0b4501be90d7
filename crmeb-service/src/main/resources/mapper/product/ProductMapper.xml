<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.ProductDao">

    <select id="getPlatformPageList" resultType="com.zbkj.common.response.PlatformProductListResponse" parameterType="Map">
        SELECT p.id,p.image,p.name as name,p.keyword,p.category_id as categoryId,p.price,p.sales,p.stock,p.ficti,
        p.audit_status as auditStatus,p.reason,p.rank,m.name as merchantName,m.is_self as isSelf,p.rank,p.type,
        p.spec_type as specType
        FROM eb_product AS p
        right join eb_merchant AS m on p.mer_id = m.id
        where p.is_del = 0 and p.is_recycle = 0 and p.marketing_type = 0
        <choose>
            <when test='type == "1"'>
                and p.is_show = 1
                and p.audit_status in (0,2)
            </when>
            <when test='type == "2"'>
                and p.is_show = 0
                and p.is_audit = 0
                and p.audit_status in (0,2)
            </when>
            <when test='type == "6"'>
                and p.audit_status = 1
                and p.is_audit = 1
            </when>
            <when test='type == "7"'>
                and p.audit_status = 3
                and p.is_audit = 0
            </when>
        </choose>
        <if test="merId != null and merId !='' ">
            and p.mer_id = #{merId}
        </if>
        <if test="self != null">
            and m.is_self = #{self}
        </if>
        <if test="categoryIds != null and categoryIds !='' ">
            and find_in_set(p.category_id, #{categoryIds, jdbcType=VARCHAR})
        </if>
        <if test="keywords != null and keywords !='' ">
            and (p.name like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%')
            or p.keyword like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="isPaidMember != null and isPaidMember !='' ">
            and p.is_paid_member = #{isPaidMember}
        </if>
        <if test="productType != null">
            and p.type = #{productType}
        </if>
        ORDER BY p.rank desc, p.id desc
    </select>

    <select id="getPlatformPageCount" resultType="java.lang.Integer" parameterType="Map">
        SELECT COUNT(*)
        FROM eb_product AS p
        right join eb_merchant AS m on p.mer_id = m.id
        where p.is_del = 0 and p.is_recycle = 0 and p.marketing_type = 0
        <choose>
            <when test='type == "1"'>
                and p.is_show = 1
                and p.audit_status in (0,2)
            </when>
            <when test='type == "2"'>
                and p.is_show = 0
                and p.is_audit = 0
                and p.audit_status in (0,2)
            </when>
            <when test='type == "6"'>
                and p.audit_status = 1
                and p.is_audit = 1
            </when>
            <when test='type == "7"'>
                and p.audit_status = 3
                and p.is_audit = 0
            </when>
        </choose>
        <if test="merId != null and merId !='' ">
            and p.mer_id = #{merId}
        </if>
        <if test="self != null">
            and m.is_self = #{self}
        </if>
        <if test="categoryIds != null and categoryIds !='' ">
            and find_in_set(p.category_id, #{categoryIds, jdbcType=VARCHAR})
        </if>
        <if test="keywords != null and keywords !='' ">
            and (p.name like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%')
            or p.keyword like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="isPaidMember != null">
            and p.is_paid_member = #{isPaidMember}
        </if>
        <if test="productType != null">
            and p.type = #{productType}
        </if>
    </select>

    <select id="findH5List" resultType="com.zbkj.common.response.ProductFrontResponse" parameterType="Map">
        SELECT p.id,p.image,p.name as name,p.price,p.ot_price as otPrice,p.price,p.sales,p.stock,p.ficti,
        p.unit_name as unitName,p.reason,p.mer_id as merId,m.name as merName,m.category_id as merCategoryId,m.type_id as merTypeId,
        p.category_id as categoryId,p.brand_id as brandId,p.vip_price as vipPrice,p.is_paid_member as isPaidMember
        FROM eb_product AS p
        right join eb_merchant AS m on p.mer_id = m.id
        where p.is_del = 0 and p.is_recycle = 0 and p.is_show = 1
        and p.audit_status in (0,2) and p.marketing_type = 0
        <if test="id != null">
            and find_in_set(p.id, #{id})
        </if>
        <if test="categoryId != null">
            and find_in_set(p.category_id, #{categoryId})
        </if>
        <if test="brandId != null">
            and find_in_set(p.brand_id, #{brandId})
        </if>

        <if test="minPrice != null">
            and p.price &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            and p.price &lt;= #{maxPrice}
        </if>
        <if test="keywords != null and keywords !='' ">
            and (p.name like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%')
            or p.keyword like CONCAT('%', #{keywords, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="isSelf != null">
            and m.is_self = #{isSelf}
        </if>
        <if test="merId != null">
            and find_in_set(p.mer_id, #{merId})
        </if>
        ${lastStr}
    </select>

    <select id="getActivitySearchPage" resultType="com.zbkj.common.response.ProductActivityResponse" parameterType="Map">
        SELECT p.id,p.image,p.name,p.price,p.stock,p.is_show as isShow,p.spec_type as specType,
        m.name as merName,m.star_level as merStarLevel,p.brand_id as brandId,
        pc.name as categoryName, p.category_id as categoryId,
        (p.sales + p.ficti) as sales,p.unit_name as unitName,p.type
        FROM eb_product AS p
        left join eb_merchant AS m on p.mer_id = m.id
        left join eb_product_category AS pc on p.category_id = pc.id
        where p.is_del = 0 and p.is_recycle = 0
        and p.audit_status in (0,2)
        and p.type in (0,2,5,6) and p.marketing_type = 0
        <if test="categoryId != null and categoryId !='' ">
            and find_in_set(p.category_id, #{categoryId})
        </if>
        <if test="isShow != null">
            and p.is_show = #{isShow}
        </if>
        <if test="name != null and name !='' ">
            and p.name like CONCAT('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="merIds != null and merIds !='' ">
            and find_in_set(p.mer_id, #{merIds})
        </if>
        <if test="merStars != null">
            and m.star_level &gt;= #{merStars}
        </if>
        <if test="brandId != null and brandId !='' ">
            and find_in_set(p.brand_id, #{brandId})
        </if>
        ${lastStr}
    </select>

    <select id="getActivitySearchPageByMerchant" resultType="com.zbkj.common.response.ProductActivityResponse" parameterType="Map">
        SELECT p.id,p.image,p.name,p.price,p.stock,p.is_show as isShow,p.spec_type as specType,p.type,
               p.marketing_type as marketingType,(p.sales + p.ficti) as sales,p.unit_name as unitName,
               m.name as merName,m.star_level as merStarLevel,
               pc.name as categoryName
        FROM eb_product AS p
        left join eb_merchant AS m on p.mer_id = m.id
        left join eb_product_category AS pc on p.category_id = pc.id
        where p.is_del = 0 and p.is_recycle = 0
        and p.audit_status in (0,2)
        and p.type in (0,2,5,6) and p.marketing_type = 0 and p.mer_id = #{merId}
        <if test="categoryId != null and categoryId != ''">
            and p.category_id = #{categoryId}
        </if>
        <if test="cateIdSql != null and cateIdSql != ''">
            and ${cateIdSql}
        </if>
        <if test="isShow != null">
            and p.is_show = #{isShow}
        </if>
        <if test="name != null and name !='' ">
            and p.name like CONCAT('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="productId != null and productId != ''">
            and p.id = #{productId}
        </if>
        <if test="proCateIds != null and proCateIds !='' ">
            and p.category_id in (SELECT id FROM `eb_product_category` where `level` = 3 and is_del = 0 and pid in (SELECT id FROM `eb_product_category` WHERE `level` = 2 and is_del = 0 and FIND_IN_SET(pid, #{proCateIds})))
        </if>
        ${lastStr}
    </select>

    <select id="findProductBrandIdByKeyword" resultType="java.lang.Integer">
        select brand_id
        FROM eb_product
        where is_del = 0
          and is_recycle = 0
          and is_show = 1
          and audit_status in (0, 2)
          and (name like CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            or keyword like CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
          and marketing_type = 0
    </select>

    <select id="findProductCategoryIdByKeyword" resultType="java.lang.Integer">
        select category_id
        FROM eb_product
        where is_del = 0
          and is_recycle = 0
          and is_show = 1
          and audit_status in (0, 2)
          and (name like CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            or keyword like CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
          and marketing_type = 0
    </select>

    <select id="getMarketingSearchPage" resultType="com.zbkj.common.response.ProductMarketingResponse" parameterType="Map">
        SELECT p.id,p.image,p.name,p.is_show as isShow,p.category_id as categoryId,p.type,
               p.price,(p.sales + p.ficti) as sales,p.unit_name,p.stock,
               pc.name as categoryName
        FROM eb_product AS p
        left join eb_product_category AS pc on p.category_id = pc.id
        where p.is_del = 0 and p.is_recycle = 0
        and p.audit_status in (0,2)
        and p.type in (0,2,5,6) and p.marketing_type = 0
        <if test="categoryId != null and categoryId !='' ">
            and find_in_set(p.category_id, #{categoryId})
        </if>
        <if test="isShow != null">
            and p.is_show = #{isShow}
        </if>
        <if test="name != null and name !='' ">
            and p.name like CONCAT('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="merIds != null and merIds !='' ">
            and find_in_set(p.mer_id, #{merIds})
        </if>
        order by p.id desc
    </select>

    <select id="getMarketingSearchPageByMerchant" resultType="com.zbkj.common.response.ProductMarketingResponse" parameterType="Map">
        SELECT p.id,p.image,p.name,p.is_show as isShow,p.category_id as categoryId,p.type,
        p.price,(p.sales + p.ficti) as sales,p.unit_name,p.stock,
        pc.name as categoryName
        FROM eb_product AS p
        left join eb_merchant AS m on p.mer_id = m.id
        left join eb_product_category AS pc on p.category_id = pc.id
        where p.is_del = 0 and p.is_recycle = 0
        and p.audit_status in (0,2)
        and p.type in (0,2,5,6) and p.marketing_type = 0
        <if test="categoryId != null">
            and p.category_id = #{categoryId}
        </if>
        <if test="cateId != null">
            and find_in_set(#{cateId}, p.cate_id)
        </if>
        <if test="isShow != null">
            and p.is_show = #{isShow}
        </if>
        <if test="name != null and name !='' ">
            and p.name like CONCAT('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="merId != null">
            and p.mer_id = #{merId}
        </if>
        <if test="productId != null">
            and p.id = #{productId}
        </if>
        <if test="proCateIds != null and proCateIds !='' ">
            and p.category_id in (SELECT id FROM `eb_product_category` where `level` = 3 and is_del = 0 and pid in (SELECT id FROM `eb_product_category` WHERE `level` = 2 and is_del = 0 and FIND_IN_SET(pid, #{proCateIds})))
        </if>
    </select>
</mapper>
